import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  ExpandLess,
  ExpandMore,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const drawerWidth = 280;

const DashboardLayout = ({ 
  children, 
  menuItems = [], 
  title = "MarketMind",
  userMenuItems = [],
  onThemeToggle,
  isDarkMode = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [openMenus, setOpenMenus] = useState({});

  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate('/auth/login');
  };

  const handleMenuToggle = (menuId) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const isActive = (path) => location.pathname === path;

  const getAccountTypeInfo = (accountType) => {
    switch (accountType) {
      case 'personal':
        return { label: 'شخصي', color: 'primary' };
      case 'business_user':
        return { label: 'مستخدم شركة', color: 'secondary' };
      case 'business_owner':
        return { label: 'مالك شركة', color: 'success' };
      case 'admin':
        return { label: 'مدير', color: 'error' };
      case 'owner':
        return { label: 'مالك النظام', color: 'warning' };
      default:
        return { label: 'مستخدم', color: 'default' };
    }
  };

  const renderMenuItem = (item) => {
    const hasChildren = item.children && item.children.length > 0;
    const isMenuOpen = openMenus[item.id];

    if (hasChildren) {
      return (
        <Box key={item.id}>
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => handleMenuToggle(item.id)}
              sx={{
                borderRadius: 1,
                mb: 0.5,
                mx: 1,
                '&:hover': { bgcolor: 'action.hover' }
              }}
            >
              <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.title} />
              {isMenuOpen ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>
          </ListItem>
          <Collapse in={isMenuOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children.map((child) => (
                <ListItem key={child.path} disablePadding>
                  <ListItemButton
                    onClick={() => navigate(child.path)}
                    sx={{
                      pl: 4,
                      borderRadius: 1,
                      mb: 0.5,
                      mx: 1,
                      bgcolor: isActive(child.path) ? 'primary.light' : 'transparent',
                      color: isActive(child.path) ? 'primary.contrastText' : 'inherit',
                      '&:hover': { 
                        bgcolor: isActive(child.path) ? 'primary.main' : 'action.hover' 
                      }
                    }}
                  >
                    <ListItemIcon sx={{ 
                      color: isActive(child.path) ? 'primary.contrastText' : 'text.secondary',
                      minWidth: 36
                    }}>
                      {child.icon}
                    </ListItemIcon>
                    <ListItemText 
                      primary={child.title}
                      primaryTypographyProps={{
                        fontSize: '0.875rem'
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Collapse>
        </Box>
      );
    }

    return (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          onClick={() => navigate(item.path)}
          sx={{
            borderRadius: 1,
            mb: 0.5,
            mx: 1,
            bgcolor: isActive(item.path) ? 'primary.light' : 'transparent',
            color: isActive(item.path) ? 'primary.contrastText' : 'inherit',
            '&:hover': { 
              bgcolor: isActive(item.path) ? 'primary.main' : 'action.hover' 
            }
          }}
        >
          <ListItemIcon sx={{ 
            color: isActive(item.path) ? 'primary.contrastText' : 'primary.main',
            minWidth: 40
          }}>
            {item.icon}
          </ListItemIcon>
          <ListItemText primary={item.title} />
        </ListItemButton>
      </ListItem>
    );
  };

  const drawer = (
    <Box>
      {/* شعار التطبيق */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="h5" fontWeight="bold" color="primary.main">
          {title}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          منصة التسويق الذكي
        </Typography>
      </Box>
      
      <Divider />
      
      {/* معلومات المستخدم */}
      {currentUser && (
        <Box sx={{ p: 2 }}>
          <Box display="flex" alignItems="center" mb={1}>
            <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
              {currentUser.first_name?.charAt(0) || currentUser.email?.charAt(0)}
            </Avatar>
            <Box flex={1}>
              <Typography variant="body2" fontWeight="bold">
                {currentUser.first_name} {currentUser.last_name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {currentUser.email}
              </Typography>
            </Box>
          </Box>
          <Chip 
            label={getAccountTypeInfo(currentUser.account_type).label}
            color={getAccountTypeInfo(currentUser.account_type).color}
            size="small"
            sx={{ mt: 1 }}
          />
          {currentUser.company_name && (
            <Typography variant="caption" display="block" color="text.secondary" mt={0.5}>
              {currentUser.company_name}
            </Typography>
          )}
        </Box>
      )}
      
      <Divider />
      
      {/* قائمة التنقل */}
      <List sx={{ px: 1, py: 1 }}>
        {menuItems.map(renderMenuItem)}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'row-reverse' }}>
      {/* الشريط الجانبي */}
      <Box
        component="nav"
        sx={{
          width: { lg: drawerWidth },
          flexShrink: { lg: 0 },
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          anchor="right"
          sx={{
            display: { xs: 'block', lg: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              zIndex: (theme) => theme.zIndex.drawer + 2
            }
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', lg: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              position: 'fixed',
              height: '100vh',
              top: 0,
              right: 0,
              zIndex: (theme) => theme.zIndex.drawer
            }
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* المحتوى الرئيسي مع شريط التطبيق */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          width: { lg: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh'
        }}
      >
        {/* شريط التطبيق العلوي */}
        <AppBar
          position="fixed"
          sx={{
            width: { lg: `calc(100% - ${drawerWidth}px)` },
            bgcolor: 'background.paper',
            color: 'text.primary',
            boxShadow: 1,
            zIndex: (theme) => theme.zIndex.appBar
          }}
        >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {/* يمكن تخصيص العنوان حسب الصفحة الحالية */}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* تبديل الوضع المظلم */}
            {onThemeToggle && (
              <Tooltip title={isDarkMode ? 'الوضع الفاتح' : 'الوضع المظلم'}>
                <IconButton color="inherit" onClick={onThemeToggle}>
                  {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
                </IconButton>
              </Tooltip>
            )}

            {/* الإشعارات */}
            <Tooltip title="الإشعارات">
              <IconButton color="inherit">
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            {/* قائمة المستخدم */}
            <Tooltip title="الملف الشخصي">
              <IconButton
                onClick={handleProfileMenuOpen}
                sx={{ p: 0 }}
              >
                <Avatar sx={{ width: 32, height: 32 }}>
                  {currentUser?.first_name?.charAt(0) || 'م'}
                </Avatar>
              </IconButton>
            </Tooltip>

            {/* زر القائمة للجوال */}
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="end"
              onClick={handleDrawerToggle}
              sx={{ ml: 2, display: { lg: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { lg: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          minHeight: 'calc(100vh - 64px)'
        }}
      >
        {children}
      </Box>

      {/* الشريط الجانبي */}
      <Box
        component="nav"
        sx={{ width: { lg: drawerWidth }, flexShrink: { lg: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          anchor="right"
          sx={{
            display: { xs: 'block', lg: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', lg: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* قائمة المستخدم */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => navigate('/profile')}>
          <ListItemIcon>
            <AccountIcon fontSize="small" />
          </ListItemIcon>
          الملف الشخصي
        </MenuItem>
        <MenuItem onClick={() => navigate('/settings')}>
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          الإعدادات
        </MenuItem>
        {userMenuItems.map((item, index) => (
          <MenuItem key={index} onClick={item.onClick}>
            <ListItemIcon>
              {item.icon}
            </ListItemIcon>
            {item.label}
          </MenuItem>
        ))}
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          تسجيل الخروج
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DashboardLayout;
