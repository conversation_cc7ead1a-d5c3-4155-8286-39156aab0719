{"name": "@marketmind/shared", "version": "1.0.0", "description": "Shared components and utilities for MarketMind platform", "main": "index.js", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@mui/x-data-grid": "^6.10.0", "@mui/x-date-pickers": "^6.10.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-router-dom": "^6.14.0", "recharts": "^2.7.0", "yup": "^1.2.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "scripts": {"build": "echo 'Shared components built'", "test": "echo 'No tests specified'"}}