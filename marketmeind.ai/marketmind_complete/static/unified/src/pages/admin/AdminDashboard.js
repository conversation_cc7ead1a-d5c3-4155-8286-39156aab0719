import React from 'react';
import { Routes, Route } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import DashboardLayout from '../../shared/components/layout/DashboardLayout';
import AdminHome from './AdminHome';
import UserManagement from './UserManagement';
import CompanyManagement from './CompanyManagement';
import ComingSoon from '../../shared/components/common/ComingSoon';

const AdminDashboard = ({ onThemeToggle, isDarkMode }) => {
  const menuItems = [
    {
      id: 'dashboard',
      title: 'لوحة التحكم',
      icon: <DashboardIcon />,
      path: '/dashboard/admin'
    },
    {
      id: 'users',
      title: 'إدارة المستخدمين',
      icon: <PeopleIcon />,
      path: '/dashboard/admin/users'
    },
    {
      id: 'companies',
      title: 'إدارة الشركات',
      icon: <BusinessIcon />,
      path: '/dashboard/admin/companies'
    },
    {
      id: 'analytics',
      title: 'تحليلات النظام',
      icon: <AnalyticsIcon />,
      path: '/dashboard/admin/analytics'
    },
    {
      id: 'settings',
      title: 'إعدادات النظام',
      icon: <SettingsIcon />,
      path: '/dashboard/admin/settings'
    }
  ];

  return (
    <DashboardLayout
      title="MarketMind Admin"
      menuItems={menuItems}
      onThemeToggle={onThemeToggle}
      isDarkMode={isDarkMode}
    >
      <Routes>
        <Route path="/" element={<AdminHome />} />
        <Route path="/users" element={<UserManagement />} />
        <Route path="/companies" element={<CompanyManagement />} />
        <Route path="/analytics" element={
          <ComingSoon
            title="تحليلات النظام"
            description="تحليلات شاملة لأداء النظام والإحصائيات المتقدمة"
          />
        } />
        <Route path="/settings" element={
          <ComingSoon
            title="إعدادات النظام"
            description="إعدادات متقدمة للتحكم في جميع جوانب النظام"
          />
        } />
      </Routes>
    </DashboardLayout>
  );
};

export default AdminDashboard;
