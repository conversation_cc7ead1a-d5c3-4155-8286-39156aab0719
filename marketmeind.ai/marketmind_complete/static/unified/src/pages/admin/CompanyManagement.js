import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  Card,
  CardContent
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as ActivateIcon,
  Email as EmailIcon,
  Download as DownloadIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  Campaign as CampaignIcon,
  AttachMoney as MoneyIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { 
  PageContainer, 
  PageSection,
  UnifiedCard, 
  UnifiedCardContent,
  StatsCard,
  StatsGrid
} from '../../shared/components/ui';

const CompanyManagement = () => {
  const [companies, setCompanies] = useState([]);
  const [filteredCompanies, setFilteredCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSize, setFilterSize] = useState('all');

  // بيانات تجريبية للشركات
  const sampleCompanies = [
    {
      id: 1,
      name: 'شركة التقنية المتقدمة',
      industry: 'تكنولوجيا',
      size: 'large',
      status: 'active',
      ownerId: 2,
      ownerName: 'فاطمة علي',
      ownerEmail: '<EMAIL>',
      employees: 150,
      activeCampaigns: 12,
      totalSpent: 45000,
      joinDate: '2023-03-10',
      lastActivity: '2024-01-22',
      subscription: 'premium',
      location: 'الرياض'
    },
    {
      id: 2,
      name: 'مؤسسة الابتكار',
      industry: 'استشارات',
      size: 'medium',
      status: 'active',
      ownerId: 4,
      ownerName: 'سارة أحمد',
      ownerEmail: '<EMAIL>',
      employees: 45,
      activeCampaigns: 8,
      totalSpent: 22000,
      joinDate: '2023-06-15',
      lastActivity: '2024-01-21',
      subscription: 'standard',
      location: 'جدة'
    },
    {
      id: 3,
      name: 'شركة التسويق الرقمي',
      industry: 'تسويق',
      size: 'small',
      status: 'pending',
      ownerId: 6,
      ownerName: 'محمد خالد',
      ownerEmail: '<EMAIL>',
      employees: 15,
      activeCampaigns: 0,
      totalSpent: 0,
      joinDate: '2024-01-20',
      lastActivity: null,
      subscription: 'basic',
      location: 'الدمام'
    },
    {
      id: 4,
      name: 'مجموعة الأعمال المتكاملة',
      industry: 'متنوع',
      size: 'large',
      status: 'suspended',
      ownerId: 8,
      ownerName: 'أحمد السعيد',
      ownerEmail: '<EMAIL>',
      employees: 300,
      activeCampaigns: 0,
      totalSpent: 78000,
      joinDate: '2023-01-15',
      lastActivity: '2023-12-10',
      subscription: 'enterprise',
      location: 'الرياض'
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setCompanies(sampleCompanies);
      setFilteredCompanies(sampleCompanies);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // تطبيق الفلاتر والبحث
    let filtered = companies;

    if (searchTerm) {
      filtered = filtered.filter(company =>
        company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.ownerName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(company => company.status === filterStatus);
    }

    if (filterSize !== 'all') {
      filtered = filtered.filter(company => company.size === filterSize);
    }

    setFilteredCompanies(filtered);
    setPage(0);
  }, [searchTerm, filterStatus, filterSize, companies]);

  const handleMenuOpen = (event, company) => {
    setAnchorEl(event.currentTarget);
    setSelectedCompany(company);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCompany(null);
  };

  const handleStatusChange = (companyId, newStatus) => {
    setCompanies(prev => 
      prev.map(company => 
        company.id === companyId 
          ? { ...company, status: newStatus }
          : company
      )
    );
    handleMenuClose();
  };

  const handleDeleteCompany = () => {
    setCompanies(prev => 
      prev.filter(company => company.id !== selectedCompany.id)
    );
    setDeleteDialogOpen(false);
    handleMenuClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'suspended': return 'error';
      case 'inactive': return 'default';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشطة';
      case 'pending': return 'في الانتظار';
      case 'suspended': return 'موقوفة';
      case 'inactive': return 'غير نشطة';
      default: return status;
    }
  };

  const getSizeText = (size) => {
    switch (size) {
      case 'small': return 'صغيرة';
      case 'medium': return 'متوسطة';
      case 'large': return 'كبيرة';
      default: return size;
    }
  };

  const getSizeColor = (size) => {
    switch (size) {
      case 'small': return 'info';
      case 'medium': return 'warning';
      case 'large': return 'success';
      default: return 'default';
    }
  };

  const getSubscriptionText = (subscription) => {
    switch (subscription) {
      case 'basic': return 'أساسي';
      case 'standard': return 'قياسي';
      case 'premium': return 'مميز';
      case 'enterprise': return 'مؤسسي';
      default: return subscription;
    }
  };

  // حساب الإحصائيات
  const stats = {
    totalCompanies: companies.length,
    activeCompanies: companies.filter(c => c.status === 'active').length,
    pendingCompanies: companies.filter(c => c.status === 'pending').length,
    totalRevenue: companies.reduce((sum, c) => sum + c.totalSpent, 0)
  };

  return (
    <PageContainer
      title="إدارة الشركات"
      subtitle="إدارة ومراقبة جميع الشركات المسجلة في النظام"
      breadcrumbs={[
        { label: 'لوحة التحكم', href: '/dashboard/admin' },
        { label: 'إدارة الشركات' }
      ]}
      actions={[
        <Button
          key="export"
          variant="outlined"
          startIcon={<DownloadIcon />}
        >
          تصدير البيانات
        </Button>
      ]}
    >
      {/* الإحصائيات */}
      <PageSection title="نظرة عامة">
        <StatsGrid>
          <StatsCard
            title="إجمالي الشركات"
            value={stats.totalCompanies}
            change={`${stats.activeCompanies} نشطة`}
            changeType="positive"
            icon={<BusinessIcon />}
            color="primary"
          />
          <StatsCard
            title="الشركات النشطة"
            value={stats.activeCompanies}
            change={`${((stats.activeCompanies / stats.totalCompanies) * 100).toFixed(0)}% من الإجمالي`}
            changeType="positive"
            icon={<ActivateIcon />}
            color="success"
          />
          <StatsCard
            title="في انتظار الموافقة"
            value={stats.pendingCompanies}
            change="يحتاج مراجعة"
            changeType="warning"
            icon={<FilterIcon />}
            color="warning"
          />
          <StatsCard
            title="إجمالي الإيرادات"
            value={`${stats.totalRevenue.toLocaleString()} ر.س`}
            change="من جميع الشركات"
            changeType="positive"
            icon={<MoneyIcon />}
            color="secondary"
          />
        </StatsGrid>
      </PageSection>

      {/* أدوات البحث والفلترة */}
      <PageSection title="البحث والفلترة">
        <UnifiedCard>
          <UnifiedCardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="البحث عن شركة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    label="الحالة"
                  >
                    <MenuItem value="all">جميع الحالات</MenuItem>
                    <MenuItem value="active">نشطة</MenuItem>
                    <MenuItem value="pending">في الانتظار</MenuItem>
                    <MenuItem value="suspended">موقوفة</MenuItem>
                    <MenuItem value="inactive">غير نشطة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>حجم الشركة</InputLabel>
                  <Select
                    value={filterSize}
                    onChange={(e) => setFilterSize(e.target.value)}
                    label="حجم الشركة"
                  >
                    <MenuItem value="all">جميع الأحجام</MenuItem>
                    <MenuItem value="small">صغيرة</MenuItem>
                    <MenuItem value="medium">متوسطة</MenuItem>
                    <MenuItem value="large">كبيرة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  variant="outlined"
                  startIcon={<FilterIcon />}
                  fullWidth
                >
                  فلاتر متقدمة
                </Button>
              </Grid>
            </Grid>
          </UnifiedCardContent>
        </UnifiedCard>
      </PageSection>

      {/* جدول الشركات */}
      <PageSection title="قائمة الشركات">
        <UnifiedCard>
          <UnifiedCardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>الشركة</TableCell>
                    <TableCell>المالك</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>الحجم</TableCell>
                    <TableCell>الموظفون</TableCell>
                    <TableCell>الحملات</TableCell>
                    <TableCell>الإنفاق</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredCompanies
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((company) => (
                      <TableRow key={company.id} hover>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar>
                              <BusinessIcon />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {company.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {company.industry} • {company.location}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {company.ownerName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {company.ownerEmail}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusText(company.status)}
                            color={getStatusColor(company.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getSizeText(company.size)}
                            color={getSizeColor(company.size)}
                            variant="outlined"
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{company.employees}</TableCell>
                        <TableCell>{company.activeCampaigns}</TableCell>
                        <TableCell>{company.totalSpent.toLocaleString()} ر.س</TableCell>
                        <TableCell>
                          <IconButton onClick={(e) => handleMenuOpen(e, company)}>
                            <MoreIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={filteredCompanies.length}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
              labelRowsPerPage="عدد الصفوف في الصفحة:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
            />
          </UnifiedCardContent>
        </UnifiedCard>
      </PageSection>

      {/* قائمة الإجراءات */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => setDetailsDialogOpen(true)}>
          <ViewIcon sx={{ mr: 1 }} />
          عرض التفاصيل
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <EditIcon sx={{ mr: 1 }} />
          تعديل
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <EmailIcon sx={{ mr: 1 }} />
          إرسال رسالة
        </MenuItem>
        {selectedCompany?.status === 'active' ? (
          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'suspended')}>
            <BlockIcon sx={{ mr: 1 }} />
            إيقاف الشركة
          </MenuItem>
        ) : (
          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'active')}>
            <ActivateIcon sx={{ mr: 1 }} />
            تفعيل الشركة
          </MenuItem>
        )}
        {selectedCompany?.status === 'pending' && (
          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'active')}>
            <ActivateIcon sx={{ mr: 1 }} />
            الموافقة على الشركة
          </MenuItem>
        )}
        <MenuItem 
          onClick={() => setDeleteDialogOpen(true)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          حذف
        </MenuItem>
      </Menu>

      {/* حوار تفاصيل الشركة */}
      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>تفاصيل الشركة</DialogTitle>
        <DialogContent>
          {selectedCompany && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      معلومات أساسية
                    </Typography>
                    <Typography><strong>الاسم:</strong> {selectedCompany.name}</Typography>
                    <Typography><strong>الصناعة:</strong> {selectedCompany.industry}</Typography>
                    <Typography><strong>الموقع:</strong> {selectedCompany.location}</Typography>
                    <Typography><strong>الحجم:</strong> {getSizeText(selectedCompany.size)}</Typography>
                    <Typography><strong>عدد الموظفين:</strong> {selectedCompany.employees}</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      الأداء والنشاط
                    </Typography>
                    <Typography><strong>الحملات النشطة:</strong> {selectedCompany.activeCampaigns}</Typography>
                    <Typography><strong>إجمالي الإنفاق:</strong> {selectedCompany.totalSpent.toLocaleString()} ر.س</Typography>
                    <Typography><strong>نوع الاشتراك:</strong> {getSubscriptionText(selectedCompany.subscription)}</Typography>
                    <Typography><strong>تاريخ الانضمام:</strong> {selectedCompany.joinDate}</Typography>
                    <Typography><strong>آخر نشاط:</strong> {selectedCompany.lastActivity || 'لا يوجد'}</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>

      {/* حوار تأكيد الحذف */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>تأكيد الحذف</DialogTitle>
        <DialogContent>
          هل أنت متأكد من حذف الشركة "{selectedCompany?.name}"؟ 
          هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بياناتها وحملاتها.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            إلغاء
          </Button>
          <Button onClick={handleDeleteCompany} color="error" variant="contained">
            حذف
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default CompanyManagement;
