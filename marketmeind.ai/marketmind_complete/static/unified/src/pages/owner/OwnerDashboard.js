import React from 'react';
import { Routes, Route } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  SupervisorAccount as OwnerIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import DashboardLayout from '../../shared/components/layout/DashboardLayout';
import OwnerHome from './OwnerHome';
import SystemOverview from './SystemOverview';
import ComingSoon from '../../shared/components/common/ComingSoon';

const OwnerDashboard = ({ onThemeToggle, isDarkMode }) => {
  const menuItems = [
    {
      id: 'dashboard',
      title: 'لوحة التحكم',
      icon: <DashboardIcon />,
      path: '/dashboard/owner'
    },
    {
      id: 'system-overview',
      title: 'نظرة عامة على النظام',
      icon: <AnalyticsIcon />,
      path: '/dashboard/owner/system-overview'
    },
    {
      id: 'security',
      title: 'الأمان والحماية',
      icon: <SecurityIcon />,
      path: '/dashboard/owner/security'
    },
    {
      id: 'advanced-settings',
      title: 'الإعدادات المتقدمة',
      icon: <SettingsIcon />,
      path: '/dashboard/owner/advanced-settings'
    }
  ];

  return (
    <DashboardLayout
      title="MarketMind Owner"
      menuItems={menuItems}
      onThemeToggle={onThemeToggle}
      isDarkMode={isDarkMode}
    >
      <Routes>
        <Route path="/" element={<OwnerHome />} />
        <Route path="/system-overview" element={<SystemOverview />} />
        <Route path="/security" element={
          <ComingSoon
            title="الأمان والحماية"
            description="إدارة أمان النظام والحماية المتقدمة ضد التهديدات"
          />
        } />
        <Route path="/advanced-settings" element={
          <ComingSoon
            title="الإعدادات المتقدمة"
            description="تكوين متقدم لجميع جوانب النظام والمنصة"
          />
        } />
      </Routes>
    </DashboardLayout>
  );
};

export default OwnerDashboard;
