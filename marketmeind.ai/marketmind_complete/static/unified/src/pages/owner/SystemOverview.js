import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  AttachMoney as MoneyIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { 
  PageContainer, 
  PageSection,
  UnifiedCard, 
  UnifiedCardContent,
  StatsCard,
  StatsGrid
} from '../../shared/components/ui';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, AreaC<PERSON>, Area, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

const SystemOverview = () => {
  const [loading, setLoading] = useState(false);
  const [systemHealth, setSystemHealth] = useState('excellent');

  // بيانات تجريبية للنظرة العامة
  const systemStats = {
    totalUsers: 1247,
    activeUsers: 892,
    totalCompanies: 156,
    activeCompanies: 134,
    totalRevenue: 2450000,
    monthlyGrowth: 15.8,
    systemUptime: 99.97,
    serverLoad: 23,
    storageUsed: 67,
    dailyActiveUsers: 456
  };

  // بيانات الأداء عبر الوقت
  const performanceData = [
    { date: '2024-01-15', users: 1180, companies: 145, revenue: 2200000 },
    { date: '2024-01-16', users: 1195, companies: 147, revenue: 2250000 },
    { date: '2024-01-17', users: 1210, companies: 149, revenue: 2300000 },
    { date: '2024-01-18', users: 1225, companies: 152, revenue: 2350000 },
    { date: '2024-01-19', users: 1235, companies: 154, revenue: 2400000 },
    { date: '2024-01-20', users: 1240, companies: 155, revenue: 2420000 },
    { date: '2024-01-21', users: 1247, companies: 156, revenue: 2450000 }
  ];

  // بيانات توزيع المستخدمين
  const userDistribution = [
    { name: 'حسابات شخصية', value: 65, color: '#8884d8' },
    { name: 'مستخدمو شركات', value: 25, color: '#82ca9d' },
    { name: 'مالكو شركات', value: 8, color: '#ffc658' },
    { name: 'مديرون', value: 2, color: '#ff7300' }
  ];

  // بيانات الأداء الفني
  const technicalMetrics = [
    { name: 'وقت الاستجابة', value: 120, unit: 'ms', status: 'good' },
    { name: 'استخدام المعالج', value: 23, unit: '%', status: 'excellent' },
    { name: 'استخدام الذاكرة', value: 45, unit: '%', status: 'good' },
    { name: 'استخدام التخزين', value: 67, unit: '%', status: 'warning' },
    { name: 'عدد الطلبات/ثانية', value: 1250, unit: 'req/s', status: 'excellent' },
    { name: 'معدل الأخطاء', value: 0.02, unit: '%', status: 'excellent' }
  ];

  // أحداث النظام الأخيرة
  const recentEvents = [
    {
      id: 1,
      type: 'info',
      message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.5',
      timestamp: '2024-01-22 14:30',
      severity: 'info'
    },
    {
      id: 2,
      type: 'success',
      message: 'تم إضافة 15 شركة جديدة اليوم',
      timestamp: '2024-01-22 12:15',
      severity: 'success'
    },
    {
      id: 3,
      type: 'warning',
      message: 'استخدام التخزين وصل إلى 67% - يُنصح بالتوسع',
      timestamp: '2024-01-22 10:45',
      severity: 'warning'
    },
    {
      id: 4,
      type: 'info',
      message: 'تم إجراء نسخ احتياطي تلقائي للبيانات',
      timestamp: '2024-01-22 03:00',
      severity: 'info'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'excellent': return <CheckIcon />;
      case 'good': return <InfoIcon />;
      case 'warning': return <WarningIcon />;
      case 'critical': return <ErrorIcon />;
      default: return <InfoIcon />;
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'success': return <CheckIcon color="success" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'error': return <ErrorIcon color="error" />;
      default: return <InfoIcon color="info" />;
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <PageContainer
      title="نظرة عامة على النظام"
      subtitle="مراقبة شاملة لأداء النظام والإحصائيات الرئيسية"
      breadcrumbs={[
        { label: 'لوحة التحكم', href: '/dashboard/owner' },
        { label: 'نظرة عامة على النظام' }
      ]}
      actions={[
        <Tooltip key="refresh" title="تحديث البيانات">
          <IconButton onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>,
        <Button key="export" variant="outlined" startIcon={<DownloadIcon />}>
          تصدير التقرير
        </Button>
      ]}
    >
      {/* حالة النظام العامة */}
      <PageSection title="حالة النظام">
        <Alert 
          severity={systemHealth === 'excellent' ? 'success' : 'warning'} 
          sx={{ mb: 3 }}
        >
          <Typography variant="body1">
            <strong>حالة النظام: ممتازة</strong> - جميع الخدمات تعمل بشكل طبيعي
          </Typography>
          <Typography variant="body2">
            وقت التشغيل: {systemStats.systemUptime}% | آخر تحديث: منذ دقيقتين
          </Typography>
        </Alert>
      </PageSection>

      {/* الإحصائيات الرئيسية */}
      <PageSection title="الإحصائيات الرئيسية">
        <StatsGrid>
          <StatsCard
            title="إجمالي المستخدمين"
            value={systemStats.totalUsers.toLocaleString()}
            change={`${systemStats.activeUsers} نشط`}
            changeType="positive"
            icon={<PeopleIcon />}
            color="primary"
          />
          <StatsCard
            title="إجمالي الشركات"
            value={systemStats.totalCompanies}
            change={`${systemStats.activeCompanies} نشطة`}
            changeType="positive"
            icon={<BusinessIcon />}
            color="secondary"
          />
          <StatsCard
            title="إجمالي الإيرادات"
            value={`${(systemStats.totalRevenue / 1000000).toFixed(1)}م ر.س`}
            change={`+${systemStats.monthlyGrowth}% هذا الشهر`}
            changeType="positive"
            icon={<MoneyIcon />}
            color="success"
          />
          <StatsCard
            title="وقت التشغيل"
            value={`${systemStats.systemUptime}%`}
            change="متاح باستمرار"
            changeType="positive"
            icon={<SecurityIcon />}
            color="warning"
          />
        </StatsGrid>
      </PageSection>

      <Grid container spacing={3}>
        {/* الأداء عبر الوقت */}
        <Grid item xs={12} lg={8}>
          <PageSection title="نمو النظام عبر الوقت">
            <UnifiedCard>
              <UnifiedCardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip />
                    <Area type="monotone" dataKey="users" stackId="1" stroke="#8884d8" fill="#8884d8" name="المستخدمون" />
                    <Area type="monotone" dataKey="companies" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="الشركات" />
                  </AreaChart>
                </ResponsiveContainer>
              </UnifiedCardContent>
            </UnifiedCard>
          </PageSection>
        </Grid>

        {/* توزيع المستخدمين */}
        <Grid item xs={12} lg={4}>
          <PageSection title="توزيع المستخدمين">
            <UnifiedCard>
              <UnifiedCardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={userDistribution}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${value}%`}
                    >
                      {userDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
                <Box mt={2}>
                  {userDistribution.map((item, index) => (
                    <Box key={index} display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Box 
                          width={12} 
                          height={12} 
                          bgcolor={item.color} 
                          borderRadius="50%" 
                        />
                        <Typography variant="body2">{item.name}</Typography>
                      </Box>
                      <Typography variant="body2" fontWeight="bold">
                        {item.value}%
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </UnifiedCardContent>
            </UnifiedCard>
          </PageSection>
        </Grid>

        {/* المقاييس الفنية */}
        <Grid item xs={12} lg={6}>
          <PageSection title="الأداء الفني">
            <UnifiedCard>
              <UnifiedCardContent>
                <Grid container spacing={2}>
                  {technicalMetrics.map((metric, index) => (
                    <Grid item xs={12} key={index}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2">{metric.name}</Typography>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontWeight="bold">
                            {metric.value} {metric.unit}
                          </Typography>
                          <Chip
                            icon={getStatusIcon(metric.status)}
                            label={metric.status === 'excellent' ? 'ممتاز' : 
                                   metric.status === 'good' ? 'جيد' : 
                                   metric.status === 'warning' ? 'تحذير' : 'حرج'}
                            color={getStatusColor(metric.status)}
                            size="small"
                          />
                        </Box>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={metric.name.includes('معدل الأخطاء') ? (1 - metric.value / 100) * 100 : 
                               metric.name.includes('وقت الاستجابة') ? Math.max(0, 100 - metric.value / 5) :
                               metric.value > 100 ? 100 : metric.value} 
                        color={getStatusColor(metric.status)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Grid>
                  ))}
                </Grid>
              </UnifiedCardContent>
            </UnifiedCard>
          </PageSection>
        </Grid>

        {/* أحداث النظام الأخيرة */}
        <Grid item xs={12} lg={6}>
          <PageSection title="أحداث النظام الأخيرة">
            <UnifiedCard>
              <UnifiedCardContent>
                <Box maxHeight={300} overflow="auto">
                  {recentEvents.map((event) => (
                    <Box key={event.id} display="flex" alignItems="flex-start" gap={2} mb={2} p={2} 
                         border={1} borderColor="divider" borderRadius={2}>
                      {getSeverityIcon(event.severity)}
                      <Box flex={1}>
                        <Typography variant="body2" gutterBottom>
                          {event.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {event.timestamp}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </UnifiedCardContent>
            </UnifiedCard>
          </PageSection>
        </Grid>
      </Grid>

      {/* ملخص سريع */}
      <PageSection title="ملخص سريع">
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  الأداء اليوم
                </Typography>
                <Typography variant="body2">
                  • {systemStats.dailyActiveUsers} مستخدم نشط
                </Typography>
                <Typography variant="body2">
                  • 15 شركة جديدة
                </Typography>
                <Typography variant="body2">
                  • 89 حملة تم إطلاقها
                </Typography>
                <Typography variant="body2">
                  • 0 مشاكل فنية
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom color="secondary">
                  التحديثات المطلوبة
                </Typography>
                <Typography variant="body2">
                  • تحديث أمني متاح
                </Typography>
                <Typography variant="body2">
                  • توسيع مساحة التخزين
                </Typography>
                <Typography variant="body2">
                  • مراجعة إعدادات النسخ الاحتياطي
                </Typography>
                <Typography variant="body2">
                  • تحديث شهادات SSL
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  الإنجازات الأخيرة
                </Typography>
                <Typography variant="body2">
                  • وصلنا إلى 1000+ مستخدم
                </Typography>
                <Typography variant="body2">
                  • تحسين الأداء بنسبة 25%
                </Typography>
                <Typography variant="body2">
                  • إطلاق ميزات جديدة
                </Typography>
                <Typography variant="body2">
                  • تحقيق 99.9% وقت تشغيل
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </PageSection>
    </PageContainer>
  );
};

export default SystemOverview;
