import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider, useAuth } from './shared/contexts/AuthContext';

// استيراد ملفات CSS الموحدة
import './shared/styles/layout.css';
import './shared/styles/responsive.css';
import ProtectedRoute from './shared/components/routing/ProtectedRoute';

// استيراد الـ themes
import {
  personalTheme,
  businessTheme,
  adminTheme,
  ownerTheme
} from './shared/theme';

// استيراد صفحات المصادقة
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import UnauthorizedPage from './pages/auth/UnauthorizedPage';

// استيراد الواجهات المختلفة
import PersonalDashboard from './pages/personal/PersonalDashboard';
import BusinessDashboard from './pages/business/BusinessDashboard';
import AdminDashboard from './pages/admin/AdminDashboard';
import OwnerDashboard from './pages/owner/OwnerDashboard';

// استيراد الصفحة الرئيسية
import LandingPage from './pages/LandingPage';

const App = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  // تحديد الـ theme حسب المسار الحالي
  const getThemeByPath = (pathname) => {
    if (pathname.startsWith('/dashboard/personal')) {
      return personalTheme;
    } else if (pathname.startsWith('/dashboard/business')) {
      return businessTheme;
    } else if (pathname.startsWith('/dashboard/admin')) {
      return adminTheme;
    } else if (pathname.startsWith('/dashboard/owner')) {
      return ownerTheme;
    }
    return personalTheme; // default theme
  };

  return (
    <Router>
      <AuthProvider>
        <Routes>
          {/* الصفحة الرئيسية */}
          <Route path="/" element={<LandingPage />} />
          
          {/* صفحات المصادقة */}
          <Route path="/auth/login" element={<LoginPage />} />
          <Route path="/auth/register" element={<RegisterPage />} />
          <Route path="/auth/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/unauthorized" element={<UnauthorizedPage />} />

          {/* الواجهة الشخصية */}
          <Route 
            path="/dashboard/personal/*" 
            element={
              <ProtectedRoute requiredAccountTypes={['personal']}>
                <ThemeProvider theme={personalTheme}>
                  <CssBaseline />
                  <PersonalDashboard onThemeToggle={toggleTheme} isDarkMode={isDarkMode} />
                </ThemeProvider>
              </ProtectedRoute>
            } 
          />

          {/* واجهة الشركات */}
          <Route 
            path="/dashboard/business/*" 
            element={
              <ProtectedRoute requiredAccountTypes={['business_user', 'business_owner']}>
                <ThemeProvider theme={businessTheme}>
                  <CssBaseline />
                  <BusinessDashboard onThemeToggle={toggleTheme} isDarkMode={isDarkMode} />
                </ThemeProvider>
              </ProtectedRoute>
            } 
          />

          {/* واجهة المدير */}
          <Route 
            path="/dashboard/admin/*" 
            element={
              <ProtectedRoute requiredAccountTypes={['admin']}>
                <ThemeProvider theme={adminTheme}>
                  <CssBaseline />
                  <AdminDashboard onThemeToggle={toggleTheme} isDarkMode={isDarkMode} />
                </ThemeProvider>
              </ProtectedRoute>
            } 
          />

          {/* واجهة المالك */}
          <Route 
            path="/dashboard/owner/*" 
            element={
              <ProtectedRoute requiredAccountTypes={['owner']}>
                <ThemeProvider theme={ownerTheme}>
                  <CssBaseline />
                  <OwnerDashboard onThemeToggle={toggleTheme} isDarkMode={isDarkMode} />
                </ThemeProvider>
              </ProtectedRoute>
            } 
          />

          {/* إعادة توجيه افتراضية */}
          <Route 
            path="/dashboard" 
            element={
              <ProtectedRoute>
                <DashboardRedirect />
              </ProtectedRoute>
            } 
          />

          {/* صفحة 404 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
};

// مكون لإعادة التوجيه حسب نوع الحساب
const DashboardRedirect = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Navigate to="/auth/login" replace />;
  }

  switch (currentUser.account_type) {
    case 'personal':
      return <Navigate to="/dashboard/personal" replace />;
    case 'business_user':
    case 'business_owner':
      return <Navigate to="/dashboard/business" replace />;
    case 'admin':
      return <Navigate to="/dashboard/admin" replace />;
    case 'owner':
      return <Navigate to="/dashboard/owner" replace />;
    default:
      return <Navigate to="/auth/login" replace />;
  }
};

export default App;
