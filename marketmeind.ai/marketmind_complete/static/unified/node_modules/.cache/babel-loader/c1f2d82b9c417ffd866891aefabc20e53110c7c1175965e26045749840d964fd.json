{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Dashboard as DashboardIcon, SupervisorAccount as OwnerIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport OwnerHome from './OwnerHome';\nimport SystemOverview from './SystemOverview';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OwnerDashboard = ({\n  onThemeToggle,\n  isDarkMode\n}) => {\n  const menuItems = [{\n    id: 'dashboard',\n    title: 'لوحة التحكم',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/owner'\n  }, {\n    id: 'system-overview',\n    title: 'نظرة عامة على النظام',\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/owner/system-overview'\n  }, {\n    id: 'security',\n    title: 'الأمان والحماية',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/owner/security'\n  }, {\n    id: 'advanced-settings',\n    title: 'الإعدادات المتقدمة',\n    icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/owner/advanced-settings'\n  }];\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"MarketMind Owner\",\n    menuItems: menuItems,\n    onThemeToggle: onThemeToggle,\n    isDarkMode: isDarkMode,\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(OwnerHome, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/system-overview\",\n        element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n          title: \"\\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n          description: \"\\u0645\\u0631\\u0627\\u0642\\u0628\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0644\\u062C\\u0645\\u064A\\u0639 \\u062C\\u0648\\u0627\\u0646\\u0628 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0645\\u0639 \\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/security\",\n        element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n          title: \"\\u0627\\u0644\\u0623\\u0645\\u0627\\u0646 \\u0648\\u0627\\u0644\\u062D\\u0645\\u0627\\u064A\\u0629\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0623\\u0645\\u0627\\u0646 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u062D\\u0645\\u0627\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0642\\u062F\\u0645\\u0629 \\u0636\\u062F \\u0627\\u0644\\u062A\\u0647\\u062F\\u064A\\u062F\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/advanced-settings\",\n        element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n          title: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\",\n          description: \"\\u062A\\u0643\\u0648\\u064A\\u0646 \\u0645\\u062A\\u0642\\u062F\\u0645 \\u0644\\u062C\\u0645\\u064A\\u0639 \\u062C\\u0648\\u0627\\u0646\\u0628 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u0645\\u0646\\u0635\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = OwnerDashboard;\nexport default OwnerDashboard;\nvar _c;\n$RefreshReg$(_c, \"OwnerDashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Dashboard", "DashboardIcon", "SupervisorAccount", "OwnerIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "Security", "SecurityIcon", "DashboardLayout", "OwnerHome", "SystemOverview", "ComingSoon", "jsxDEV", "_jsxDEV", "OwnerDashboard", "onThemeToggle", "isDarkMode", "menuItems", "id", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "element", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport {\n  Dashboard as DashboardIcon,\n  SupervisorAccount as OwnerIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon,\n  Security as SecurityIcon\n} from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport OwnerHome from './OwnerHome';\nimport SystemOverview from './SystemOverview';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\n\nconst OwnerDashboard = ({ onThemeToggle, isDarkMode }) => {\n  const menuItems = [\n    {\n      id: 'dashboard',\n      title: 'لوحة التحكم',\n      icon: <DashboardIcon />,\n      path: '/dashboard/owner'\n    },\n    {\n      id: 'system-overview',\n      title: 'نظرة عامة على النظام',\n      icon: <AnalyticsIcon />,\n      path: '/dashboard/owner/system-overview'\n    },\n    {\n      id: 'security',\n      title: 'الأمان والحماية',\n      icon: <SecurityIcon />,\n      path: '/dashboard/owner/security'\n    },\n    {\n      id: 'advanced-settings',\n      title: 'الإعدادات المتقدمة',\n      icon: <SettingsIcon />,\n      path: '/dashboard/owner/advanced-settings'\n    }\n  ];\n\n  return (\n    <DashboardLayout\n      title=\"MarketMind Owner\"\n      menuItems={menuItems}\n      onThemeToggle={onThemeToggle}\n      isDarkMode={isDarkMode}\n    >\n      <Routes>\n        <Route path=\"/\" element={<OwnerHome />} />\n        <Route path=\"/system-overview\" element={\n          <ComingSoon\n            title=\"نظرة عامة على النظام\"\n            description=\"مراقبة شاملة لجميع جوانب النظام مع تحليلات متقدمة\"\n          />\n        } />\n        <Route path=\"/security\" element={\n          <ComingSoon\n            title=\"الأمان والحماية\"\n            description=\"إدارة أمان النظام والحماية المتقدمة ضد التهديدات\"\n          />\n        } />\n        <Route path=\"/advanced-settings\" element={\n          <ComingSoon\n            title=\"الإعدادات المتقدمة\"\n            description=\"تكوين متقدم لجميع جوانب النظام والمنصة\"\n          />\n        } />\n      </Routes>\n    </DashboardLayout>\n  );\n};\n\nexport default OwnerDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SACEC,SAAS,IAAIC,aAAa,EAC1BC,iBAAiB,IAAIC,SAAS,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gDAAgD;AAC5E,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,UAAU,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,cAAc,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAW,CAAC,KAAK;EACxD,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEP,OAAA,CAACd,aAAa;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,eAAEP,OAAA,CAACV,aAAa;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,eAAEP,OAAA,CAACN,YAAY;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,mBAAmB;IACvBC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,eAAEP,OAAA,CAACR,YAAY;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEZ,OAAA,CAACL,eAAe;IACdW,KAAK,EAAC,kBAAkB;IACxBF,SAAS,EAAEA,SAAU;IACrBF,aAAa,EAAEA,aAAc;IAC7BC,UAAU,EAAEA,UAAW;IAAAU,QAAA,eAEvBb,OAAA,CAACjB,MAAM;MAAA8B,QAAA,gBACLb,OAAA,CAAChB,KAAK;QAAC4B,IAAI,EAAC,GAAG;QAACE,OAAO,eAAEd,OAAA,CAACJ,SAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CX,OAAA,CAAChB,KAAK;QAAC4B,IAAI,EAAC,kBAAkB;QAACE,OAAO,eACpCd,OAAA,CAACF,UAAU;UACTQ,KAAK,EAAC,2GAAsB;UAC5BS,WAAW,EAAC;QAAmD;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJX,OAAA,CAAChB,KAAK;QAAC4B,IAAI,EAAC,WAAW;QAACE,OAAO,eAC7Bd,OAAA,CAACF,UAAU;UACTQ,KAAK,EAAC,uFAAiB;UACvBS,WAAW,EAAC;QAAkD;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJX,OAAA,CAAChB,KAAK;QAAC4B,IAAI,EAAC,oBAAoB;QAACE,OAAO,eACtCd,OAAA,CAACF,UAAU;UACTQ,KAAK,EAAC,yGAAoB;UAC1BS,WAAW,EAAC;QAAwC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEtB,CAAC;AAACK,EAAA,GA1DIf,cAAc;AA4DpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}