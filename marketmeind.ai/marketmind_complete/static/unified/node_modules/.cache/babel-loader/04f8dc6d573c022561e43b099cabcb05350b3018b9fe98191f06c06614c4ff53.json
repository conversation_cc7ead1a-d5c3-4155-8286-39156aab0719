{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Typography, Button, Alert, Chip } from '@mui/material';\nimport { Campaign as CampaignIcon, People as PeopleIcon, AttachMoney as MoneyIcon, TrendingUp as TrendingUpIcon, Add as AddIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon, Lightbulb as LightbulbIcon, Assessment as AssessmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../shared/contexts/AuthContext';\nimport { UnifiedCard, UnifiedCardContent, StatsCard, ActionCard, PageContainer, PageSection, StatsGrid, UnifiedGrid } from '../../shared/components/ui';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BusinessHome = () => {\n  _s();\n  var _stats$monthlyRevenue;\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n  const navigate = useNavigate();\n  const {\n    currentUser\n  } = useAuth();\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setStats({\n        activeCampaigns: 12,\n        totalCustomers: 2450,\n        monthlyRevenue: 125000,\n        conversionRate: 3.8\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \", currentUser === null || currentUser === void 0 ? void 0 : currentUser.first_name, \"! \\uD83C\\uDFE2\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"\\u0646\\u0638\\u0631\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0639\\u0644\\u0649 \\u0623\\u062F\\u0627\\u0621 \\u062D\\u0645\\u0644\\u0627\\u062A\\u0643 \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0648\\u0639\\u0645\\u0644\\u0627\\u0626\\u0643\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), (currentUser === null || currentUser === void 0 ? void 0 : currentUser.company_name) && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"primary.main\",\n          fontWeight: \"bold\",\n          children: currentUser.company_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.account_type) === 'business_owner' ? 'مالك شركة' : 'مستخدم شركة',\n          color: \"secondary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 24\n          }, this),\n          onClick: () => navigate('/dashboard/business/campaigns/create'),\n          children: \"\\u062D\\u0645\\u0644\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), \"\\u064A\\u0645\\u0643\\u0646\\u0643 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u062D\\u0645\\u0644\\u0627\\u062A\\u0643 \\u0648\\u0639\\u0645\\u0644\\u0627\\u0626\\u0643 \\u0645\\u0646 \\u0627\\u0644\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u062C\\u0627\\u0646\\u0628\\u064A\\u0629.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0627\\u0644\\u062D\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\",\n          value: stats.activeCampaigns,\n          subtitle: \"\\u062D\\u0645\\u0644\\u0629\",\n          icon: /*#__PURE__*/_jsxDEV(CampaignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 19\n          }, this),\n          color: \"primary\",\n          loading: loading,\n          trend: \"\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0634\\u0647\\u0631\",\n          trendValue: 15.2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\",\n          value: stats.totalCustomers,\n          subtitle: \"\\u0639\\u0645\\u064A\\u0644\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 19\n          }, this),\n          color: \"secondary\",\n          loading: loading,\n          trend: \"\\u0646\\u0645\\u0648 \\u0634\\u0647\\u0631\\u064A\",\n          trendValue: 8.7\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0647\\u0631\\u064A\\u0629\",\n          value: `${(_stats$monthlyRevenue = stats.monthlyRevenue) === null || _stats$monthlyRevenue === void 0 ? void 0 : _stats$monthlyRevenue.toLocaleString()} ر.س`,\n          subtitle: \"\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0634\\u0647\\u0631\",\n          icon: /*#__PURE__*/_jsxDEV(MoneyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 19\n          }, this),\n          color: \"success\",\n          loading: loading,\n          trend: \"\\u0645\\u0642\\u0627\\u0631\\u0646\\u0629 \\u0628\\u0627\\u0644\\u0634\\u0647\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0636\\u064A\",\n          trendValue: 12.3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644\",\n          value: `${stats.conversionRate}%`,\n          subtitle: \"\\u0645\\u062A\\u0648\\u0633\\u0637\",\n          icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 19\n          }, this),\n          color: \"info\",\n          loading: loading,\n          progress: stats.conversionRate * 10,\n          progressColor: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      sx: {\n        mt: 4,\n        mb: 3\n      },\n      children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A \\u0627\\u0644\\u0633\\u0631\\u064A\\u0639\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"unified-grid unified-grid-3 mb-xl\",\n      children: [/*#__PURE__*/_jsxDEV(ActionCard, {\n        title: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0645\\u0644\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\",\n        description: \"\\u0627\\u0628\\u062F\\u0623 \\u062D\\u0645\\u0644\\u0629 \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629 \\u0628\\u062E\\u0637\\u0648\\u0627\\u062A \\u0628\\u0633\\u064A\\u0637\\u0629\",\n        icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 17\n        }, this),\n        buttonText: \"\\u0625\\u0646\\u0634\\u0627\\u0621 \\u062D\\u0645\\u0644\\u0629\",\n        onButtonClick: () => navigate('/dashboard/business/campaigns/create'),\n        color: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionCard, {\n        title: \"\\u062A\\u062D\\u0644\\u064A\\u0644 \\u0627\\u0644\\u0623\\u062F\\u0627\\u0621\",\n        description: \"\\u0631\\u0627\\u062C\\u0639 \\u0623\\u062F\\u0627\\u0621 \\u062D\\u0645\\u0644\\u0627\\u062A\\u0643 \\u0648\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0645\\u0641\\u0635\\u0644\\u0629\",\n        icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 17\n        }, this),\n        buttonText: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A\",\n        onButtonClick: () => navigate('/dashboard/business/campaigns/analytics'),\n        color: \"secondary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionCard, {\n        title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\",\n        description: \"\\u062A\\u0635\\u0641\\u062D \\u0648\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0642\\u0627\\u0639\\u062F\\u0629 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0639\\u0645\\u0644\\u0627\\u0626\\u0643\",\n        icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 17\n        }, this),\n        buttonText: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\",\n        onButtonClick: () => navigate('/dashboard/business/customers'),\n        color: \"success\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      sx: {\n        mb: 3\n      },\n      children: \"\\u0627\\u0644\\u062D\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"unified-grid unified-grid-2 mb-xl\",\n      children: [/*#__PURE__*/_jsxDEV(UnifiedCard, {\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"\\u062D\\u0645\\u0644\\u0629 \\u0627\\u0644\\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0635\\u064A\\u0641\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u0646\\u0634\\u0637\\u0629\",\n              color: \"success\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            paragraph: true,\n            children: \"\\u062D\\u0645\\u0644\\u0629 \\u062A\\u0633\\u0648\\u064A\\u0642\\u064A\\u0629 \\u0644\\u0644\\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0635\\u064A\\u0641\\u064A\\u0629 \\u0645\\u0639 \\u062E\\u0635\\u0648\\u0645\\u0627\\u062A \\u062A\\u0635\\u0644 \\u0625\\u0644\\u0649 50%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\u0627\\u0644\\u0645\\u064A\\u0632\\u0627\\u0646\\u064A\\u0629: 25,000 \\u0631.\\u0633\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"success.main\",\n              children: \"ROI: +320%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UnifiedCard, {\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"\\u0625\\u0637\\u0644\\u0627\\u0642 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"\\u0646\\u0634\\u0637\\u0629\",\n              color: \"success\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            paragraph: true,\n            children: \"\\u062D\\u0645\\u0644\\u0629 \\u0625\\u0637\\u0644\\u0627\\u0642 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F \\u0645\\u0639 \\u0639\\u0631\\u0648\\u0636 \\u062E\\u0627\\u0635\\u0629 \\u0644\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0623\\u0648\\u0627\\u0626\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\u0627\\u0644\\u0645\\u064A\\u0632\\u0627\\u0646\\u064A\\u0629: 20,000 \\u0631.\\u0633\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"success.main\",\n              children: \"ROI: +280%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      sx: {\n        mb: 3\n      },\n      children: \"\\u0646\\u0635\\u0627\\u0626\\u062D \\u0648\\u062A\\u0648\\u0635\\u064A\\u0627\\u062A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"unified-grid unified-grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(UnifiedCard, {\n        sx: {\n          bgcolor: 'info.light',\n          color: 'info.contrastText'\n        },\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            mb: 1,\n            children: [/*#__PURE__*/_jsxDEV(LightbulbIcon, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"\\u0646\\u0635\\u064A\\u062D\\u0629 \\u0627\\u0644\\u064A\\u0648\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"\\u0627\\u0633\\u062A\\u062E\\u062F\\u0645 \\u062A\\u062C\\u0632\\u0626\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0644\\u062A\\u062D\\u0633\\u064A\\u0646 \\u0627\\u0633\\u062A\\u0647\\u062F\\u0627\\u0641 \\u062D\\u0645\\u0644\\u0627\\u062A\\u0643 \\u0648\\u0632\\u064A\\u0627\\u062F\\u0629 \\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0633\\u0628\\u0629 \\u062A\\u0635\\u0644 \\u0625\\u0644\\u0649 40%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UnifiedCard, {\n        sx: {\n          bgcolor: 'warning.light',\n          color: 'warning.contrastText'\n        },\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            mb: 1,\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"\\u062A\\u0648\\u0635\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"\\u0631\\u0627\\u062C\\u0639 \\u0623\\u062F\\u0627\\u0621 \\u062D\\u0645\\u0644\\u0627\\u062A\\u0643 \\u0623\\u0633\\u0628\\u0648\\u0639\\u064A\\u0627\\u064B \\u0648\\u0642\\u0645 \\u0628\\u062A\\u062D\\u0633\\u064A\\u0646 \\u0627\\u0644\\u0643\\u0644\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0641\\u062A\\u0627\\u062D\\u064A\\u0629 \\u0644\\u062A\\u062D\\u0642\\u064A\\u0642 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0646\\u062A\\u0627\\u0626\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(BusinessHome, \"ZGJLDUNvDGjHg5ujYJhO+2DoueI=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = BusinessHome;\nexport default BusinessHome;\nvar _c;\n$RefreshReg$(_c, \"BusinessHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "Campaign", "CampaignIcon", "People", "PeopleIcon", "AttachMoney", "MoneyIcon", "TrendingUp", "TrendingUpIcon", "Add", "AddIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "Lightbulb", "LightbulbIcon", "Assessment", "AssessmentIcon", "useNavigate", "useAuth", "UnifiedCard", "UnifiedCardContent", "StatsCard", "ActionCard", "<PERSON><PERSON><PERSON><PERSON>", "PageSection", "StatsGrid", "UnifiedGrid", "jsxDEV", "_jsxDEV", "BusinessHome", "_s", "_stats$monthlyRevenue", "loading", "setLoading", "stats", "setStats", "navigate", "currentUser", "setTimeout", "activeCampaigns", "totalCustomers", "monthlyRevenue", "conversionRate", "children", "display", "justifyContent", "alignItems", "mb", "variant", "component", "gutterBottom", "first_name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "company_name", "fontWeight", "gap", "label", "account_type", "startIcon", "onClick", "severity", "sx", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "subtitle", "icon", "trend", "trendValue", "toLocaleString", "progress", "progressColor", "mt", "className", "description", "buttonText", "onButtonClick", "size", "paragraph", "bgcolor", "mr", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  <PERSON>ton,\n  Alert,\n  Chip\n} from '@mui/material';\nimport {\n  Campaign as CampaignIcon,\n  People as PeopleIcon,\n  AttachMoney as MoneyIcon,\n  TrendingUp as TrendingUpIcon,\n  Add as AddIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon,\n  Lightbulb as LightbulbIcon,\n  Assessment as AssessmentIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../shared/contexts/AuthContext';\nimport {\n  UnifiedCard,\n  UnifiedCardContent,\n  StatsCard,\n  ActionCard,\n  PageContainer,\n  PageSection,\n  StatsGrid,\n  UnifiedGrid\n} from '../../shared/components/ui';\n\nconst BusinessHome = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n  const navigate = useNavigate();\n  const { currentUser } = useAuth();\n\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setStats({\n        activeCampaigns: 12,\n        totalCustomers: 2450,\n        monthlyRevenue: 125000,\n        conversionRate: 3.8\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Box>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            مرحباً {currentUser?.first_name}! 🏢\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            نظرة شاملة على أداء حملاتك التسويقية وعملائك\n          </Typography>\n          {currentUser?.company_name && (\n            <Typography variant=\"body2\" color=\"primary.main\" fontWeight=\"bold\">\n              {currentUser.company_name}\n            </Typography>\n          )}\n        </Box>\n        <Box display=\"flex\" gap={1}>\n          <Chip\n            label={currentUser?.account_type === 'business_owner' ? 'مالك شركة' : 'مستخدم شركة'}\n            color=\"secondary\"\n            variant=\"outlined\"\n          />\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => navigate('/dashboard/business/campaigns/create')}\n          >\n            حملة جديدة\n          </Button>\n        </Box>\n      </Box>\n\n      <Alert severity=\"success\" sx={{ mb: 3 }}>\n        <Typography variant=\"body2\">\n          <strong>مرحباً بك في لوحة تحكم الشركة!</strong>\n          يمكنك إدارة حملاتك وعملائك من القائمة الجانبية.\n        </Typography>\n      </Alert>\n\n      {/* إحصائيات الشركة */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"الحملات النشطة\"\n            value={stats.activeCampaigns}\n            subtitle=\"حملة\"\n            icon={<CampaignIcon />}\n            color=\"primary\"\n            loading={loading}\n            trend=\"هذا الشهر\"\n            trendValue={15.2}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"إجمالي العملاء\"\n            value={stats.totalCustomers}\n            subtitle=\"عميل\"\n            icon={<PeopleIcon />}\n            color=\"secondary\"\n            loading={loading}\n            trend=\"نمو شهري\"\n            trendValue={8.7}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"الإيرادات الشهرية\"\n            value={`${stats.monthlyRevenue?.toLocaleString()} ر.س`}\n            subtitle=\"هذا الشهر\"\n            icon={<MoneyIcon />}\n            color=\"success\"\n            loading={loading}\n            trend=\"مقارنة بالشهر الماضي\"\n            trendValue={12.3}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"معدل التحويل\"\n            value={`${stats.conversionRate}%`}\n            subtitle=\"متوسط\"\n            icon={<TrendingUpIcon />}\n            color=\"info\"\n            loading={loading}\n            progress={stats.conversionRate * 10}\n            progressColor=\"success\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* الإجراءات السريعة */}\n      <Typography variant=\"h5\" gutterBottom sx={{ mt: 4, mb: 3 }}>\n        الإجراءات السريعة\n      </Typography>\n\n      <Box className=\"unified-grid unified-grid-3 mb-xl\">\n        <ActionCard\n          title=\"إنشاء حملة جديدة\"\n          description=\"ابدأ حملة تسويقية جديدة بخطوات بسيطة\"\n          icon={<AddIcon />}\n          buttonText=\"إنشاء حملة\"\n          onButtonClick={() => navigate('/dashboard/business/campaigns/create')}\n          color=\"primary\"\n        />\n\n        <ActionCard\n          title=\"تحليل الأداء\"\n          description=\"راجع أداء حملاتك وتحليلات مفصلة\"\n          icon={<AnalyticsIcon />}\n          buttonText=\"عرض التحليلات\"\n          onButtonClick={() => navigate('/dashboard/business/campaigns/analytics')}\n          color=\"secondary\"\n        />\n\n        <ActionCard\n          title=\"إدارة العملاء\"\n          description=\"تصفح وإدارة قاعدة بيانات عملائك\"\n          icon={<PeopleIcon />}\n          buttonText=\"إدارة العملاء\"\n          onButtonClick={() => navigate('/dashboard/business/customers')}\n          color=\"success\"\n        />\n      </Box>\n\n      {/* الحملات الأخيرة */}\n      <Typography variant=\"h5\" gutterBottom sx={{ mb: 3 }}>\n        الحملات الأخيرة\n      </Typography>\n\n      <Box className=\"unified-grid unified-grid-2 mb-xl\">\n        <UnifiedCard>\n          <UnifiedCardContent>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n              <Typography variant=\"h6\">حملة العروض الصيفية</Typography>\n              <Chip label=\"نشطة\" color=\"success\" size=\"small\" />\n            </Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" paragraph>\n              حملة تسويقية للعروض الصيفية مع خصومات تصل إلى 50%\n            </Typography>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n              <Typography variant=\"body2\">\n                الميزانية: 25,000 ر.س\n              </Typography>\n              <Typography variant=\"body2\" color=\"success.main\">\n                ROI: +320%\n              </Typography>\n            </Box>\n          </UnifiedCardContent>\n        </UnifiedCard>\n\n        <UnifiedCard>\n          <UnifiedCardContent>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n              <Typography variant=\"h6\">إطلاق المنتج الجديد</Typography>\n              <Chip label=\"نشطة\" color=\"success\" size=\"small\" />\n            </Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" paragraph>\n              حملة إطلاق المنتج الجديد مع عروض خاصة للعملاء الأوائل\n            </Typography>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n              <Typography variant=\"body2\">\n                الميزانية: 20,000 ر.س\n              </Typography>\n              <Typography variant=\"body2\" color=\"success.main\">\n                ROI: +280%\n              </Typography>\n            </Box>\n          </UnifiedCardContent>\n        </UnifiedCard>\n      </Box>\n\n      {/* نصائح وتوصيات */}\n      <Typography variant=\"h5\" gutterBottom sx={{ mb: 3 }}>\n        نصائح وتوصيات\n      </Typography>\n\n      <Box className=\"unified-grid unified-grid-2\">\n        <UnifiedCard sx={{ bgcolor: 'info.light', color: 'info.contrastText' }}>\n          <UnifiedCardContent>\n            <Box display=\"flex\" alignItems=\"center\" mb={1}>\n              <LightbulbIcon sx={{ mr: 1 }} />\n              <Typography variant=\"h6\">\n                نصيحة اليوم\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\">\n              استخدم تجزئة العملاء لتحسين استهداف حملاتك وزيادة معدل التحويل بنسبة تصل إلى 40%\n            </Typography>\n          </UnifiedCardContent>\n        </UnifiedCard>\n\n        <UnifiedCard sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>\n          <UnifiedCardContent>\n            <Box display=\"flex\" alignItems=\"center\" mb={1}>\n              <AssessmentIcon sx={{ mr: 1 }} />\n              <Typography variant=\"h6\">\n                توصية\n              </Typography>\n            </Box>\n            <Typography variant=\"body2\">\n              راجع أداء حملاتك أسبوعياً وقم بتحسين الكلمات المفتاحية لتحقيق أفضل النتائج\n            </Typography>\n          </UnifiedCardContent>\n        </UnifiedCard>\n      </Box>\n    </Box>\n  );\n};\n\nexport default BusinessHome;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,SAAS,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SACEC,WAAW,EACXC,kBAAkB,EAClBC,SAAS,EACTC,UAAU,EACVC,aAAa,EACbC,WAAW,EACXC,SAAS,EACTC,WAAW,QACN,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM6C,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB;EAAY,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAEjC1B,SAAS,CAAC,MAAM;IACd;IACA8C,UAAU,CAAC,MAAM;MACfH,QAAQ,CAAC;QACPI,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE,MAAM;QACtBC,cAAc,EAAE;MAClB,CAAC,CAAC;MACFT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA,CAACnC,GAAG;IAAAkD,QAAA,gBACFf,OAAA,CAACnC,GAAG;MAACmD,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBAC3Ef,OAAA,CAACnC,GAAG;QAAAkD,QAAA,gBACFf,OAAA,CAACjC,UAAU;UAACqD,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAP,QAAA,GAAC,uCAC5C,EAACN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,UAAU,EAAC,gBAClC;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3B,OAAA,CAACjC,UAAU;UAACqD,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAAAb,QAAA,EAAC;QAEnD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAAAlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoB,YAAY,kBACxB7B,OAAA,CAACjC,UAAU;UAACqD,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,cAAc;UAACE,UAAU,EAAC,MAAM;UAAAf,QAAA,EAC/DN,WAAW,CAACoB;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN3B,OAAA,CAACnC,GAAG;QAACmD,OAAO,EAAC,MAAM;QAACe,GAAG,EAAE,CAAE;QAAAhB,QAAA,gBACzBf,OAAA,CAAC9B,IAAI;UACH8D,KAAK,EAAE,CAAAvB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,YAAY,MAAK,gBAAgB,GAAG,WAAW,GAAG,aAAc;UACpFL,KAAK,EAAC,WAAW;UACjBR,OAAO,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACF3B,OAAA,CAAChC,MAAM;UACLoD,OAAO,EAAC,WAAW;UACnBc,SAAS,eAAElC,OAAA,CAACpB,OAAO;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBQ,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAAC,sCAAsC,CAAE;UAAAO,QAAA,EACjE;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA,CAAC/B,KAAK;MAACmE,QAAQ,EAAC,SAAS;MAACC,EAAE,EAAE;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACtCf,OAAA,CAACjC,UAAU;QAACqD,OAAO,EAAC,OAAO;QAAAL,QAAA,gBACzBf,OAAA;UAAAe,QAAA,EAAQ;QAA8B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,2PAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGR3B,OAAA,CAAClC,IAAI;MAACwE,SAAS;MAACC,OAAO,EAAE,CAAE;MAACF,EAAE,EAAE;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCf,OAAA,CAAClC,IAAI;QAAC0E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9Bf,OAAA,CAACP,SAAS;UACRmD,KAAK,EAAC,iFAAgB;UACtBC,KAAK,EAAEvC,KAAK,CAACK,eAAgB;UAC7BmC,QAAQ,EAAC,0BAAM;UACfC,IAAI,eAAE/C,OAAA,CAAC5B,YAAY;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,KAAK,EAAC,SAAS;UACfxB,OAAO,EAAEA,OAAQ;UACjB4C,KAAK,EAAC,mDAAW;UACjBC,UAAU,EAAE;QAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP3B,OAAA,CAAClC,IAAI;QAAC0E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9Bf,OAAA,CAACP,SAAS;UACRmD,KAAK,EAAC,iFAAgB;UACtBC,KAAK,EAAEvC,KAAK,CAACM,cAAe;UAC5BkC,QAAQ,EAAC,0BAAM;UACfC,IAAI,eAAE/C,OAAA,CAAC1B,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBC,KAAK,EAAC,WAAW;UACjBxB,OAAO,EAAEA,OAAQ;UACjB4C,KAAK,EAAC,6CAAU;UAChBC,UAAU,EAAE;QAAI;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP3B,OAAA,CAAClC,IAAI;QAAC0E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9Bf,OAAA,CAACP,SAAS;UACRmD,KAAK,EAAC,mGAAmB;UACzBC,KAAK,EAAE,IAAA1C,qBAAA,GAAGG,KAAK,CAACO,cAAc,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsB+C,cAAc,CAAC,CAAC,MAAO;UACvDJ,QAAQ,EAAC,mDAAW;UACpBC,IAAI,eAAE/C,OAAA,CAACxB,SAAS;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBC,KAAK,EAAC,SAAS;UACfxB,OAAO,EAAEA,OAAQ;UACjB4C,KAAK,EAAC,gHAAsB;UAC5BC,UAAU,EAAE;QAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP3B,OAAA,CAAClC,IAAI;QAAC0E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9Bf,OAAA,CAACP,SAAS;UACRmD,KAAK,EAAC,qEAAc;UACpBC,KAAK,EAAE,GAAGvC,KAAK,CAACQ,cAAc,GAAI;UAClCgC,QAAQ,EAAC,gCAAO;UAChBC,IAAI,eAAE/C,OAAA,CAACtB,cAAc;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBC,KAAK,EAAC,MAAM;UACZxB,OAAO,EAAEA,OAAQ;UACjB+C,QAAQ,EAAE7C,KAAK,CAACQ,cAAc,GAAG,EAAG;UACpCsC,aAAa,EAAC;QAAS;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3B,OAAA,CAACjC,UAAU;MAACqD,OAAO,EAAC,IAAI;MAACE,YAAY;MAACe,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAElC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAE5D;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb3B,OAAA,CAACnC,GAAG;MAACyF,SAAS,EAAC,mCAAmC;MAAAvC,QAAA,gBAChDf,OAAA,CAACN,UAAU;QACTkD,KAAK,EAAC,wFAAkB;QACxBW,WAAW,EAAC,iMAAsC;QAClDR,IAAI,eAAE/C,OAAA,CAACpB,OAAO;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClB6B,UAAU,EAAC,yDAAY;QACvBC,aAAa,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,sCAAsC,CAAE;QACtEoB,KAAK,EAAC;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEF3B,OAAA,CAACN,UAAU;QACTkD,KAAK,EAAC,qEAAc;QACpBW,WAAW,EAAC,wKAAiC;QAC7CR,IAAI,eAAE/C,OAAA,CAAClB,aAAa;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxB6B,UAAU,EAAC,2EAAe;QAC1BC,aAAa,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,yCAAyC,CAAE;QACzEoB,KAAK,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEF3B,OAAA,CAACN,UAAU;QACTkD,KAAK,EAAC,2EAAe;QACrBW,WAAW,EAAC,wKAAiC;QAC7CR,IAAI,eAAE/C,OAAA,CAAC1B,UAAU;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrB6B,UAAU,EAAC,2EAAe;QAC1BC,aAAa,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,+BAA+B,CAAE;QAC/DoB,KAAK,EAAC;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA,CAACjC,UAAU;MAACqD,OAAO,EAAC,IAAI;MAACE,YAAY;MAACe,EAAE,EAAE;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAErD;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb3B,OAAA,CAACnC,GAAG;MAACyF,SAAS,EAAC,mCAAmC;MAAAvC,QAAA,gBAChDf,OAAA,CAACT,WAAW;QAAAwB,QAAA,eACVf,OAAA,CAACR,kBAAkB;UAAAuB,QAAA,gBACjBf,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,eAAe;YAACC,UAAU,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBAC3Ef,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzD3B,OAAA,CAAC9B,IAAI;cAAC8D,KAAK,EAAC,0BAAM;cAACJ,KAAK,EAAC,SAAS;cAAC8B,IAAI,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN3B,OAAA,CAACjC,UAAU;YAACqD,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAC+B,SAAS;YAAA5C,QAAA,EAAC;UAE7D;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3B,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,eAAe;YAACC,UAAU,EAAC,QAAQ;YAAAH,QAAA,gBACpEf,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3B,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,OAAO;cAACQ,KAAK,EAAC,cAAc;cAAAb,QAAA,EAAC;YAEjD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEd3B,OAAA,CAACT,WAAW;QAAAwB,QAAA,eACVf,OAAA,CAACR,kBAAkB;UAAAuB,QAAA,gBACjBf,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,eAAe;YAACC,UAAU,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBAC3Ef,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzD3B,OAAA,CAAC9B,IAAI;cAAC8D,KAAK,EAAC,0BAAM;cAACJ,KAAK,EAAC,SAAS;cAAC8B,IAAI,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN3B,OAAA,CAACjC,UAAU;YAACqD,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAC+B,SAAS;YAAA5C,QAAA,EAAC;UAE7D;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3B,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,eAAe;YAACC,UAAU,EAAC,QAAQ;YAAAH,QAAA,gBACpEf,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3B,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,OAAO;cAACQ,KAAK,EAAC,cAAc;cAAAb,QAAA,EAAC;YAEjD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGN3B,OAAA,CAACjC,UAAU;MAACqD,OAAO,EAAC,IAAI;MAACE,YAAY;MAACe,EAAE,EAAE;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAErD;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb3B,OAAA,CAACnC,GAAG;MAACyF,SAAS,EAAC,6BAA6B;MAAAvC,QAAA,gBAC1Cf,OAAA,CAACT,WAAW;QAAC8C,EAAE,EAAE;UAAEuB,OAAO,EAAE,YAAY;UAAEhC,KAAK,EAAE;QAAoB,CAAE;QAAAb,QAAA,eACrEf,OAAA,CAACR,kBAAkB;UAAAuB,QAAA,gBACjBf,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBAC5Cf,OAAA,CAACd,aAAa;cAACmD,EAAE,EAAE;gBAAEwB,EAAE,EAAE;cAAE;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC3B,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAEzB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3B,OAAA,CAACjC,UAAU;YAACqD,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAE5B;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEd3B,OAAA,CAACT,WAAW;QAAC8C,EAAE,EAAE;UAAEuB,OAAO,EAAE,eAAe;UAAEhC,KAAK,EAAE;QAAuB,CAAE;QAAAb,QAAA,eAC3Ef,OAAA,CAACR,kBAAkB;UAAAuB,QAAA,gBACjBf,OAAA,CAACnC,GAAG;YAACmD,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACC,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBAC5Cf,OAAA,CAACZ,cAAc;cAACiD,EAAE,EAAE;gBAAEwB,EAAE,EAAE;cAAE;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC3B,OAAA,CAACjC,UAAU;cAACqD,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAEzB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3B,OAAA,CAACjC,UAAU;YAACqD,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAE5B;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAnOID,YAAY;EAAA,QAGCZ,WAAW,EACJC,OAAO;AAAA;AAAAwE,EAAA,GAJ3B7D,YAAY;AAqOlB,eAAeA,YAAY;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}