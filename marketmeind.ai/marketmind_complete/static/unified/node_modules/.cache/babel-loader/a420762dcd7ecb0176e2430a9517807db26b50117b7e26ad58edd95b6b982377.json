{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js\";\nimport React from 'react';\nimport { Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Badge = ({\n  children,\n  variant = 'filled',\n  color = 'default',\n  size = 'medium',\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(Chip, {\n    label: children,\n    variant: variant,\n    color: color,\n    size: size,\n    className: className,\n    sx: {\n      borderRadius: 1,\n      fontWeight: 500,\n      ...props.sx\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = Badge;\nvar _c;\n$RefreshReg$(_c, \"Badge\");", "map": {"version": 3, "names": ["React", "Chip", "jsxDEV", "_jsxDEV", "Badge", "children", "variant", "color", "size", "className", "props", "label", "sx", "borderRadius", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js"], "sourcesContent": ["import React from 'react';\nimport { Chip } from '@mui/material';\n\nexport const Badge = ({ \n  children, \n  variant = 'filled',\n  color = 'default',\n  size = 'medium',\n  className,\n  ...props \n}) => {\n  return (\n    <Chip\n      label={children}\n      variant={variant}\n      color={color}\n      size={size}\n      className={className}\n      sx={{\n        borderRadius: 1,\n        fontWeight: 500,\n        ...props.sx\n      }}\n      {...props}\n    />\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,KAAK,GAAGA,CAAC;EACpBC,QAAQ;EACRC,OAAO,GAAG,QAAQ;EAClBC,KAAK,GAAG,SAAS;EACjBC,IAAI,GAAG,QAAQ;EACfC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEP,OAAA,CAACF,IAAI;IACHU,KAAK,EAAEN,QAAS;IAChBC,OAAO,EAAEA,OAAQ;IACjBC,KAAK,EAAEA,KAAM;IACbC,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEA,SAAU;IACrBG,EAAE,EAAE;MACFC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,GAAG;MACf,GAAGJ,KAAK,CAACE;IACX,CAAE;IAAA,GACEF;EAAK;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACC,EAAA,GAvBWf,KAAK;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}