{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Dashboard as DashboardIcon, Person as PersonIcon, AutoAwesome as AIIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport PersonalHome from './PersonalHome';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonalDashboard = ({\n  onThemeToggle,\n  isDarkMode\n}) => {\n  const menuItems = [{\n    id: 'dashboard',\n    title: 'لوحة التحكم',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/personal'\n  }, {\n    id: 'ai-features',\n    title: 'ميزات الذكاء الاصطناعي',\n    icon: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this),\n    children: [{\n      title: 'تحليل المشاعر',\n      icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this),\n      path: '/dashboard/personal/sentiment-analysis'\n    }, {\n      title: 'توليد المحتوى',\n      icon: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this),\n      path: '/dashboard/personal/content-generation'\n    }, {\n      title: 'التنبؤ بالاتجاهات',\n      icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this),\n      path: '/dashboard/personal/trend-prediction'\n    }]\n  }, {\n    id: 'profile',\n    title: 'الملف الشخصي',\n    icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/personal/profile'\n  }, {\n    id: 'settings',\n    title: 'الإعدادات',\n    icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/personal/settings'\n  }];\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"MarketMind Personal\",\n    menuItems: menuItems,\n    onThemeToggle: onThemeToggle,\n    isDarkMode: isDarkMode,\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(PersonalHome, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/sentiment-analysis\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u062A\\u062D\\u0644\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0634\\u0627\\u0639\\u0631 - \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/content-generation\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u062A\\u0648\\u0644\\u064A\\u062F \\u0627\\u0644\\u0645\\u062D\\u062A\\u0648\\u0649 - \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/trend-prediction\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u0627\\u0644\\u062A\\u0646\\u0628\\u0624 \\u0628\\u0627\\u0644\\u0627\\u062A\\u062C\\u0627\\u0647\\u0627\\u062A - \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 50\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/profile\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A - \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/settings\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A - \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_c = PersonalDashboard;\nexport default PersonalDashboard;\nvar _c;\n$RefreshReg$(_c, \"PersonalDashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Dashboard", "DashboardIcon", "Person", "PersonIcon", "AutoAwesome", "AIIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "DashboardLayout", "PersonalHome", "ComingSoon", "jsxDEV", "_jsxDEV", "PersonalDashboard", "onThemeToggle", "isDarkMode", "menuItems", "id", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "element", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport {\n  Dashboard as DashboardIcon,\n  Person as PersonIcon,\n  AutoAwesome as AIIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport PersonalHome from './PersonalHome';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\n\nconst PersonalDashboard = ({ onThemeToggle, isDarkMode }) => {\n  const menuItems = [\n    {\n      id: 'dashboard',\n      title: 'لوحة التحكم',\n      icon: <DashboardIcon />,\n      path: '/dashboard/personal'\n    },\n    {\n      id: 'ai-features',\n      title: 'ميزات الذكاء الاصطناعي',\n      icon: <AIIcon />,\n      children: [\n        {\n          title: 'تحليل المشاعر',\n          icon: <AnalyticsIcon />,\n          path: '/dashboard/personal/sentiment-analysis'\n        },\n        {\n          title: 'توليد المحتوى',\n          icon: <AIIcon />,\n          path: '/dashboard/personal/content-generation'\n        },\n        {\n          title: 'التنبؤ بالاتجاهات',\n          icon: <AnalyticsIcon />,\n          path: '/dashboard/personal/trend-prediction'\n        }\n      ]\n    },\n    {\n      id: 'profile',\n      title: 'الملف الشخصي',\n      icon: <PersonIcon />,\n      path: '/dashboard/personal/profile'\n    },\n    {\n      id: 'settings',\n      title: 'الإعدادات',\n      icon: <SettingsIcon />,\n      path: '/dashboard/personal/settings'\n    }\n  ];\n\n  return (\n    <DashboardLayout\n      title=\"MarketMind Personal\"\n      menuItems={menuItems}\n      onThemeToggle={onThemeToggle}\n      isDarkMode={isDarkMode}\n    >\n      <Routes>\n        <Route path=\"/\" element={<PersonalHome />} />\n        <Route path=\"/sentiment-analysis\" element={<div>تحليل المشاعر - قريباً</div>} />\n        <Route path=\"/content-generation\" element={<div>توليد المحتوى - قريباً</div>} />\n        <Route path=\"/trend-prediction\" element={<div>التنبؤ بالاتجاهات - قريباً</div>} />\n        <Route path=\"/profile\" element={<div>الملف الشخصي - قريباً</div>} />\n        <Route path=\"/settings\" element={<div>الإعدادات - قريباً</div>} />\n      </Routes>\n    </DashboardLayout>\n  );\n};\n\nexport default PersonalDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,MAAM,EACrBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gDAAgD;AAC5E,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAW,CAAC,KAAK;EAC3D,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEP,OAAA,CAACb,aAAa;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,eAAEP,OAAA,CAACT,MAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBE,QAAQ,EAAE,CACR;MACEP,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEP,OAAA,CAACP,aAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEP,OAAA,CAACT,MAAM;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAChBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,eAAEP,OAAA,CAACP,aAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEP,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,cAAc;IACrBC,IAAI,eAAEP,OAAA,CAACX,UAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEP,OAAA,CAACL,YAAY;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEZ,OAAA,CAACJ,eAAe;IACdU,KAAK,EAAC,qBAAqB;IAC3BF,SAAS,EAAEA,SAAU;IACrBF,aAAa,EAAEA,aAAc;IAC7BC,UAAU,EAAEA,UAAW;IAAAU,QAAA,eAEvBb,OAAA,CAAChB,MAAM;MAAA6B,QAAA,gBACLb,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,GAAG;QAACE,OAAO,eAAEd,OAAA,CAACH,YAAY;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CX,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,qBAAqB;QAACE,OAAO,eAAEd,OAAA;UAAAa,QAAA,EAAK;QAAsB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChFX,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,qBAAqB;QAACE,OAAO,eAAEd,OAAA;UAAAa,QAAA,EAAK;QAAsB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChFX,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,mBAAmB;QAACE,OAAO,eAAEd,OAAA;UAAAa,QAAA,EAAK;QAA0B;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClFX,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,UAAU;QAACE,OAAO,eAAEd,OAAA;UAAAa,QAAA,EAAK;QAAqB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEX,OAAA,CAACf,KAAK;QAAC2B,IAAI,EAAC,WAAW;QAACE,OAAO,eAAEd,OAAA;UAAAa,QAAA,EAAK;QAAkB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEtB,CAAC;AAACI,EAAA,GA7DId,iBAAiB;AA+DvB,eAAeA,iBAAiB;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}