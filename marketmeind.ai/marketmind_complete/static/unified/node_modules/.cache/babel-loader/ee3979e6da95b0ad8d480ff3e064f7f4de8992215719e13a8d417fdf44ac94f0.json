{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js\";\nimport React from 'react';\nimport { Alert as MuiAlert, AlertTitle } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Alert = ({\n  children,\n  severity = 'info',\n  variant = 'filled',\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiAlert, {\n    severity: severity,\n    variant: variant,\n    className: className,\n    sx: {\n      borderRadius: 2,\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Alert;\nexport const AlertDescription = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c2 = AlertDescription;\nvar _c, _c2;\n$RefreshReg$(_c, \"Alert\");\n$RefreshReg$(_c2, \"AlertDescription\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "children", "severity", "variant", "className", "props", "sx", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AlertDescription", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js"], "sourcesContent": ["import React from 'react';\nimport { Alert as Mu<PERSON><PERSON><PERSON><PERSON>, AlertTitle } from '@mui/material';\n\nexport const Alert = ({ \n  children, \n  severity = 'info',\n  variant = 'filled',\n  className,\n  ...props \n}) => {\n  return (\n    <MuiAlert\n      severity={severity}\n      variant={variant}\n      className={className}\n      sx={{\n        borderRadius: 2,\n        ...props.sx\n      }}\n      {...props}\n    >\n      {children}\n    </MuiAlert>\n  );\n};\n\nexport const AlertDescription = ({ children, className, ...props }) => {\n  return (\n    <div className={className} {...props}>\n      {children}\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMJ,KAAK,GAAGA,CAAC;EACpBK,QAAQ;EACRC,QAAQ,GAAG,MAAM;EACjBC,OAAO,GAAG,QAAQ;EAClBC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEL,OAAA,CAACH,QAAQ;IACPK,QAAQ,EAAEA,QAAS;IACnBC,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAEA,SAAU;IACrBE,EAAE,EAAE;MACFC,YAAY,EAAE,CAAC;MACf,GAAGF,KAAK,CAACC;IACX,CAAE;IAAA,GACED,KAAK;IAAAJ,QAAA,EAERA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEf,CAAC;AAACC,EAAA,GArBWhB,KAAK;AAuBlB,OAAO,MAAMiB,gBAAgB,GAAGA,CAAC;EAAEZ,QAAQ;EAAEG,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACrE,oBACEL,OAAA;IAAKI,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAJ,QAAA,EACjCA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACG,GAAA,GANWD,gBAAgB;AAAA,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}