{"ast": null, "code": "const red = {\n  50: '#ffebee',\n  100: '#ffcdd2',\n  200: '#ef9a9a',\n  300: '#e57373',\n  400: '#ef5350',\n  500: '#f44336',\n  600: '#e53935',\n  700: '#d32f2f',\n  800: '#c62828',\n  900: '#b71c1c',\n  A100: '#ff8a80',\n  A200: '#ff5252',\n  A400: '#ff1744',\n  A700: '#d50000'\n};\nexport default red;", "map": {"version": 3, "names": ["red", "A100", "A200", "A400", "A700"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/node_modules/@mui/material/colors/red.js"], "sourcesContent": ["const red = {\n  50: '#ffebee',\n  100: '#ffcdd2',\n  200: '#ef9a9a',\n  300: '#e57373',\n  400: '#ef5350',\n  500: '#f44336',\n  600: '#e53935',\n  700: '#d32f2f',\n  800: '#c62828',\n  900: '#b71c1c',\n  A100: '#ff8a80',\n  A200: '#ff5252',\n  A400: '#ff1744',\n  A700: '#d50000'\n};\nexport default red;"], "mappings": "AAAA,MAAMA,GAAG,GAAG;EACV,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}