{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js\";\nimport React from 'react';\nimport { Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Label = ({\n  children,\n  htmlFor,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(Typography, {\n    component: \"label\",\n    htmlFor: htmlFor,\n    variant: \"body2\",\n    className: className,\n    sx: {\n      fontWeight: 500,\n      color: 'text.primary',\n      display: 'block',\n      mb: 0.5,\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Label;\nvar _c;\n$RefreshReg$(_c, \"Label\");", "map": {"version": 3, "names": ["React", "Typography", "jsxDEV", "_jsxDEV", "Label", "children", "htmlFor", "className", "props", "component", "variant", "sx", "fontWeight", "color", "display", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js"], "sourcesContent": ["import React from 'react';\nimport { Typography } from '@mui/material';\n\nexport const Label = ({ \n  children, \n  htmlFor,\n  className,\n  ...props \n}) => {\n  return (\n    <Typography\n      component=\"label\"\n      htmlFor={htmlFor}\n      variant=\"body2\"\n      className={className}\n      sx={{\n        fontWeight: 500,\n        color: 'text.primary',\n        display: 'block',\n        mb: 0.5,\n        ...props.sx\n      }}\n      {...props}\n    >\n      {children}\n    </Typography>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,OAAO,MAAMC,KAAK,GAAGA,CAAC;EACpBC,QAAQ;EACRC,OAAO;EACPC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEL,OAAA,CAACF,UAAU;IACTQ,SAAS,EAAC,OAAO;IACjBH,OAAO,EAAEA,OAAQ;IACjBI,OAAO,EAAC,OAAO;IACfH,SAAS,EAAEA,SAAU;IACrBI,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,OAAO;MAChBC,EAAE,EAAE,GAAG;MACP,GAAGP,KAAK,CAACG;IACX,CAAE;IAAA,GACEH,KAAK;IAAAH,QAAA,EAERA;EAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;AAACC,EAAA,GAxBWhB,KAAK;AAAA,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}