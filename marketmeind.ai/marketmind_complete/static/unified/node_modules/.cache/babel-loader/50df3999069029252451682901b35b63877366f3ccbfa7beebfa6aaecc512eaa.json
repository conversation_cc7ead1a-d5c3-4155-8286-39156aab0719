{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M11 20v-3H7v5h10v-5h-4.4L11 20z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v13h4v-2.5H9L13 7v5.5h2L12.6 17H17V4z\"\n  })]\n}), 'BatteryCharging20Sharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "children", "d", "fillOpacity"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/node_modules/@mui/icons-material/esm/BatteryCharging20Sharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M11 20v-3H7v5h10v-5h-4.4L11 20z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v13h4v-2.5H9L13 7v5.5h2L12.6 17H17V4z\"\n  })]\n}), 'BatteryCharging20Sharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,eAAeJ,aAAa,CAAE,aAAaI,KAAK,CAACL,KAAK,CAACM,QAAQ,EAAE;EAC/DC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCK,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAaL,IAAI,CAAC,MAAM,EAAE;IAC5BM,WAAW,EAAE,IAAI;IACjBD,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}