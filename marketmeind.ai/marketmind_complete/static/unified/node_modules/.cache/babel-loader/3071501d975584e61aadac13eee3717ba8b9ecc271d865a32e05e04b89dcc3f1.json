{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Typography, Button, Alert, Chip, Card, CardContent } from '@mui/material';\nimport { People as PeopleIcon, Business as BusinessIcon, Campaign as CampaignIcon, Computer as SystemIcon, Settings as SettingsIcon, Analytics as AnalyticsIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../shared/contexts/AuthContext';\nimport StatsCard from '../../shared/components/ui/StatsCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHome = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setStats({\n        totalUsers: 1250,\n        totalCompanies: 85,\n        activeCampaigns: 342,\n        systemHealth: 98\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"\\u0644\\u0648\\u062D\\u0629 \\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0631\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"\\u0646\\u0638\\u0631\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646 \\u0648\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\",\n          value: stats.totalUsers,\n          subtitle: \"\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 19\n          }, this),\n          color: \"primary\",\n          loading: loading,\n          trend: \"\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0634\\u0647\\u0631\",\n          trendValue: 12.5\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629\",\n          value: stats.totalCompanies,\n          subtitle: \"\\u0634\\u0631\\u0643\\u0629\",\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 19\n          }, this),\n          color: \"secondary\",\n          loading: loading,\n          trend: \"\\u0646\\u0645\\u0648 \\u0634\\u0647\\u0631\\u064A\",\n          trendValue: 8.3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0627\\u0644\\u062D\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\",\n          value: stats.activeCampaigns,\n          subtitle: \"\\u062D\\u0645\\u0644\\u0629\",\n          icon: /*#__PURE__*/_jsxDEV(CampaignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 19\n          }, this),\n          color: \"success\",\n          loading: loading,\n          trend: \"\\u0627\\u0644\\u064A\\u0648\\u0645\",\n          trendValue: 5.7\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0635\\u062D\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n          value: `${stats.systemHealth}%`,\n          subtitle: \"\\u0645\\u062A\\u0627\\u062D\",\n          icon: /*#__PURE__*/_jsxDEV(SystemIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 19\n          }, this),\n          color: \"info\",\n          loading: loading,\n          progress: stats.systemHealth,\n          progressColor: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"\\u0623\\u062F\\u0648\\u0627\\u062A \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      children: \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0645\\u0632\\u064A\\u062F \\u0645\\u0646 \\u0623\\u062F\\u0648\\u0627\\u062A \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u0627\\u0644\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHome, \"HNFZnTV1NBtFiw28AV6hxjXzzy0=\");\n_c = AdminHome;\nexport default AdminHome;\nvar _c;\n$RefreshReg$(_c, \"AdminHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "Card", "<PERSON><PERSON><PERSON><PERSON>", "People", "PeopleIcon", "Business", "BusinessIcon", "Campaign", "CampaignIcon", "Computer", "SystemIcon", "Settings", "SettingsIcon", "Analytics", "AnalyticsIcon", "Security", "SecurityIcon", "useNavigate", "useAuth", "StatsCard", "jsxDEV", "_jsxDEV", "AdminHome", "_s", "loading", "setLoading", "stats", "setStats", "setTimeout", "totalUsers", "totalCompanies", "activeCampaigns", "systemHealth", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "paragraph", "container", "spacing", "sx", "mb", "item", "xs", "sm", "md", "title", "value", "subtitle", "icon", "trend", "trendValue", "progress", "progressColor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  But<PERSON>,\n  Alert,\n  Chip,\n  Card,\n  CardContent\n} from '@mui/material';\nimport {\n  People as PeopleIcon,\n  Business as BusinessIcon,\n  Campaign as CampaignIcon,\n  Computer as SystemIcon,\n  Settings as SettingsIcon,\n  Analytics as AnalyticsIcon,\n  Security as SecurityIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../shared/contexts/AuthContext';\nimport StatsCard from '../../shared/components/ui/StatsCard';\n\nconst AdminHome = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({});\n\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setStats({\n        totalUsers: 1250,\n        totalCompanies: 85,\n        activeCampaigns: 342,\n        systemHealth: 98\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        لوحة تحكم المدير\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        نظرة شاملة على النظام والمستخدمين والشركات\n      </Typography>\n\n      {/* إحصائيات النظام */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"إجمالي المستخدمين\"\n            value={stats.totalUsers}\n            subtitle=\"مستخدم\"\n            icon={<PeopleIcon />}\n            color=\"primary\"\n            loading={loading}\n            trend=\"هذا الشهر\"\n            trendValue={12.5}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"الشركات المسجلة\"\n            value={stats.totalCompanies}\n            subtitle=\"شركة\"\n            icon={<BusinessIcon />}\n            color=\"secondary\"\n            loading={loading}\n            trend=\"نمو شهري\"\n            trendValue={8.3}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"الحملات النشطة\"\n            value={stats.activeCampaigns}\n            subtitle=\"حملة\"\n            icon={<CampaignIcon />}\n            color=\"success\"\n            loading={loading}\n            trend=\"اليوم\"\n            trendValue={5.7}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <StatsCard\n            title=\"صحة النظام\"\n            value={`${stats.systemHealth}%`}\n            subtitle=\"متاح\"\n            icon={<SystemIcon />}\n            color=\"info\"\n            loading={loading}\n            progress={stats.systemHealth}\n            progressColor=\"success\"\n          />\n        </Grid>\n      </Grid>\n\n      <Typography variant=\"h5\" gutterBottom>\n        أدوات الإدارة\n      </Typography>\n      <Typography variant=\"body2\" color=\"text.secondary\">\n        سيتم إضافة المزيد من أدوات الإدارة والتحليلات قريباً...\n      </Typography>\n    </Box>\n  );\n};\n\nexport default AdminHome;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,UAAU,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,OAAOC,SAAS,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd;IACAkC,UAAU,CAAC,MAAM;MACfD,QAAQ,CAAC;QACPE,UAAU,EAAE,IAAI;QAChBC,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,GAAG;QACpBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEJ,OAAA,CAAC1B,GAAG;IAAAsC,QAAA,gBACFZ,OAAA,CAACxB,UAAU;MAACqC,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAH,QAAA,EAAC;IAErD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbnB,OAAA,CAACxB,UAAU;MAACqC,OAAO,EAAC,OAAO;MAACO,KAAK,EAAC,gBAAgB;MAACC,SAAS;MAAAT,QAAA,EAAC;IAE7D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbnB,OAAA,CAACzB,IAAI;MAAC+C,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAb,QAAA,gBACxCZ,OAAA,CAACzB,IAAI;QAACmD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAC9BZ,OAAA,CAACF,SAAS;UACRgC,KAAK,EAAC,mGAAmB;UACzBC,KAAK,EAAE1B,KAAK,CAACG,UAAW;UACxBwB,QAAQ,EAAC,sCAAQ;UACjBC,IAAI,eAAEjC,OAAA,CAACjB,UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBC,KAAK,EAAC,SAAS;UACfjB,OAAO,EAAEA,OAAQ;UACjB+B,KAAK,EAAC,mDAAW;UACjBC,UAAU,EAAE;QAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPnB,OAAA,CAACzB,IAAI;QAACmD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAC9BZ,OAAA,CAACF,SAAS;UACRgC,KAAK,EAAC,uFAAiB;UACvBC,KAAK,EAAE1B,KAAK,CAACI,cAAe;UAC5BuB,QAAQ,EAAC,0BAAM;UACfC,IAAI,eAAEjC,OAAA,CAACf,YAAY;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,KAAK,EAAC,WAAW;UACjBjB,OAAO,EAAEA,OAAQ;UACjB+B,KAAK,EAAC,6CAAU;UAChBC,UAAU,EAAE;QAAI;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPnB,OAAA,CAACzB,IAAI;QAACmD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAC9BZ,OAAA,CAACF,SAAS;UACRgC,KAAK,EAAC,iFAAgB;UACtBC,KAAK,EAAE1B,KAAK,CAACK,eAAgB;UAC7BsB,QAAQ,EAAC,0BAAM;UACfC,IAAI,eAAEjC,OAAA,CAACb,YAAY;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,KAAK,EAAC,SAAS;UACfjB,OAAO,EAAEA,OAAQ;UACjB+B,KAAK,EAAC,gCAAO;UACbC,UAAU,EAAE;QAAI;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPnB,OAAA,CAACzB,IAAI;QAACmD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAC9BZ,OAAA,CAACF,SAAS;UACRgC,KAAK,EAAC,yDAAY;UAClBC,KAAK,EAAE,GAAG1B,KAAK,CAACM,YAAY,GAAI;UAChCqB,QAAQ,EAAC,0BAAM;UACfC,IAAI,eAAEjC,OAAA,CAACX,UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBC,KAAK,EAAC,MAAM;UACZjB,OAAO,EAAEA,OAAQ;UACjBiC,QAAQ,EAAE/B,KAAK,CAACM,YAAa;UAC7B0B,aAAa,EAAC;QAAS;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPnB,OAAA,CAACxB,UAAU;MAACqC,OAAO,EAAC,IAAI;MAACE,YAAY;MAAAH,QAAA,EAAC;IAEtC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbnB,OAAA,CAACxB,UAAU;MAACqC,OAAO,EAAC,OAAO;MAACO,KAAK,EAAC,gBAAgB;MAAAR,QAAA,EAAC;IAEnD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACjB,EAAA,CAtFID,SAAS;AAAAqC,EAAA,GAATrC,SAAS;AAwFf,eAAeA,SAAS;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}