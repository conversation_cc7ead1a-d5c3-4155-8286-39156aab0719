{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/CompanyManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Typography, Button, TextField, InputAdornment, Chip, IconButton, Tooltip, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, Alert, Avatar, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, FormControl, InputLabel, Select, Card, CardContent } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, MoreVert as MoreIcon, Edit as EditIcon, Delete as DeleteIcon, Block as BlockIcon, CheckCircle as ActivateIcon, Email as EmailIcon, Download as DownloadIcon, Business as BusinessIcon, People as PeopleIcon, Campaign as CampaignIcon, AttachMoney as MoneyIcon, FilterList as FilterIcon, Visibility as ViewIcon } from '@mui/icons-material';\nimport { PageContainer, PageSection, UnifiedCard, UnifiedCardContent, StatsCard, StatsGrid } from '../../shared/components/ui';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CompanyManagement = () => {\n  _s();\n  const [companies, setCompanies] = useState([]);\n  const [filteredCompanies, setFilteredCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [filterSize, setFilterSize] = useState('all');\n\n  // بيانات تجريبية للشركات\n  const sampleCompanies = [{\n    id: 1,\n    name: 'شركة التقنية المتقدمة',\n    industry: 'تكنولوجيا',\n    size: 'large',\n    status: 'active',\n    ownerId: 2,\n    ownerName: 'فاطمة علي',\n    ownerEmail: '<EMAIL>',\n    employees: 150,\n    activeCampaigns: 12,\n    totalSpent: 45000,\n    joinDate: '2023-03-10',\n    lastActivity: '2024-01-22',\n    subscription: 'premium',\n    location: 'الرياض'\n  }, {\n    id: 2,\n    name: 'مؤسسة الابتكار',\n    industry: 'استشارات',\n    size: 'medium',\n    status: 'active',\n    ownerId: 4,\n    ownerName: 'سارة أحمد',\n    ownerEmail: '<EMAIL>',\n    employees: 45,\n    activeCampaigns: 8,\n    totalSpent: 22000,\n    joinDate: '2023-06-15',\n    lastActivity: '2024-01-21',\n    subscription: 'standard',\n    location: 'جدة'\n  }, {\n    id: 3,\n    name: 'شركة التسويق الرقمي',\n    industry: 'تسويق',\n    size: 'small',\n    status: 'pending',\n    ownerId: 6,\n    ownerName: 'محمد خالد',\n    ownerEmail: '<EMAIL>',\n    employees: 15,\n    activeCampaigns: 0,\n    totalSpent: 0,\n    joinDate: '2024-01-20',\n    lastActivity: null,\n    subscription: 'basic',\n    location: 'الدمام'\n  }, {\n    id: 4,\n    name: 'مجموعة الأعمال المتكاملة',\n    industry: 'متنوع',\n    size: 'large',\n    status: 'suspended',\n    ownerId: 8,\n    ownerName: 'أحمد السعيد',\n    ownerEmail: '<EMAIL>',\n    employees: 300,\n    activeCampaigns: 0,\n    totalSpent: 78000,\n    joinDate: '2023-01-15',\n    lastActivity: '2023-12-10',\n    subscription: 'enterprise',\n    location: 'الرياض'\n  }];\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setCompanies(sampleCompanies);\n      setFilteredCompanies(sampleCompanies);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  useEffect(() => {\n    // تطبيق الفلاتر والبحث\n    let filtered = companies;\n    if (searchTerm) {\n      filtered = filtered.filter(company => company.name.toLowerCase().includes(searchTerm.toLowerCase()) || company.industry.toLowerCase().includes(searchTerm.toLowerCase()) || company.ownerName.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(company => company.status === filterStatus);\n    }\n    if (filterSize !== 'all') {\n      filtered = filtered.filter(company => company.size === filterSize);\n    }\n    setFilteredCompanies(filtered);\n    setPage(0);\n  }, [searchTerm, filterStatus, filterSize, companies]);\n  const handleMenuOpen = (event, company) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedCompany(company);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedCompany(null);\n  };\n  const handleStatusChange = (companyId, newStatus) => {\n    setCompanies(prev => prev.map(company => company.id === companyId ? {\n      ...company,\n      status: newStatus\n    } : company));\n    handleMenuClose();\n  };\n  const handleDeleteCompany = () => {\n    setCompanies(prev => prev.filter(company => company.id !== selectedCompany.id));\n    setDeleteDialogOpen(false);\n    handleMenuClose();\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'suspended':\n        return 'error';\n      case 'inactive':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'active':\n        return 'نشطة';\n      case 'pending':\n        return 'في الانتظار';\n      case 'suspended':\n        return 'موقوفة';\n      case 'inactive':\n        return 'غير نشطة';\n      default:\n        return status;\n    }\n  };\n  const getSizeText = size => {\n    switch (size) {\n      case 'small':\n        return 'صغيرة';\n      case 'medium':\n        return 'متوسطة';\n      case 'large':\n        return 'كبيرة';\n      default:\n        return size;\n    }\n  };\n  const getSizeColor = size => {\n    switch (size) {\n      case 'small':\n        return 'info';\n      case 'medium':\n        return 'warning';\n      case 'large':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getSubscriptionText = subscription => {\n    switch (subscription) {\n      case 'basic':\n        return 'أساسي';\n      case 'standard':\n        return 'قياسي';\n      case 'premium':\n        return 'مميز';\n      case 'enterprise':\n        return 'مؤسسي';\n      default:\n        return subscription;\n    }\n  };\n\n  // حساب الإحصائيات\n  const stats = {\n    totalCompanies: companies.length,\n    activeCompanies: companies.filter(c => c.status === 'active').length,\n    pendingCompanies: companies.filter(c => c.status === 'pending').length,\n    totalRevenue: companies.reduce((sum, c) => sum + c.totalSpent, 0)\n  };\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\",\n    subtitle: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u0645\\u0631\\u0627\\u0642\\u0628\\u0629 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062C\\u0644\\u0629 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n    breadcrumbs: [{\n      label: 'لوحة التحكم',\n      href: '/dashboard/admin'\n    }, {\n      label: 'إدارة الشركات'\n    }],\n    actions: [/*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 22\n      }, this),\n      children: \"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n    }, \"export\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 9\n    }, this)],\n    children: [/*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629\",\n      children: /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\",\n          value: stats.totalCompanies,\n          change: `${stats.activeCompanies} نشطة`,\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629\",\n          value: stats.activeCompanies,\n          change: `${(stats.activeCompanies / stats.totalCompanies * 100).toFixed(0)}% من الإجمالي`,\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(ActivateIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0641\\u064A \\u0627\\u0646\\u062A\\u0638\\u0627\\u0631 \\u0627\\u0644\\u0645\\u0648\\u0627\\u0641\\u0642\\u0629\",\n          value: stats.pendingCompanies,\n          change: \"\\u064A\\u062D\\u062A\\u0627\\u062C \\u0645\\u0631\\u0627\\u062C\\u0639\\u0629\",\n          changeType: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 19\n          }, this),\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\",\n          value: `${stats.totalRevenue.toLocaleString()} ر.س`,\n          change: \"\\u0645\\u0646 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\",\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(MoneyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 19\n          }, this),\n          color: \"secondary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0648\\u0627\\u0644\\u0641\\u0644\\u062A\\u0631\\u0629\",\n      children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                placeholder: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0634\\u0631\\u0643\\u0629...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"start\",\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: filterStatus,\n                  onChange: e => setFilterStatus(e.target.value),\n                  label: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062D\\u0627\\u0644\\u0627\\u062A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"active\",\n                    children: \"\\u0646\\u0634\\u0637\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"pending\",\n                    children: \"\\u0641\\u064A \\u0627\\u0644\\u0627\\u0646\\u062A\\u0638\\u0627\\u0631\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"suspended\",\n                    children: \"\\u0645\\u0648\\u0642\\u0648\\u0641\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"inactive\",\n                    children: \"\\u063A\\u064A\\u0631 \\u0646\\u0634\\u0637\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u062D\\u062C\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: filterSize,\n                  onChange: e => setFilterSize(e.target.value),\n                  label: \"\\u062D\\u062C\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"all\",\n                    children: \"\\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u062D\\u062C\\u0627\\u0645\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"small\",\n                    children: \"\\u0635\\u063A\\u064A\\u0631\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"medium\",\n                    children: \"\\u0645\\u062A\\u0648\\u0633\\u0637\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"large\",\n                    children: \"\\u0643\\u0628\\u064A\\u0631\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 2,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 30\n                }, this),\n                fullWidth: true,\n                children: \"\\u0641\\u0644\\u0627\\u062A\\u0631 \\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\",\n      children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n        children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n          children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u0645\\u0627\\u0644\\u0643\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u062D\\u062C\\u0645\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u0648\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u062D\\u0645\\u0644\\u0627\\u062A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u0625\\u0646\\u0641\\u0627\\u0642\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: filteredCompanies.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(company => /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        children: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"bold\",\n                          children: company.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 413,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [company.industry, \" \\u2022 \", company.location]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 416,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: company.ownerName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 424,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: company.ownerEmail\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: getStatusText(company.status),\n                      color: getStatusColor(company.status),\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: getSizeText(company.size),\n                      color: getSizeColor(company.size),\n                      variant: \"outlined\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: company.employees\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: company.activeCampaigns\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: [company.totalSpent.toLocaleString(), \" \\u0631.\\u0633\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: e => handleMenuOpen(e, company),\n                      children: /*#__PURE__*/_jsxDEV(MoreIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this)]\n                }, company.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n            component: \"div\",\n            count: filteredCompanies.length,\n            page: page,\n            onPageChange: (e, newPage) => setPage(newPage),\n            rowsPerPage: rowsPerPage,\n            onRowsPerPageChange: e => setRowsPerPage(parseInt(e.target.value, 10)),\n            labelRowsPerPage: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641 \\u0641\\u064A \\u0627\\u0644\\u0635\\u0641\\u062D\\u0629:\",\n            labelDisplayedRows: ({\n              from,\n              to,\n              count\n            }) => `${from}-${to} من ${count}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setDetailsDialogOpen(true),\n        children: [/*#__PURE__*/_jsxDEV(ViewIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(EditIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), \"\\u062A\\u0639\\u062F\\u064A\\u0644\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), \"\\u0625\\u0631\\u0633\\u0627\\u0644 \\u0631\\u0633\\u0627\\u0644\\u0629\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), (selectedCompany === null || selectedCompany === void 0 ? void 0 : selectedCompany.status) === 'active' ? /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleStatusChange(selectedCompany.id, 'suspended'),\n        children: [/*#__PURE__*/_jsxDEV(BlockIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), \"\\u0625\\u064A\\u0642\\u0627\\u0641 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleStatusChange(selectedCompany.id, 'active'),\n        children: [/*#__PURE__*/_jsxDEV(ActivateIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), \"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), (selectedCompany === null || selectedCompany === void 0 ? void 0 : selectedCompany.status) === 'pending' && /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => handleStatusChange(selectedCompany.id, 'active'),\n        children: [/*#__PURE__*/_jsxDEV(ActivateIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this), \"\\u0627\\u0644\\u0645\\u0648\\u0627\\u0641\\u0642\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setDeleteDialogOpen(true),\n        sx: {\n          color: 'error.main'\n        },\n        children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), \"\\u062D\\u0630\\u0641\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: () => setDetailsDialogOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCompany && /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0627\\u0633\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0635\\u0646\\u0627\\u0639\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.industry]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u062D\\u062C\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 33\n                  }, this), \" \", getSizeText(selectedCompany.size)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.employees]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"\\u0627\\u0644\\u0623\\u062F\\u0627\\u0621 \\u0648\\u0627\\u0644\\u0646\\u0634\\u0627\\u0637\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u062D\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u0646\\u0634\\u0637\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.activeCampaigns]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u0646\\u0641\\u0627\\u0642:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.totalSpent.toLocaleString(), \" \\u0631.\\u0633\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0627\\u0634\\u062A\\u0631\\u0627\\u0643:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 33\n                  }, this), \" \", getSubscriptionText(selectedCompany.subscription)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0627\\u0646\\u0636\\u0645\\u0627\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.joinDate]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0622\\u062E\\u0631 \\u0646\\u0634\\u0627\\u0637:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 33\n                  }, this), \" \", selectedCompany.lastActivity || 'لا يوجد']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDetailsDialogOpen(false),\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u062D\\u0630\\u0641\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u062D\\u0630\\u0641 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 \\\"\", selectedCompany === null || selectedCompany === void 0 ? void 0 : selectedCompany.name, \"\\\"\\u061F \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621 \\u0644\\u0627 \\u064A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u062A\\u0631\\u0627\\u062C\\u0639 \\u0639\\u0646\\u0647 \\u0648\\u0633\\u064A\\u062A\\u0645 \\u062D\\u0630\\u0641 \\u062C\\u0645\\u064A\\u0639 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\\u0647\\u0627 \\u0648\\u062D\\u0645\\u0644\\u0627\\u062A\\u0647\\u0627.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCompany,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"\\u062D\\u0630\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_s(CompanyManagement, \"c9ZcrOwzglelOkGSkpfG7GYYq+8=\");\n_c = CompanyManagement;\nexport default CompanyManagement;\nvar _c;\n$RefreshReg$(_c, \"CompanyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Typography", "<PERSON><PERSON>", "TextField", "InputAdornment", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Avatar", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "FormControl", "InputLabel", "Select", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Add", "AddIcon", "Search", "SearchIcon", "<PERSON><PERSON><PERSON>", "MoreIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Block", "BlockIcon", "CheckCircle", "ActivateIcon", "Email", "EmailIcon", "Download", "DownloadIcon", "Business", "BusinessIcon", "People", "PeopleIcon", "Campaign", "CampaignIcon", "AttachMoney", "MoneyIcon", "FilterList", "FilterIcon", "Visibility", "ViewIcon", "<PERSON><PERSON><PERSON><PERSON>", "PageSection", "UnifiedCard", "UnifiedCardContent", "StatsCard", "StatsGrid", "jsxDEV", "_jsxDEV", "CompanyManagement", "_s", "companies", "setCompanies", "filteredCompanies", "setFilteredCompanies", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCompany", "setSelectedCompany", "anchorEl", "setAnchorEl", "deleteDialogOpen", "setDeleteDialogOpen", "detailsDialogOpen", "setDetailsDialogOpen", "page", "setPage", "rowsPerPage", "setRowsPerPage", "filterStatus", "setFilterStatus", "filterSize", "setFilterSize", "sampleCompanies", "id", "name", "industry", "size", "status", "ownerId", "ownerName", "ownerEmail", "employees", "activeCampaigns", "totalSpent", "joinDate", "lastActivity", "subscription", "location", "setTimeout", "filtered", "filter", "company", "toLowerCase", "includes", "handleMenuOpen", "event", "currentTarget", "handleMenuClose", "handleStatusChange", "companyId", "newStatus", "prev", "map", "handleDeleteCompany", "getStatusColor", "getStatusText", "getSizeText", "getSizeColor", "getSubscriptionText", "stats", "totalCompanies", "length", "activeCompanies", "c", "pendingCompanies", "totalRevenue", "reduce", "sum", "title", "subtitle", "breadcrumbs", "label", "href", "actions", "variant", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "value", "change", "changeType", "icon", "color", "toFixed", "toLocaleString", "container", "spacing", "alignItems", "item", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "slice", "hover", "display", "gap", "fontWeight", "onClick", "component", "count", "onPageChange", "newPage", "onRowsPerPageChange", "parseInt", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "open", "Boolean", "onClose", "sx", "mr", "max<PERSON><PERSON><PERSON>", "gutterBottom", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/CompanyManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Button,\n  TextField,\n  InputAdornment,\n  Chip,\n  IconButton,\n  Tooltip,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  Avatar,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TablePagination,\n  FormControl,\n  InputLabel,\n  Select,\n  Card,\n  CardContent\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  MoreVert as MoreIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Block as BlockIcon,\n  CheckCircle as ActivateIcon,\n  Email as EmailIcon,\n  Download as DownloadIcon,\n  Business as BusinessIcon,\n  People as PeopleIcon,\n  Campaign as CampaignIcon,\n  AttachMoney as MoneyIcon,\n  FilterList as FilterIcon,\n  Visibility as ViewIcon\n} from '@mui/icons-material';\nimport { \n  PageContainer, \n  PageSection,\n  UnifiedCard, \n  UnifiedCardContent,\n  StatsCard,\n  StatsGrid\n} from '../../shared/components/ui';\n\nconst CompanyManagement = () => {\n  const [companies, setCompanies] = useState([]);\n  const [filteredCompanies, setFilteredCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCompany, setSelectedCompany] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [filterSize, setFilterSize] = useState('all');\n\n  // بيانات تجريبية للشركات\n  const sampleCompanies = [\n    {\n      id: 1,\n      name: 'شركة التقنية المتقدمة',\n      industry: 'تكنولوجيا',\n      size: 'large',\n      status: 'active',\n      ownerId: 2,\n      ownerName: 'فاطمة علي',\n      ownerEmail: '<EMAIL>',\n      employees: 150,\n      activeCampaigns: 12,\n      totalSpent: 45000,\n      joinDate: '2023-03-10',\n      lastActivity: '2024-01-22',\n      subscription: 'premium',\n      location: 'الرياض'\n    },\n    {\n      id: 2,\n      name: 'مؤسسة الابتكار',\n      industry: 'استشارات',\n      size: 'medium',\n      status: 'active',\n      ownerId: 4,\n      ownerName: 'سارة أحمد',\n      ownerEmail: '<EMAIL>',\n      employees: 45,\n      activeCampaigns: 8,\n      totalSpent: 22000,\n      joinDate: '2023-06-15',\n      lastActivity: '2024-01-21',\n      subscription: 'standard',\n      location: 'جدة'\n    },\n    {\n      id: 3,\n      name: 'شركة التسويق الرقمي',\n      industry: 'تسويق',\n      size: 'small',\n      status: 'pending',\n      ownerId: 6,\n      ownerName: 'محمد خالد',\n      ownerEmail: '<EMAIL>',\n      employees: 15,\n      activeCampaigns: 0,\n      totalSpent: 0,\n      joinDate: '2024-01-20',\n      lastActivity: null,\n      subscription: 'basic',\n      location: 'الدمام'\n    },\n    {\n      id: 4,\n      name: 'مجموعة الأعمال المتكاملة',\n      industry: 'متنوع',\n      size: 'large',\n      status: 'suspended',\n      ownerId: 8,\n      ownerName: 'أحمد السعيد',\n      ownerEmail: '<EMAIL>',\n      employees: 300,\n      activeCampaigns: 0,\n      totalSpent: 78000,\n      joinDate: '2023-01-15',\n      lastActivity: '2023-12-10',\n      subscription: 'enterprise',\n      location: 'الرياض'\n    }\n  ];\n\n  useEffect(() => {\n    // محاكاة تحميل البيانات\n    setTimeout(() => {\n      setCompanies(sampleCompanies);\n      setFilteredCompanies(sampleCompanies);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  useEffect(() => {\n    // تطبيق الفلاتر والبحث\n    let filtered = companies;\n\n    if (searchTerm) {\n      filtered = filtered.filter(company =>\n        company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        company.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        company.ownerName.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(company => company.status === filterStatus);\n    }\n\n    if (filterSize !== 'all') {\n      filtered = filtered.filter(company => company.size === filterSize);\n    }\n\n    setFilteredCompanies(filtered);\n    setPage(0);\n  }, [searchTerm, filterStatus, filterSize, companies]);\n\n  const handleMenuOpen = (event, company) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedCompany(company);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedCompany(null);\n  };\n\n  const handleStatusChange = (companyId, newStatus) => {\n    setCompanies(prev => \n      prev.map(company => \n        company.id === companyId \n          ? { ...company, status: newStatus }\n          : company\n      )\n    );\n    handleMenuClose();\n  };\n\n  const handleDeleteCompany = () => {\n    setCompanies(prev => \n      prev.filter(company => company.id !== selectedCompany.id)\n    );\n    setDeleteDialogOpen(false);\n    handleMenuClose();\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active': return 'success';\n      case 'pending': return 'warning';\n      case 'suspended': return 'error';\n      case 'inactive': return 'default';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'active': return 'نشطة';\n      case 'pending': return 'في الانتظار';\n      case 'suspended': return 'موقوفة';\n      case 'inactive': return 'غير نشطة';\n      default: return status;\n    }\n  };\n\n  const getSizeText = (size) => {\n    switch (size) {\n      case 'small': return 'صغيرة';\n      case 'medium': return 'متوسطة';\n      case 'large': return 'كبيرة';\n      default: return size;\n    }\n  };\n\n  const getSizeColor = (size) => {\n    switch (size) {\n      case 'small': return 'info';\n      case 'medium': return 'warning';\n      case 'large': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getSubscriptionText = (subscription) => {\n    switch (subscription) {\n      case 'basic': return 'أساسي';\n      case 'standard': return 'قياسي';\n      case 'premium': return 'مميز';\n      case 'enterprise': return 'مؤسسي';\n      default: return subscription;\n    }\n  };\n\n  // حساب الإحصائيات\n  const stats = {\n    totalCompanies: companies.length,\n    activeCompanies: companies.filter(c => c.status === 'active').length,\n    pendingCompanies: companies.filter(c => c.status === 'pending').length,\n    totalRevenue: companies.reduce((sum, c) => sum + c.totalSpent, 0)\n  };\n\n  return (\n    <PageContainer\n      title=\"إدارة الشركات\"\n      subtitle=\"إدارة ومراقبة جميع الشركات المسجلة في النظام\"\n      breadcrumbs={[\n        { label: 'لوحة التحكم', href: '/dashboard/admin' },\n        { label: 'إدارة الشركات' }\n      ]}\n      actions={[\n        <Button\n          key=\"export\"\n          variant=\"outlined\"\n          startIcon={<DownloadIcon />}\n        >\n          تصدير البيانات\n        </Button>\n      ]}\n    >\n      {/* الإحصائيات */}\n      <PageSection title=\"نظرة عامة\">\n        <StatsGrid>\n          <StatsCard\n            title=\"إجمالي الشركات\"\n            value={stats.totalCompanies}\n            change={`${stats.activeCompanies} نشطة`}\n            changeType=\"positive\"\n            icon={<BusinessIcon />}\n            color=\"primary\"\n          />\n          <StatsCard\n            title=\"الشركات النشطة\"\n            value={stats.activeCompanies}\n            change={`${((stats.activeCompanies / stats.totalCompanies) * 100).toFixed(0)}% من الإجمالي`}\n            changeType=\"positive\"\n            icon={<ActivateIcon />}\n            color=\"success\"\n          />\n          <StatsCard\n            title=\"في انتظار الموافقة\"\n            value={stats.pendingCompanies}\n            change=\"يحتاج مراجعة\"\n            changeType=\"warning\"\n            icon={<FilterIcon />}\n            color=\"warning\"\n          />\n          <StatsCard\n            title=\"إجمالي الإيرادات\"\n            value={`${stats.totalRevenue.toLocaleString()} ر.س`}\n            change=\"من جميع الشركات\"\n            changeType=\"positive\"\n            icon={<MoneyIcon />}\n            color=\"secondary\"\n          />\n        </StatsGrid>\n      </PageSection>\n\n      {/* أدوات البحث والفلترة */}\n      <PageSection title=\"البحث والفلترة\">\n        <UnifiedCard>\n          <UnifiedCardContent>\n            <Grid container spacing={3} alignItems=\"center\">\n              <Grid item xs={12} md={4}>\n                <TextField\n                  fullWidth\n                  placeholder=\"البحث عن شركة...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  InputProps={{\n                    startAdornment: (\n                      <InputAdornment position=\"start\">\n                        <SearchIcon />\n                      </InputAdornment>\n                    )\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth>\n                  <InputLabel>الحالة</InputLabel>\n                  <Select\n                    value={filterStatus}\n                    onChange={(e) => setFilterStatus(e.target.value)}\n                    label=\"الحالة\"\n                  >\n                    <MenuItem value=\"all\">جميع الحالات</MenuItem>\n                    <MenuItem value=\"active\">نشطة</MenuItem>\n                    <MenuItem value=\"pending\">في الانتظار</MenuItem>\n                    <MenuItem value=\"suspended\">موقوفة</MenuItem>\n                    <MenuItem value=\"inactive\">غير نشطة</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth>\n                  <InputLabel>حجم الشركة</InputLabel>\n                  <Select\n                    value={filterSize}\n                    onChange={(e) => setFilterSize(e.target.value)}\n                    label=\"حجم الشركة\"\n                  >\n                    <MenuItem value=\"all\">جميع الأحجام</MenuItem>\n                    <MenuItem value=\"small\">صغيرة</MenuItem>\n                    <MenuItem value=\"medium\">متوسطة</MenuItem>\n                    <MenuItem value=\"large\">كبيرة</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} md={2}>\n                <Button\n                  variant=\"outlined\"\n                  startIcon={<FilterIcon />}\n                  fullWidth\n                >\n                  فلاتر متقدمة\n                </Button>\n              </Grid>\n            </Grid>\n          </UnifiedCardContent>\n        </UnifiedCard>\n      </PageSection>\n\n      {/* جدول الشركات */}\n      <PageSection title=\"قائمة الشركات\">\n        <UnifiedCard>\n          <UnifiedCardContent>\n            <TableContainer>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>الشركة</TableCell>\n                    <TableCell>المالك</TableCell>\n                    <TableCell>الحالة</TableCell>\n                    <TableCell>الحجم</TableCell>\n                    <TableCell>الموظفون</TableCell>\n                    <TableCell>الحملات</TableCell>\n                    <TableCell>الإنفاق</TableCell>\n                    <TableCell>الإجراءات</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {filteredCompanies\n                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)\n                    .map((company) => (\n                      <TableRow key={company.id} hover>\n                        <TableCell>\n                          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                            <Avatar>\n                              <BusinessIcon />\n                            </Avatar>\n                            <Box>\n                              <Typography variant=\"body2\" fontWeight=\"bold\">\n                                {company.name}\n                              </Typography>\n                              <Typography variant=\"caption\" color=\"text.secondary\">\n                                {company.industry} • {company.location}\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          <Box>\n                            <Typography variant=\"body2\">\n                              {company.ownerName}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {company.ownerEmail}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          <Chip\n                            label={getStatusText(company.status)}\n                            color={getStatusColor(company.status)}\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <Chip\n                            label={getSizeText(company.size)}\n                            color={getSizeColor(company.size)}\n                            variant=\"outlined\"\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>{company.employees}</TableCell>\n                        <TableCell>{company.activeCampaigns}</TableCell>\n                        <TableCell>{company.totalSpent.toLocaleString()} ر.س</TableCell>\n                        <TableCell>\n                          <IconButton onClick={(e) => handleMenuOpen(e, company)}>\n                            <MoreIcon />\n                          </IconButton>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n\n            <TablePagination\n              component=\"div\"\n              count={filteredCompanies.length}\n              page={page}\n              onPageChange={(e, newPage) => setPage(newPage)}\n              rowsPerPage={rowsPerPage}\n              onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}\n              labelRowsPerPage=\"عدد الصفوف في الصفحة:\"\n              labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}\n            />\n          </UnifiedCardContent>\n        </UnifiedCard>\n      </PageSection>\n\n      {/* قائمة الإجراءات */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleMenuClose}\n      >\n        <MenuItem onClick={() => setDetailsDialogOpen(true)}>\n          <ViewIcon sx={{ mr: 1 }} />\n          عرض التفاصيل\n        </MenuItem>\n        <MenuItem onClick={handleMenuClose}>\n          <EditIcon sx={{ mr: 1 }} />\n          تعديل\n        </MenuItem>\n        <MenuItem onClick={handleMenuClose}>\n          <EmailIcon sx={{ mr: 1 }} />\n          إرسال رسالة\n        </MenuItem>\n        {selectedCompany?.status === 'active' ? (\n          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'suspended')}>\n            <BlockIcon sx={{ mr: 1 }} />\n            إيقاف الشركة\n          </MenuItem>\n        ) : (\n          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'active')}>\n            <ActivateIcon sx={{ mr: 1 }} />\n            تفعيل الشركة\n          </MenuItem>\n        )}\n        {selectedCompany?.status === 'pending' && (\n          <MenuItem onClick={() => handleStatusChange(selectedCompany.id, 'active')}>\n            <ActivateIcon sx={{ mr: 1 }} />\n            الموافقة على الشركة\n          </MenuItem>\n        )}\n        <MenuItem \n          onClick={() => setDeleteDialogOpen(true)}\n          sx={{ color: 'error.main' }}\n        >\n          <DeleteIcon sx={{ mr: 1 }} />\n          حذف\n        </MenuItem>\n      </Menu>\n\n      {/* حوار تفاصيل الشركة */}\n      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>تفاصيل الشركة</DialogTitle>\n        <DialogContent>\n          {selectedCompany && (\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      معلومات أساسية\n                    </Typography>\n                    <Typography><strong>الاسم:</strong> {selectedCompany.name}</Typography>\n                    <Typography><strong>الصناعة:</strong> {selectedCompany.industry}</Typography>\n                    <Typography><strong>الموقع:</strong> {selectedCompany.location}</Typography>\n                    <Typography><strong>الحجم:</strong> {getSizeText(selectedCompany.size)}</Typography>\n                    <Typography><strong>عدد الموظفين:</strong> {selectedCompany.employees}</Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      الأداء والنشاط\n                    </Typography>\n                    <Typography><strong>الحملات النشطة:</strong> {selectedCompany.activeCampaigns}</Typography>\n                    <Typography><strong>إجمالي الإنفاق:</strong> {selectedCompany.totalSpent.toLocaleString()} ر.س</Typography>\n                    <Typography><strong>نوع الاشتراك:</strong> {getSubscriptionText(selectedCompany.subscription)}</Typography>\n                    <Typography><strong>تاريخ الانضمام:</strong> {selectedCompany.joinDate}</Typography>\n                    <Typography><strong>آخر نشاط:</strong> {selectedCompany.lastActivity || 'لا يوجد'}</Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            </Grid>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDetailsDialogOpen(false)}>\n            إغلاق\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* حوار تأكيد الحذف */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>تأكيد الحذف</DialogTitle>\n        <DialogContent>\n          هل أنت متأكد من حذف الشركة \"{selectedCompany?.name}\"؟ \n          هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بياناتها وحملاتها.\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>\n            إلغاء\n          </Button>\n          <Button onClick={handleDeleteCompany} color=\"error\" variant=\"contained\">\n            حذف\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </PageContainer>\n  );\n};\n\nexport default CompanyManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,QAAQ,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,YAAY,EAC3BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,SAAS,EACxBC,UAAU,IAAIC,UAAU,EACxBC,UAAU,IAAIC,QAAQ,QACjB,qBAAqB;AAC5B,SACEC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,kBAAkB,EAClBC,SAAS,EACTC,SAAS,QACJ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+E,eAAe,EAAEC,kBAAkB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuF,IAAI,EAAEC,OAAO,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM+F,eAAe,GAAG,CACtB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,uBAAuB;IAC7BC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,qBAAqB;IACjCC,SAAS,EAAE,GAAG;IACdC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,SAAS;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,qBAAqB;IACjCC,SAAS,EAAE,EAAE;IACbC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,UAAU;IACxBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qBAAqB;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,WAAW;IACtBC,UAAU,EAAE,+BAA+B;IAC3CC,SAAS,EAAE,EAAE;IACbC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,OAAO;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,0BAA0B;IAChCC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,0BAA0B;IACtCC,SAAS,EAAE,GAAG;IACdC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,YAAY;IACtBC,YAAY,EAAE,YAAY;IAC1BC,YAAY,EAAE,YAAY;IAC1BC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED7G,SAAS,CAAC,MAAM;IACd;IACA8G,UAAU,CAAC,MAAM;MACfvC,YAAY,CAACuB,eAAe,CAAC;MAC7BrB,oBAAoB,CAACqB,eAAe,CAAC;MACrCnB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN3E,SAAS,CAAC,MAAM;IACd;IACA,IAAI+G,QAAQ,GAAGzC,SAAS;IAExB,IAAIM,UAAU,EAAE;MACdmC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAChCA,OAAO,CAACjB,IAAI,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IAC7DD,OAAO,CAAChB,QAAQ,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IACjED,OAAO,CAACZ,SAAS,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CACnE,CAAC;IACH;IAEA,IAAIxB,YAAY,KAAK,KAAK,EAAE;MAC1BqB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACd,MAAM,KAAKT,YAAY,CAAC;IACxE;IAEA,IAAIE,UAAU,KAAK,KAAK,EAAE;MACxBmB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACf,IAAI,KAAKN,UAAU,CAAC;IACpE;IAEAnB,oBAAoB,CAACsC,QAAQ,CAAC;IAC9BxB,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAACX,UAAU,EAAEc,YAAY,EAAEE,UAAU,EAAEtB,SAAS,CAAC,CAAC;EAErD,MAAM8C,cAAc,GAAGA,CAACC,KAAK,EAAEJ,OAAO,KAAK;IACzChC,WAAW,CAACoC,KAAK,CAACC,aAAa,CAAC;IAChCvC,kBAAkB,CAACkC,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BtC,WAAW,CAAC,IAAI,CAAC;IACjBF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK;IACnDnD,YAAY,CAACoD,IAAI,IACfA,IAAI,CAACC,GAAG,CAACX,OAAO,IACdA,OAAO,CAAClB,EAAE,KAAK0B,SAAS,GACpB;MAAE,GAAGR,OAAO;MAAEd,MAAM,EAAEuB;IAAU,CAAC,GACjCT,OACN,CACF,CAAC;IACDM,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAChCtD,YAAY,CAACoD,IAAI,IACfA,IAAI,CAACX,MAAM,CAACC,OAAO,IAAIA,OAAO,CAAClB,EAAE,KAAKjB,eAAe,CAACiB,EAAE,CAC1D,CAAC;IACDZ,mBAAmB,CAAC,KAAK,CAAC;IAC1BoC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMO,cAAc,GAAI3B,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM4B,aAAa,GAAI5B,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC,KAAK,WAAW;QAAE,OAAO,QAAQ;MACjC,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC;QAAS,OAAOA,MAAM;IACxB;EACF,CAAC;EAED,MAAM6B,WAAW,GAAI9B,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAM+B,YAAY,GAAI/B,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAItB,YAAY,IAAK;IAC5C,QAAQA,YAAY;MAClB,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,UAAU;QAAE,OAAO,OAAO;MAC/B,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC;QAAS,OAAOA,YAAY;IAC9B;EACF,CAAC;;EAED;EACA,MAAMuB,KAAK,GAAG;IACZC,cAAc,EAAE9D,SAAS,CAAC+D,MAAM;IAChCC,eAAe,EAAEhE,SAAS,CAAC0C,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;IACpEG,gBAAgB,EAAElE,SAAS,CAAC0C,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,SAAS,CAAC,CAACkC,MAAM;IACtEI,YAAY,EAAEnE,SAAS,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,GAAGJ,CAAC,CAAC9B,UAAU,EAAE,CAAC;EAClE,CAAC;EAED,oBACEtC,OAAA,CAACP,aAAa;IACZgF,KAAK,EAAC,2EAAe;IACrBC,QAAQ,EAAC,4OAA8C;IACvDC,WAAW,EAAE,CACX;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAmB,CAAC,EAClD;MAAED,KAAK,EAAE;IAAgB,CAAC,CAC1B;IACFE,OAAO,EAAE,cACP9E,OAAA,CAAC/D,MAAM;MAEL8I,OAAO,EAAC,UAAU;MAClBC,SAAS,eAAEhF,OAAA,CAACpB,YAAY;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAC,QAAA,EAC7B;IAED,GALM,QAAQ;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKN,CAAC,CACT;IAAAC,QAAA,gBAGFrF,OAAA,CAACN,WAAW;MAAC+E,KAAK,EAAC,mDAAW;MAAAY,QAAA,eAC5BrF,OAAA,CAACF,SAAS;QAAAuF,QAAA,gBACRrF,OAAA,CAACH,SAAS;UACR4E,KAAK,EAAC,iFAAgB;UACtBa,KAAK,EAAEtB,KAAK,CAACC,cAAe;UAC5BsB,MAAM,EAAE,GAAGvB,KAAK,CAACG,eAAe,OAAQ;UACxCqB,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAEzF,OAAA,CAAClB,YAAY;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,KAAK,EAAC;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFpF,OAAA,CAACH,SAAS;UACR4E,KAAK,EAAC,iFAAgB;UACtBa,KAAK,EAAEtB,KAAK,CAACG,eAAgB;UAC7BoB,MAAM,EAAE,GAAG,CAAEvB,KAAK,CAACG,eAAe,GAAGH,KAAK,CAACC,cAAc,GAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,eAAgB;UAC5FH,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAEzF,OAAA,CAACxB,YAAY;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBM,KAAK,EAAC;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFpF,OAAA,CAACH,SAAS;UACR4E,KAAK,EAAC,oGAAoB;UAC1Ba,KAAK,EAAEtB,KAAK,CAACK,gBAAiB;UAC9BkB,MAAM,EAAC,qEAAc;UACrBC,UAAU,EAAC,SAAS;UACpBC,IAAI,eAAEzF,OAAA,CAACV,UAAU;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBM,KAAK,EAAC;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFpF,OAAA,CAACH,SAAS;UACR4E,KAAK,EAAC,6FAAkB;UACxBa,KAAK,EAAE,GAAGtB,KAAK,CAACM,YAAY,CAACsB,cAAc,CAAC,CAAC,MAAO;UACpDL,MAAM,EAAC,kFAAiB;UACxBC,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAEzF,OAAA,CAACZ,SAAS;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBM,KAAK,EAAC;QAAW;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGdpF,OAAA,CAACN,WAAW;MAAC+E,KAAK,EAAC,iFAAgB;MAAAY,QAAA,eACjCrF,OAAA,CAACL,WAAW;QAAA0F,QAAA,eACVrF,OAAA,CAACJ,kBAAkB;UAAAyF,QAAA,eACjBrF,OAAA,CAACjE,IAAI;YAAC8J,SAAS;YAACC,OAAO,EAAE,CAAE;YAACC,UAAU,EAAC,QAAQ;YAAAV,QAAA,gBAC7CrF,OAAA,CAACjE,IAAI;cAACiK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrF,OAAA,CAAC9D,SAAS;gBACRiK,SAAS;gBACTC,WAAW,EAAC,yEAAkB;gBAC9Bd,KAAK,EAAE7E,UAAW;gBAClB4F,QAAQ,EAAGC,CAAC,IAAK5F,aAAa,CAAC4F,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;gBAC/CkB,UAAU,EAAE;kBACVC,cAAc,eACZzG,OAAA,CAAC7D,cAAc;oBAACuK,QAAQ,EAAC,OAAO;oBAAArB,QAAA,eAC9BrF,OAAA,CAAClC,UAAU;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpF,OAAA,CAACjE,IAAI;cAACiK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrF,OAAA,CAAC1C,WAAW;gBAAC6I,SAAS;gBAAAd,QAAA,gBACpBrF,OAAA,CAACzC,UAAU;kBAAA8H,QAAA,EAAC;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BpF,OAAA,CAACxC,MAAM;kBACL8H,KAAK,EAAE/D,YAAa;kBACpB8E,QAAQ,EAAGC,CAAC,IAAK9E,eAAe,CAAC8E,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;kBACjDV,KAAK,EAAC,sCAAQ;kBAAAS,QAAA,gBAEdrF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,KAAK;oBAAAD,QAAA,EAAC;kBAAY;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,QAAQ;oBAAAD,QAAA,EAAC;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxCpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,SAAS;oBAAAD,QAAA,EAAC;kBAAW;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,UAAU;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPpF,OAAA,CAACjE,IAAI;cAACiK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrF,OAAA,CAAC1C,WAAW;gBAAC6I,SAAS;gBAAAd,QAAA,gBACpBrF,OAAA,CAACzC,UAAU;kBAAA8H,QAAA,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCpF,OAAA,CAACxC,MAAM;kBACL8H,KAAK,EAAE7D,UAAW;kBAClB4E,QAAQ,EAAGC,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;kBAC/CV,KAAK,EAAC,yDAAY;kBAAAS,QAAA,gBAElBrF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,KAAK;oBAAAD,QAAA,EAAC;kBAAY;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC7CpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxCpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,QAAQ;oBAAAD,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CpF,OAAA,CAACxD,QAAQ;oBAAC8I,KAAK,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPpF,OAAA,CAACjE,IAAI;cAACiK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrF,OAAA,CAAC/D,MAAM;gBACL8I,OAAO,EAAC,UAAU;gBAClBC,SAAS,eAAEhF,OAAA,CAACV,UAAU;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1Be,SAAS;gBAAAd,QAAA,EACV;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGdpF,OAAA,CAACN,WAAW;MAAC+E,KAAK,EAAC,2EAAe;MAAAY,QAAA,eAChCrF,OAAA,CAACL,WAAW;QAAA0F,QAAA,eACVrF,OAAA,CAACJ,kBAAkB;UAAAyF,QAAA,gBACjBrF,OAAA,CAAC9C,cAAc;YAAAmI,QAAA,eACbrF,OAAA,CAACjD,KAAK;cAAAsI,QAAA,gBACJrF,OAAA,CAAC7C,SAAS;gBAAAkI,QAAA,eACRrF,OAAA,CAAC5C,QAAQ;kBAAAiI,QAAA,gBACPrF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAQ;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAO;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAO;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAC;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZpF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,EACPhF,iBAAiB,CACfsG,KAAK,CAACxF,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC,CAC3DoC,GAAG,CAAEX,OAAO,iBACX9C,OAAA,CAAC5C,QAAQ;kBAAkBwJ,KAAK;kBAAAvB,QAAA,gBAC9BrF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,eACRrF,OAAA,CAAClE,GAAG;sBAAC+K,OAAO,EAAC,MAAM;sBAACd,UAAU,EAAC,QAAQ;sBAACe,GAAG,EAAE,CAAE;sBAAAzB,QAAA,gBAC7CrF,OAAA,CAAClD,MAAM;wBAAAuI,QAAA,eACLrF,OAAA,CAAClB,YAAY;0BAAAmG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACTpF,OAAA,CAAClE,GAAG;wBAAAuJ,QAAA,gBACFrF,OAAA,CAAChE,UAAU;0BAAC+I,OAAO,EAAC,OAAO;0BAACgC,UAAU,EAAC,MAAM;0BAAA1B,QAAA,EAC1CvC,OAAO,CAACjB;wBAAI;0BAAAoD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACbpF,OAAA,CAAChE,UAAU;0BAAC+I,OAAO,EAAC,SAAS;0BAACW,KAAK,EAAC,gBAAgB;0BAAAL,QAAA,GACjDvC,OAAO,CAAChB,QAAQ,EAAC,UAAG,EAACgB,OAAO,CAACJ,QAAQ;wBAAA;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,eACRrF,OAAA,CAAClE,GAAG;sBAAAuJ,QAAA,gBACFrF,OAAA,CAAChE,UAAU;wBAAC+I,OAAO,EAAC,OAAO;wBAAAM,QAAA,EACxBvC,OAAO,CAACZ;sBAAS;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eACbpF,OAAA,CAAChE,UAAU;wBAAC+I,OAAO,EAAC,SAAS;wBAACW,KAAK,EAAC,gBAAgB;wBAAAL,QAAA,EACjDvC,OAAO,CAACX;sBAAU;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,eACRrF,OAAA,CAAC5D,IAAI;sBACHwI,KAAK,EAAEhB,aAAa,CAACd,OAAO,CAACd,MAAM,CAAE;sBACrC0D,KAAK,EAAE/B,cAAc,CAACb,OAAO,CAACd,MAAM,CAAE;sBACtCD,IAAI,EAAC;oBAAO;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,eACRrF,OAAA,CAAC5D,IAAI;sBACHwI,KAAK,EAAEf,WAAW,CAACf,OAAO,CAACf,IAAI,CAAE;sBACjC2D,KAAK,EAAE5B,YAAY,CAAChB,OAAO,CAACf,IAAI,CAAE;sBAClCgD,OAAO,EAAC,UAAU;sBAClBhD,IAAI,EAAC;oBAAO;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAEvC,OAAO,CAACV;kBAAS;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1CpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,EAAEvC,OAAO,CAACT;kBAAe;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,GAAEvC,OAAO,CAACR,UAAU,CAACsD,cAAc,CAAC,CAAC,EAAC,gBAAI;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChEpF,OAAA,CAAC/C,SAAS;oBAAAoI,QAAA,eACRrF,OAAA,CAAC3D,UAAU;sBAAC2K,OAAO,EAAGV,CAAC,IAAKrD,cAAc,CAACqD,CAAC,EAAExD,OAAO,CAAE;sBAAAuC,QAAA,eACrDrF,OAAA,CAAChC,QAAQ;wBAAAiH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAhDCtC,OAAO,CAAClB,EAAE;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiDf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEjBpF,OAAA,CAAC3C,eAAe;YACd4J,SAAS,EAAC,KAAK;YACfC,KAAK,EAAE7G,iBAAiB,CAAC6D,MAAO;YAChC/C,IAAI,EAAEA,IAAK;YACXgG,YAAY,EAAEA,CAACb,CAAC,EAAEc,OAAO,KAAKhG,OAAO,CAACgG,OAAO,CAAE;YAC/C/F,WAAW,EAAEA,WAAY;YACzBgG,mBAAmB,EAAGf,CAAC,IAAKhF,cAAc,CAACgG,QAAQ,CAAChB,CAAC,CAACC,MAAM,CAACjB,KAAK,EAAE,EAAE,CAAC,CAAE;YACzEiC,gBAAgB,EAAC,4GAAuB;YACxCC,kBAAkB,EAAEA,CAAC;cAAEC,IAAI;cAAEC,EAAE;cAAER;YAAM,CAAC,KAAK,GAAGO,IAAI,IAAIC,EAAE,OAAOR,KAAK;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGdpF,OAAA,CAACzD,IAAI;MACHsE,QAAQ,EAAEA,QAAS;MACnB8G,IAAI,EAAEC,OAAO,CAAC/G,QAAQ,CAAE;MACxBgH,OAAO,EAAEzE,eAAgB;MAAAiC,QAAA,gBAEzBrF,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAAC,IAAI,CAAE;QAAAmE,QAAA,gBAClDrF,OAAA,CAACR,QAAQ;UAACsI,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uEAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXpF,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAE5D,eAAgB;QAAAiC,QAAA,gBACjCrF,OAAA,CAAC9B,QAAQ;UAAC4J,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXpF,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAE5D,eAAgB;QAAAiC,QAAA,gBACjCrF,OAAA,CAACtB,SAAS;UAACoJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iEAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,EACV,CAAAzE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,MAAM,MAAK,QAAQ,gBACnChC,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC1C,eAAe,CAACiB,EAAE,EAAE,WAAW,CAAE;QAAAyD,QAAA,gBAC3ErF,OAAA,CAAC1B,SAAS;UAACwJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uEAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,gBAEXpF,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC1C,eAAe,CAACiB,EAAE,EAAE,QAAQ,CAAE;QAAAyD,QAAA,gBACxErF,OAAA,CAACxB,YAAY;UAACsJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uEAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CACX,EACA,CAAAzE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqB,MAAM,MAAK,SAAS,iBACpChC,OAAA,CAACxD,QAAQ;QAACwK,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC1C,eAAe,CAACiB,EAAE,EAAE,QAAQ,CAAE;QAAAyD,QAAA,gBACxErF,OAAA,CAACxB,YAAY;UAACsJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4GAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CACX,eACDpF,OAAA,CAACxD,QAAQ;QACPwK,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAAC,IAAI,CAAE;QACzC8G,EAAE,EAAE;UAAEpC,KAAK,EAAE;QAAa,CAAE;QAAAL,QAAA,gBAE5BrF,OAAA,CAAC5B,UAAU;UAAC0J,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPpF,OAAA,CAACvD,MAAM;MAACkL,IAAI,EAAE1G,iBAAkB;MAAC4G,OAAO,EAAEA,CAAA,KAAM3G,oBAAoB,CAAC,KAAK,CAAE;MAAC8G,QAAQ,EAAC,IAAI;MAAC7B,SAAS;MAAAd,QAAA,gBAClGrF,OAAA,CAACtD,WAAW;QAAA2I,QAAA,EAAC;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACxCpF,OAAA,CAACrD,aAAa;QAAA0I,QAAA,EACX1E,eAAe,iBACdX,OAAA,CAACjE,IAAI;UAAC8J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAT,QAAA,gBACzBrF,OAAA,CAACjE,IAAI;YAACiK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvBrF,OAAA,CAACvC,IAAI;cAACsH,OAAO,EAAC,UAAU;cAAAM,QAAA,eACtBrF,OAAA,CAACtC,WAAW;gBAAA2H,QAAA,gBACVrF,OAAA,CAAChE,UAAU;kBAAC+I,OAAO,EAAC,IAAI;kBAACkD,YAAY;kBAAA5C,QAAA,EAAC;gBAEtC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAACkB,IAAI;gBAAA;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACvEpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAQ;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAACmB,QAAQ;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC7EpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAO;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAAC+B,QAAQ;gBAAA;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC5EpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvB,WAAW,CAAClD,eAAe,CAACoB,IAAI,CAAC;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpFpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAa;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAACyB,SAAS;gBAAA;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACPpF,OAAA,CAACjE,IAAI;YAACiK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvBrF,OAAA,CAACvC,IAAI;cAACsH,OAAO,EAAC,UAAU;cAAAM,QAAA,eACtBrF,OAAA,CAACtC,WAAW;gBAAA2H,QAAA,gBACVrF,OAAA,CAAChE,UAAU;kBAAC+I,OAAO,EAAC,IAAI;kBAACkD,YAAY;kBAAA5C,QAAA,EAAC;gBAEtC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAe;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAAC0B,eAAe;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3FpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAe;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAAC2B,UAAU,CAACsD,cAAc,CAAC,CAAC,EAAC,gBAAI;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3GpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAa;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACrB,mBAAmB,CAACpD,eAAe,CAAC8B,YAAY,CAAC;gBAAA;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3GpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAe;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAAC4B,QAAQ;gBAAA;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpFpF,OAAA,CAAChE,UAAU;kBAAAqJ,QAAA,gBAACrF,OAAA;oBAAAqF,QAAA,EAAQ;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACzE,eAAe,CAAC6B,YAAY,IAAI,SAAS;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBpF,OAAA,CAACpD,aAAa;QAAAyI,QAAA,eACZrF,OAAA,CAAC/D,MAAM;UAAC+K,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAAC,KAAK,CAAE;UAAAmE,QAAA,EAAC;QAEpD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpF,OAAA,CAACvD,MAAM;MAACkL,IAAI,EAAE5G,gBAAiB;MAAC8G,OAAO,EAAEA,CAAA,KAAM7G,mBAAmB,CAAC,KAAK,CAAE;MAAAqE,QAAA,gBACxErF,OAAA,CAACtD,WAAW;QAAA2I,QAAA,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtCpF,OAAA,CAACrD,aAAa;QAAA0I,QAAA,GAAC,wIACe,EAAC1E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkB,IAAI,EAAC,0VAErD;MAAA;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAChBpF,OAAA,CAACpD,aAAa;QAAAyI,QAAA,gBACZrF,OAAA,CAAC/D,MAAM;UAAC+K,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAAC,KAAK,CAAE;UAAAqE,QAAA,EAAC;QAEnD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpF,OAAA,CAAC/D,MAAM;UAAC+K,OAAO,EAAEtD,mBAAoB;UAACgC,KAAK,EAAC,OAAO;UAACX,OAAO,EAAC,WAAW;UAAAM,QAAA,EAAC;QAExE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB,CAAC;AAAClF,EAAA,CA1gBID,iBAAiB;AAAAiI,EAAA,GAAjBjI,iBAAiB;AA4gBvB,eAAeA,iBAAiB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}