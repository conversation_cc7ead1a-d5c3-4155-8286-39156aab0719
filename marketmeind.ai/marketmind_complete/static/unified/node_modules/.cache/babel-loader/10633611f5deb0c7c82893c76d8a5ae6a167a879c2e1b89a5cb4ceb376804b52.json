{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Tabs as MuiTabs, Tab as MuiTab, Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Tabs = ({\n  children,\n  defaultValue,\n  value,\n  onValueChange,\n  className,\n  ...props\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(defaultValue || value || 0);\n  const handleChange = (event, newValue) => {\n    setActiveTab(newValue);\n    if (onValueChange) {\n      onValueChange(newValue);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: className,\n    ...props,\n    children: React.Children.map(children, (child, index) => {\n      if (child.type === TabsList) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          value: value || activeTab,\n          onChange: handleChange\n        });\n      }\n      if (child.type === TabsContent) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          activeTab: value || activeTab\n        });\n      }\n      return child;\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Tabs, \"+B+jFi/I7rpJnAyVI/aa0pnaIGE=\");\n_c = Tabs;\nexport const TabsList = ({\n  children,\n  value,\n  onChange,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiTabs, {\n    value: value,\n    onChange: onChange,\n    className: className,\n    sx: {\n      borderBottom: 1,\n      borderColor: 'divider',\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c2 = TabsList;\nexport const TabsTrigger = ({\n  children,\n  value,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiTab, {\n    label: children,\n    value: value,\n    className: className,\n    sx: {\n      textTransform: 'none',\n      fontWeight: 500,\n      ...props.sx\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_c3 = TabsTrigger;\nexport const TabsContent = ({\n  children,\n  value,\n  activeTab,\n  className,\n  ...props\n}) => {\n  if (activeTab !== value) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: className,\n    sx: {\n      pt: 2,\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_c4 = TabsContent;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Tabs\");\n$RefreshReg$(_c2, \"TabsList\");\n$RefreshReg$(_c3, \"TabsTrigger\");\n$RefreshReg$(_c4, \"TabsContent\");", "map": {"version": 3, "names": ["React", "useState", "Tabs", "MuiTabs", "Tab", "MuiTab", "Box", "jsxDEV", "_jsxDEV", "children", "defaultValue", "value", "onValueChange", "className", "props", "_s", "activeTab", "setActiveTab", "handleChange", "event", "newValue", "Children", "map", "child", "index", "type", "TabsList", "cloneElement", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "sx", "borderBottom", "borderColor", "_c2", "TabsTrigger", "label", "textTransform", "fontWeight", "_c3", "pt", "_c4", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Tabs as Mui<PERSON>ab<PERSON>,\n  Tab as MuiTab,\n  Box\n} from '@mui/material';\n\nexport const Tabs = ({ \n  children, \n  defaultValue,\n  value,\n  onValueChange,\n  className,\n  ...props \n}) => {\n  const [activeTab, setActiveTab] = useState(defaultValue || value || 0);\n\n  const handleChange = (event, newValue) => {\n    setActiveTab(newValue);\n    if (onValueChange) {\n      onValueChange(newValue);\n    }\n  };\n\n  return (\n    <Box className={className} {...props}>\n      {React.Children.map(children, (child, index) => {\n        if (child.type === TabsList) {\n          return React.cloneElement(child, { \n            value: value || activeTab, \n            onChange: handleChange \n          });\n        }\n        if (child.type === TabsContent) {\n          return React.cloneElement(child, { \n            activeTab: value || activeTab \n          });\n        }\n        return child;\n      })}\n    </Box>\n  );\n};\n\nexport const TabsList = ({ children, value, onChange, className, ...props }) => {\n  return (\n    <MuiTabs\n      value={value}\n      onChange={onChange}\n      className={className}\n      sx={{\n        borderBottom: 1,\n        borderColor: 'divider',\n        ...props.sx\n      }}\n      {...props}\n    >\n      {children}\n    </MuiTabs>\n  );\n};\n\nexport const TabsTrigger = ({ children, value, className, ...props }) => {\n  return (\n    <MuiTab\n      label={children}\n      value={value}\n      className={className}\n      sx={{\n        textTransform: 'none',\n        fontWeight: 500,\n        ...props.sx\n      }}\n      {...props}\n    />\n  );\n};\n\nexport const TabsContent = ({ children, value, activeTab, className, ...props }) => {\n  if (activeTab !== value) {\n    return null;\n  }\n\n  return (\n    <Box className={className} sx={{ pt: 2, ...props.sx }} {...props}>\n      {children}\n    </Box>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,IAAIC,OAAO,EACfC,GAAG,IAAIC,MAAM,EACbC,GAAG,QACE,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,OAAO,MAAMN,IAAI,GAAGA,CAAC;EACnBO,QAAQ;EACRC,YAAY;EACZC,KAAK;EACLC,aAAa;EACbC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAACS,YAAY,IAAIC,KAAK,IAAI,CAAC,CAAC;EAEtE,MAAMO,YAAY,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACxCH,YAAY,CAACG,QAAQ,CAAC;IACtB,IAAIR,aAAa,EAAE;MACjBA,aAAa,CAACQ,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,oBACEZ,OAAA,CAACF,GAAG;IAACO,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAL,QAAA,EACjCT,KAAK,CAACqB,QAAQ,CAACC,GAAG,CAACb,QAAQ,EAAE,CAACc,KAAK,EAAEC,KAAK,KAAK;MAC9C,IAAID,KAAK,CAACE,IAAI,KAAKC,QAAQ,EAAE;QAC3B,oBAAO1B,KAAK,CAAC2B,YAAY,CAACJ,KAAK,EAAE;UAC/BZ,KAAK,EAAEA,KAAK,IAAIK,SAAS;UACzBY,QAAQ,EAAEV;QACZ,CAAC,CAAC;MACJ;MACA,IAAIK,KAAK,CAACE,IAAI,KAAKI,WAAW,EAAE;QAC9B,oBAAO7B,KAAK,CAAC2B,YAAY,CAACJ,KAAK,EAAE;UAC/BP,SAAS,EAAEL,KAAK,IAAIK;QACtB,CAAC,CAAC;MACJ;MACA,OAAOO,KAAK;IACd,CAAC;EAAC;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnCWb,IAAI;AAAAgC,EAAA,GAAJhC,IAAI;AAqCjB,OAAO,MAAMwB,QAAQ,GAAGA,CAAC;EAAEjB,QAAQ;EAAEE,KAAK;EAAEiB,QAAQ;EAAEf,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAC9E,oBACEN,OAAA,CAACL,OAAO;IACNQ,KAAK,EAAEA,KAAM;IACbiB,QAAQ,EAAEA,QAAS;IACnBf,SAAS,EAAEA,SAAU;IACrBsB,EAAE,EAAE;MACFC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,SAAS;MACtB,GAAGvB,KAAK,CAACqB;IACX,CAAE;IAAA,GACErB,KAAK;IAAAL,QAAA,EAERA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC;AAACK,GAAA,GAhBWZ,QAAQ;AAkBrB,OAAO,MAAMa,WAAW,GAAGA,CAAC;EAAE9B,QAAQ;EAAEE,KAAK;EAAEE,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACvE,oBACEN,OAAA,CAACH,MAAM;IACLmC,KAAK,EAAE/B,QAAS;IAChBE,KAAK,EAAEA,KAAM;IACbE,SAAS,EAAEA,SAAU;IACrBsB,EAAE,EAAE;MACFM,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE,GAAG;MACf,GAAG5B,KAAK,CAACqB;IACX,CAAE;IAAA,GACErB;EAAK;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACU,GAAA,GAdWJ,WAAW;AAgBxB,OAAO,MAAMV,WAAW,GAAGA,CAAC;EAAEpB,QAAQ;EAAEE,KAAK;EAAEK,SAAS;EAAEH,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAClF,IAAIE,SAAS,KAAKL,KAAK,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,oBACEH,OAAA,CAACF,GAAG;IAACO,SAAS,EAAEA,SAAU;IAACsB,EAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAE,GAAG9B,KAAK,CAACqB;IAAG,CAAE;IAAA,GAAKrB,KAAK;IAAAL,QAAA,EAC7DA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACY,GAAA,GAVWhB,WAAW;AAAA,IAAAK,EAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}