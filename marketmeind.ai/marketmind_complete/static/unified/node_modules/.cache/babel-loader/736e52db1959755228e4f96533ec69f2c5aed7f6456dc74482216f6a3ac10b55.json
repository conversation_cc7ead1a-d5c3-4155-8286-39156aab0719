{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js\";\nimport React from 'react';\nimport { Dialog as MuiDialog, DialogTitle as MuiDialogTitle, DialogContent as MuiDialogContent, DialogActions as MuiDialogActions, DialogContentText } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Dialog = ({\n  children,\n  open,\n  onOpenChange,\n  maxWidth = 'sm',\n  fullWidth = true,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiDialog, {\n    open: open,\n    onClose: () => onOpenChange && onOpenChange(false),\n    maxWidth: maxWidth,\n    fullWidth: fullWidth,\n    className: className,\n    sx: {\n      '& .MuiDialog-paper': {\n        borderRadius: 3\n      },\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = Dialog;\nexport const DialogContent = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiDialogContent, {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c2 = DialogContent;\nexport const DialogHeader = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c3 = DialogHeader;\nexport const DialogTitle = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiDialogTitle, {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_c4 = DialogTitle;\nexport const DialogDescription = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(DialogContentText, {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_c5 = DialogDescription;\nexport const DialogActions = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiDialogActions, {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_c6 = DialogActions;\nexport const DialogTrigger = ({\n  children,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_c7 = DialogTrigger;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Dialog\");\n$RefreshReg$(_c2, \"DialogContent\");\n$RefreshReg$(_c3, \"DialogHeader\");\n$RefreshReg$(_c4, \"DialogTitle\");\n$RefreshReg$(_c5, \"DialogDescription\");\n$RefreshReg$(_c6, \"DialogActions\");\n$RefreshReg$(_c7, \"DialogTrigger\");", "map": {"version": 3, "names": ["React", "Dialog", "MuiDialog", "DialogTitle", "MuiDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MuiD<PERSON>ogContent", "DialogActions", "MuiDialogActions", "DialogContentText", "jsxDEV", "_jsxDEV", "children", "open", "onOpenChange", "max<PERSON><PERSON><PERSON>", "fullWidth", "className", "props", "onClose", "sx", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "_c2", "DialogHeader", "_c3", "_c4", "DialogDescription", "_c5", "_c6", "DialogTrigger", "_c7", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js"], "sourcesContent": ["import React from 'react';\nimport { \n  Dialog as <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  DialogTitle as <PERSON>i<PERSON><PERSON>ogTitle,\n  DialogContent as <PERSON><PERSON><PERSON><PERSON>ogContent,\n  DialogActions as Mui<PERSON>ialogActions,\n  DialogContentText\n} from '@mui/material';\n\nexport const Dialog = ({ \n  children, \n  open,\n  onOpenChange,\n  maxWidth = 'sm',\n  fullWidth = true,\n  className,\n  ...props \n}) => {\n  return (\n    <MuiDialog\n      open={open}\n      onClose={() => onOpenChange && onOpenChange(false)}\n      maxWidth={maxWidth}\n      fullWidth={fullWidth}\n      className={className}\n      sx={{\n        '& .MuiDialog-paper': {\n          borderRadius: 3,\n        },\n        ...props.sx\n      }}\n      {...props}\n    >\n      {children}\n    </MuiDialog>\n  );\n};\n\nexport const DialogContent = ({ children, className, ...props }) => {\n  return (\n    <MuiDialogContent className={className} {...props}>\n      {children}\n    </MuiDialogContent>\n  );\n};\n\nexport const DialogHeader = ({ children, className, ...props }) => {\n  return (\n    <div className={className} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport const DialogTitle = ({ children, className, ...props }) => {\n  return (\n    <MuiDialogTitle className={className} {...props}>\n      {children}\n    </MuiDialogTitle>\n  );\n};\n\nexport const DialogDescription = ({ children, className, ...props }) => {\n  return (\n    <DialogContentText className={className} {...props}>\n      {children}\n    </DialogContentText>\n  );\n};\n\nexport const DialogActions = ({ children, className, ...props }) => {\n  return (\n    <MuiDialogActions className={className} {...props}>\n      {children}\n    </MuiDialogActions>\n  );\n};\n\nexport const DialogTrigger = ({ children, ...props }) => {\n  return (\n    <div {...props}>\n      {children}\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,IAAIC,SAAS,EACnBC,WAAW,IAAIC,cAAc,EAC7BC,aAAa,IAAIC,gBAAgB,EACjCC,aAAa,IAAIC,gBAAgB,EACjCC,iBAAiB,QACZ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,OAAO,MAAMV,MAAM,GAAGA,CAAC;EACrBW,QAAQ;EACRC,IAAI;EACJC,YAAY;EACZC,QAAQ,GAAG,IAAI;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEP,OAAA,CAACT,SAAS;IACRW,IAAI,EAAEA,IAAK;IACXM,OAAO,EAAEA,CAAA,KAAML,YAAY,IAAIA,YAAY,CAAC,KAAK,CAAE;IACnDC,QAAQ,EAAEA,QAAS;IACnBC,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA,SAAU;IACrBG,EAAE,EAAE;MACF,oBAAoB,EAAE;QACpBC,YAAY,EAAE;MAChB,CAAC;MACD,GAAGH,KAAK,CAACE;IACX,CAAE;IAAA,GACEF,KAAK;IAAAN,QAAA,EAERA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACC,EAAA,GA3BWzB,MAAM;AA6BnB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEO,QAAQ;EAAEK,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAClE,oBACEP,OAAA,CAACL,gBAAgB;IAACW,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAN,QAAA,EAC9CA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEvB,CAAC;AAACE,GAAA,GANWtB,aAAa;AAQ1B,OAAO,MAAMuB,YAAY,GAAGA,CAAC;EAAEhB,QAAQ;EAAEK,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACjE,oBACEP,OAAA;IAAKM,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAN,QAAA,EACjCA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACI,GAAA,GANWD,YAAY;AAQzB,OAAO,MAAMzB,WAAW,GAAGA,CAAC;EAAES,QAAQ;EAAEK,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAChE,oBACEP,OAAA,CAACP,cAAc;IAACa,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAN,QAAA,EAC5CA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAACK,GAAA,GANW3B,WAAW;AAQxB,OAAO,MAAM4B,iBAAiB,GAAGA,CAAC;EAAEnB,QAAQ;EAAEK,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACtE,oBACEP,OAAA,CAACF,iBAAiB;IAACQ,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAN,QAAA,EAC/CA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAExB,CAAC;AAACO,GAAA,GANWD,iBAAiB;AAQ9B,OAAO,MAAMxB,aAAa,GAAGA,CAAC;EAAEK,QAAQ;EAAEK,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAClE,oBACEP,OAAA,CAACH,gBAAgB;IAACS,SAAS,EAAEA,SAAU;IAAA,GAAKC,KAAK;IAAAN,QAAA,EAC9CA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEvB,CAAC;AAACQ,GAAA,GANW1B,aAAa;AAQ1B,OAAO,MAAM2B,aAAa,GAAGA,CAAC;EAAEtB,QAAQ;EAAE,GAAGM;AAAM,CAAC,KAAK;EACvD,oBACEP,OAAA;IAAA,GAASO,KAAK;IAAAN,QAAA,EACXA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACU,GAAA,GANWD,aAAa;AAAA,IAAAR,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}