{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js\";\nimport React from 'react';\nimport { TextField } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Input = ({\n  placeholder,\n  value,\n  onChange,\n  type = 'text',\n  className,\n  disabled = false,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'medium',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(TextField, {\n    placeholder: placeholder,\n    value: value,\n    onChange: onChange,\n    type: type,\n    className: className,\n    disabled: disabled,\n    fullWidth: fullWidth,\n    variant: variant,\n    size: size,\n    sx: {\n      '& .MuiOutlinedInput-root': {\n        borderRadius: 2\n      },\n      ...props.sx\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = Input;\nvar _c;\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "names": ["React", "TextField", "jsxDEV", "_jsxDEV", "Input", "placeholder", "value", "onChange", "type", "className", "disabled", "fullWidth", "variant", "size", "props", "sx", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js"], "sourcesContent": ["import React from 'react';\nimport { TextField } from '@mui/material';\n\nexport const Input = ({ \n  placeholder,\n  value,\n  onChange,\n  type = 'text',\n  className,\n  disabled = false,\n  fullWidth = true,\n  variant = 'outlined',\n  size = 'medium',\n  ...props \n}) => {\n  return (\n    <TextField\n      placeholder={placeholder}\n      value={value}\n      onChange={onChange}\n      type={type}\n      className={className}\n      disabled={disabled}\n      fullWidth={fullWidth}\n      variant={variant}\n      size={size}\n      sx={{\n        '& .MuiOutlinedInput-root': {\n          borderRadius: 2,\n        },\n        ...props.sx\n      }}\n      {...props}\n    />\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,OAAO,MAAMC,KAAK,GAAGA,CAAC;EACpBC,WAAW;EACXC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,MAAM;EACbC,SAAS;EACTC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,IAAI;EAChBC,OAAO,GAAG,UAAU;EACpBC,IAAI,GAAG,QAAQ;EACf,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEX,OAAA,CAACF,SAAS;IACRI,WAAW,EAAEA,WAAY;IACzBC,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBC,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEA,SAAU;IACrBC,QAAQ,EAAEA,QAAS;IACnBC,SAAS,EAAEA,SAAU;IACrBC,OAAO,EAAEA,OAAQ;IACjBC,IAAI,EAAEA,IAAK;IACXE,EAAE,EAAE;MACF,0BAA0B,EAAE;QAC1BC,YAAY,EAAE;MAChB,CAAC;MACD,GAAGF,KAAK,CAACC;IACX,CAAE;IAAA,GACED;EAAK;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACC,EAAA,GAhCWjB,KAAK;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}