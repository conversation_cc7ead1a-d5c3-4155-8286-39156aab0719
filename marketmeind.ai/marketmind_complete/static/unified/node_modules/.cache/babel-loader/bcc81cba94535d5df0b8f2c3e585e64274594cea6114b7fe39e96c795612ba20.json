{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/SystemOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Typography, Button, Card, CardContent, Alert, LinearProgress, Chip, IconButton, Tooltip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon, People as PeopleIcon, Business as BusinessIcon, AttachMoney as MoneyIcon, Security as SecurityIcon, Storage as StorageIcon, Speed as SpeedIcon, Refresh as RefreshIcon, Download as DownloadIcon, Warning as WarningIcon, CheckCircle as CheckIcon, Error as ErrorIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { PageContainer, PageSection, UnifiedCard, UnifiedCardContent, StatsCard, StatsGrid } from '../../shared/components/ui';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SystemOverview = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [systemHealth, setSystemHealth] = useState('excellent');\n\n  // بيانات تجريبية للنظرة العامة\n  const systemStats = {\n    totalUsers: 1247,\n    activeUsers: 892,\n    totalCompanies: 156,\n    activeCompanies: 134,\n    totalRevenue: 2450000,\n    monthlyGrowth: 15.8,\n    systemUptime: 99.97,\n    serverLoad: 23,\n    storageUsed: 67,\n    dailyActiveUsers: 456\n  };\n\n  // بيانات الأداء عبر الوقت\n  const performanceData = [{\n    date: '2024-01-15',\n    users: 1180,\n    companies: 145,\n    revenue: 2200000\n  }, {\n    date: '2024-01-16',\n    users: 1195,\n    companies: 147,\n    revenue: 2250000\n  }, {\n    date: '2024-01-17',\n    users: 1210,\n    companies: 149,\n    revenue: 2300000\n  }, {\n    date: '2024-01-18',\n    users: 1225,\n    companies: 152,\n    revenue: 2350000\n  }, {\n    date: '2024-01-19',\n    users: 1235,\n    companies: 154,\n    revenue: 2400000\n  }, {\n    date: '2024-01-20',\n    users: 1240,\n    companies: 155,\n    revenue: 2420000\n  }, {\n    date: '2024-01-21',\n    users: 1247,\n    companies: 156,\n    revenue: 2450000\n  }];\n\n  // بيانات توزيع المستخدمين\n  const userDistribution = [{\n    name: 'حسابات شخصية',\n    value: 65,\n    color: '#8884d8'\n  }, {\n    name: 'مستخدمو شركات',\n    value: 25,\n    color: '#82ca9d'\n  }, {\n    name: 'مالكو شركات',\n    value: 8,\n    color: '#ffc658'\n  }, {\n    name: 'مديرون',\n    value: 2,\n    color: '#ff7300'\n  }];\n\n  // بيانات الأداء الفني\n  const technicalMetrics = [{\n    name: 'وقت الاستجابة',\n    value: 120,\n    unit: 'ms',\n    status: 'good'\n  }, {\n    name: 'استخدام المعالج',\n    value: 23,\n    unit: '%',\n    status: 'excellent'\n  }, {\n    name: 'استخدام الذاكرة',\n    value: 45,\n    unit: '%',\n    status: 'good'\n  }, {\n    name: 'استخدام التخزين',\n    value: 67,\n    unit: '%',\n    status: 'warning'\n  }, {\n    name: 'عدد الطلبات/ثانية',\n    value: 1250,\n    unit: 'req/s',\n    status: 'excellent'\n  }, {\n    name: 'معدل الأخطاء',\n    value: 0.02,\n    unit: '%',\n    status: 'excellent'\n  }];\n\n  // أحداث النظام الأخيرة\n  const recentEvents = [{\n    id: 1,\n    type: 'info',\n    message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.5',\n    timestamp: '2024-01-22 14:30',\n    severity: 'info'\n  }, {\n    id: 2,\n    type: 'success',\n    message: 'تم إضافة 15 شركة جديدة اليوم',\n    timestamp: '2024-01-22 12:15',\n    severity: 'success'\n  }, {\n    id: 3,\n    type: 'warning',\n    message: 'استخدام التخزين وصل إلى 67% - يُنصح بالتوسع',\n    timestamp: '2024-01-22 10:45',\n    severity: 'warning'\n  }, {\n    id: 4,\n    type: 'info',\n    message: 'تم إجراء نسخ احتياطي تلقائي للبيانات',\n    timestamp: '2024-01-22 03:00',\n    severity: 'info'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'excellent':\n        return 'success';\n      case 'good':\n        return 'info';\n      case 'warning':\n        return 'warning';\n      case 'critical':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'excellent':\n        return /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 32\n        }, this);\n      case 'good':\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 27\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 30\n        }, this);\n      case 'critical':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 30\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 30\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 28\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => setLoading(false), 2000);\n  };\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    title: \"\\u0646\\u0638\\u0631\\u0629 \\u0639\\u0627\\u0645\\u0629 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n    subtitle: \"\\u0645\\u0631\\u0627\\u0642\\u0628\\u0629 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\",\n    breadcrumbs: [{\n      label: 'لوحة التحكم',\n      href: '/dashboard/owner'\n    }, {\n      label: 'نظرة عامة على النظام'\n    }],\n    actions: [/*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\",\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleRefresh,\n        children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)\n    }, \"refresh\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 60\n      }, this),\n      children: \"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631\"\n    }, \"export\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this)],\n    children: [/*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: systemHealth === 'excellent' ? 'success' : 'warning',\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645: \\u0645\\u0645\\u062A\\u0627\\u0632\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), \" - \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u062E\\u062F\\u0645\\u0627\\u062A \\u062A\\u0639\\u0645\\u0644 \\u0628\\u0634\\u0643\\u0644 \\u0637\\u0628\\u064A\\u0639\\u064A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"\\u0648\\u0642\\u062A \\u0627\\u0644\\u062A\\u0634\\u063A\\u064A\\u0644: \", systemStats.systemUptime, \"% | \\u0622\\u062E\\u0631 \\u062A\\u062D\\u062F\\u064A\\u062B: \\u0645\\u0646\\u0630 \\u062F\\u0642\\u064A\\u0642\\u062A\\u064A\\u0646\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\",\n      children: /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\",\n          value: systemStats.totalUsers.toLocaleString(),\n          change: `${systemStats.activeUsers} نشط`,\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\",\n          value: systemStats.totalCompanies,\n          change: `${systemStats.activeCompanies} نشطة`,\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 19\n          }, this),\n          color: \"secondary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\",\n          value: `${(systemStats.totalRevenue / 1000000).toFixed(1)}م ر.س`,\n          change: `+${systemStats.monthlyGrowth}% هذا الشهر`,\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(MoneyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"\\u0648\\u0642\\u062A \\u0627\\u0644\\u062A\\u0634\\u063A\\u064A\\u0644\",\n          value: `${systemStats.systemUptime}%`,\n          change: \"\\u0645\\u062A\\u0627\\u062D \\u0628\\u0627\\u0633\\u062A\\u0645\\u0631\\u0627\\u0631\",\n          changeType: \"positive\",\n          icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 19\n          }, this),\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(PageSection, {\n          title: \"\\u0646\\u0645\\u0648 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0639\\u0628\\u0631 \\u0627\\u0644\\u0648\\u0642\\u062A\",\n          children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n            children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 300,\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: performanceData,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(RechartsTooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"users\",\n                    stackId: \"1\",\n                    stroke: \"#8884d8\",\n                    fill: \"#8884d8\",\n                    name: \"\\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u0648\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"companies\",\n                    stackId: \"1\",\n                    stroke: \"#82ca9d\",\n                    fill: \"#82ca9d\",\n                    name: \"\\u0627\\u0644\\u0634\\u0631\\u0643\\u0627\\u062A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(PageSection, {\n          title: \"\\u062A\\u0648\\u0632\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\\u064A\\u0646\",\n          children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n            children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n              children: [/*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 300,\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: userDistribution,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    label: ({\n                      name,\n                      value\n                    }) => `${value}%`,\n                    children: userDistribution.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: entry.color\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(RechartsTooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                mt: 2,\n                children: userDistribution.map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      width: 12,\n                      height: 12,\n                      bgcolor: item.color,\n                      borderRadius: \"50%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: item.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: [item.value, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(PageSection, {\n          title: \"\\u0627\\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0641\\u0646\\u064A\",\n          children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n            children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: technicalMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: metric.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: [metric.value, \" \", metric.unit]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        icon: getStatusIcon(metric.status),\n                        label: metric.status === 'excellent' ? 'ممتاز' : metric.status === 'good' ? 'جيد' : metric.status === 'warning' ? 'تحذير' : 'حرج',\n                        color: getStatusColor(metric.status),\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: metric.name.includes('معدل الأخطاء') ? (1 - metric.value / 100) * 100 : metric.name.includes('وقت الاستجابة') ? Math.max(0, 100 - metric.value / 5) : metric.value > 100 ? 100 : metric.value,\n                    color: getStatusColor(metric.status),\n                    sx: {\n                      height: 8,\n                      borderRadius: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(PageSection, {\n          title: \"\\u0623\\u062D\\u062F\\u0627\\u062B \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\",\n          children: /*#__PURE__*/_jsxDEV(UnifiedCard, {\n            children: /*#__PURE__*/_jsxDEV(UnifiedCardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                maxHeight: 300,\n                overflow: \"auto\",\n                children: recentEvents.map(event => /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"flex-start\",\n                  gap: 2,\n                  mb: 2,\n                  p: 2,\n                  border: 1,\n                  borderColor: \"divider\",\n                  borderRadius: 2,\n                  children: [getSeverityIcon(event.severity), /*#__PURE__*/_jsxDEV(Box, {\n                    flex: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      gutterBottom: true,\n                      children: event.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: event.timestamp\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)]\n                }, event.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageSection, {\n      title: \"\\u0645\\u0644\\u062E\\u0635 \\u0633\\u0631\\u064A\\u0639\",\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                color: \"primary\",\n                children: \"\\u0627\\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u064A\\u0648\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [\"\\u2022 \", systemStats.dailyActiveUsers, \" \\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0646\\u0634\\u0637\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 15 \\u0634\\u0631\\u0643\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 89 \\u062D\\u0645\\u0644\\u0629 \\u062A\\u0645 \\u0625\\u0637\\u0644\\u0627\\u0642\\u0647\\u0627\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 0 \\u0645\\u0634\\u0627\\u0643\\u0644 \\u0641\\u0646\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                color: \"secondary\",\n                children: \"\\u0627\\u0644\\u062A\\u062D\\u062F\\u064A\\u062B\\u0627\\u062A \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u062A\\u062D\\u062F\\u064A\\u062B \\u0623\\u0645\\u0646\\u064A \\u0645\\u062A\\u0627\\u062D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u062A\\u0648\\u0633\\u064A\\u0639 \\u0645\\u0633\\u0627\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062E\\u0632\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u062A\\u062D\\u062F\\u064A\\u062B \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A SSL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                color: \"success.main\",\n                children: \"\\u0627\\u0644\\u0625\\u0646\\u062C\\u0627\\u0632\\u0627\\u062A \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u0648\\u0635\\u0644\\u0646\\u0627 \\u0625\\u0644\\u0649 1000+ \\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u062A\\u062D\\u0633\\u064A\\u0646 \\u0627\\u0644\\u0623\\u062F\\u0627\\u0621 \\u0628\\u0646\\u0633\\u0628\\u0629 25%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u0625\\u0637\\u0644\\u0627\\u0642 \\u0645\\u064A\\u0632\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\u2022 \\u062A\\u062D\\u0642\\u064A\\u0642 99.9% \\u0648\\u0642\\u062A \\u062A\\u0634\\u063A\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemOverview, \"+JoZfybmLbXjAFyjq1NuZix+pcs=\");\n_c = SystemOverview;\nexport default SystemOverview;\nvar _c;\n$RefreshReg$(_c, \"SystemOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "LinearProgress", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TrendingUp", "TrendingUpIcon", "TrendingDown", "TrendingDownIcon", "People", "PeopleIcon", "Business", "BusinessIcon", "AttachMoney", "MoneyIcon", "Security", "SecurityIcon", "Storage", "StorageIcon", "Speed", "SpeedIcon", "Refresh", "RefreshIcon", "Download", "DownloadIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Info", "InfoIcon", "<PERSON><PERSON><PERSON><PERSON>", "PageSection", "UnifiedCard", "UnifiedCardContent", "StatsCard", "StatsGrid", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "RechartsTooltip", "ResponsiveContainer", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "jsxDEV", "_jsxDEV", "SystemOverview", "_s", "loading", "setLoading", "systemHealth", "setSystemHealth", "systemStats", "totalUsers", "activeUsers", "totalCompanies", "activeCompanies", "totalRevenue", "monthlyGrowth", "systemUptime", "serverLoad", "storageUsed", "dailyActiveUsers", "performanceData", "date", "users", "companies", "revenue", "userDistribution", "name", "value", "color", "technicalMetrics", "unit", "status", "recentEvents", "id", "type", "message", "timestamp", "severity", "getStatusColor", "getStatusIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getSeverityIcon", "handleRefresh", "setTimeout", "title", "subtitle", "breadcrumbs", "label", "href", "actions", "children", "onClick", "variant", "startIcon", "sx", "mb", "toLocaleString", "change", "changeType", "icon", "toFixed", "container", "spacing", "item", "xs", "lg", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stackId", "stroke", "fill", "cx", "cy", "outerRadius", "map", "entry", "index", "mt", "display", "alignItems", "justifyContent", "gap", "bgcolor", "borderRadius", "fontWeight", "metric", "size", "includes", "Math", "max", "maxHeight", "overflow", "event", "p", "border", "borderColor", "flex", "gutterBottom", "md", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/SystemOverview.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Grid,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Alert,\n  LinearProgress,\n  Chip,\n  IconButton,\n  Tooltip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  TrendingUp as TrendingUpIcon,\n  TrendingDown as TrendingDownIcon,\n  People as PeopleIcon,\n  Business as BusinessIcon,\n  AttachMoney as MoneyIcon,\n  Security as SecurityIcon,\n  Storage as StorageIcon,\n  Speed as SpeedIcon,\n  Refresh as RefreshIcon,\n  Download as DownloadIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { \n  PageContainer, \n  PageSection,\n  UnifiedCard, \n  UnifiedCardContent,\n  StatsCard,\n  StatsGrid\n} from '../../shared/components/ui';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, AreaC<PERSON>, Area, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';\n\nconst SystemOverview = () => {\n  const [loading, setLoading] = useState(false);\n  const [systemHealth, setSystemHealth] = useState('excellent');\n\n  // بيانات تجريبية للنظرة العامة\n  const systemStats = {\n    totalUsers: 1247,\n    activeUsers: 892,\n    totalCompanies: 156,\n    activeCompanies: 134,\n    totalRevenue: 2450000,\n    monthlyGrowth: 15.8,\n    systemUptime: 99.97,\n    serverLoad: 23,\n    storageUsed: 67,\n    dailyActiveUsers: 456\n  };\n\n  // بيانات الأداء عبر الوقت\n  const performanceData = [\n    { date: '2024-01-15', users: 1180, companies: 145, revenue: 2200000 },\n    { date: '2024-01-16', users: 1195, companies: 147, revenue: 2250000 },\n    { date: '2024-01-17', users: 1210, companies: 149, revenue: 2300000 },\n    { date: '2024-01-18', users: 1225, companies: 152, revenue: 2350000 },\n    { date: '2024-01-19', users: 1235, companies: 154, revenue: 2400000 },\n    { date: '2024-01-20', users: 1240, companies: 155, revenue: 2420000 },\n    { date: '2024-01-21', users: 1247, companies: 156, revenue: 2450000 }\n  ];\n\n  // بيانات توزيع المستخدمين\n  const userDistribution = [\n    { name: 'حسابات شخصية', value: 65, color: '#8884d8' },\n    { name: 'مستخدمو شركات', value: 25, color: '#82ca9d' },\n    { name: 'مالكو شركات', value: 8, color: '#ffc658' },\n    { name: 'مديرون', value: 2, color: '#ff7300' }\n  ];\n\n  // بيانات الأداء الفني\n  const technicalMetrics = [\n    { name: 'وقت الاستجابة', value: 120, unit: 'ms', status: 'good' },\n    { name: 'استخدام المعالج', value: 23, unit: '%', status: 'excellent' },\n    { name: 'استخدام الذاكرة', value: 45, unit: '%', status: 'good' },\n    { name: 'استخدام التخزين', value: 67, unit: '%', status: 'warning' },\n    { name: 'عدد الطلبات/ثانية', value: 1250, unit: 'req/s', status: 'excellent' },\n    { name: 'معدل الأخطاء', value: 0.02, unit: '%', status: 'excellent' }\n  ];\n\n  // أحداث النظام الأخيرة\n  const recentEvents = [\n    {\n      id: 1,\n      type: 'info',\n      message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.5',\n      timestamp: '2024-01-22 14:30',\n      severity: 'info'\n    },\n    {\n      id: 2,\n      type: 'success',\n      message: 'تم إضافة 15 شركة جديدة اليوم',\n      timestamp: '2024-01-22 12:15',\n      severity: 'success'\n    },\n    {\n      id: 3,\n      type: 'warning',\n      message: 'استخدام التخزين وصل إلى 67% - يُنصح بالتوسع',\n      timestamp: '2024-01-22 10:45',\n      severity: 'warning'\n    },\n    {\n      id: 4,\n      type: 'info',\n      message: 'تم إجراء نسخ احتياطي تلقائي للبيانات',\n      timestamp: '2024-01-22 03:00',\n      severity: 'info'\n    }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'excellent': return 'success';\n      case 'good': return 'info';\n      case 'warning': return 'warning';\n      case 'critical': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'excellent': return <CheckIcon />;\n      case 'good': return <InfoIcon />;\n      case 'warning': return <WarningIcon />;\n      case 'critical': return <ErrorIcon />;\n      default: return <InfoIcon />;\n    }\n  };\n\n  const getSeverityIcon = (severity) => {\n    switch (severity) {\n      case 'success': return <CheckIcon color=\"success\" />;\n      case 'warning': return <WarningIcon color=\"warning\" />;\n      case 'error': return <ErrorIcon color=\"error\" />;\n      default: return <InfoIcon color=\"info\" />;\n    }\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => setLoading(false), 2000);\n  };\n\n  return (\n    <PageContainer\n      title=\"نظرة عامة على النظام\"\n      subtitle=\"مراقبة شاملة لأداء النظام والإحصائيات الرئيسية\"\n      breadcrumbs={[\n        { label: 'لوحة التحكم', href: '/dashboard/owner' },\n        { label: 'نظرة عامة على النظام' }\n      ]}\n      actions={[\n        <Tooltip key=\"refresh\" title=\"تحديث البيانات\">\n          <IconButton onClick={handleRefresh}>\n            <RefreshIcon />\n          </IconButton>\n        </Tooltip>,\n        <Button key=\"export\" variant=\"outlined\" startIcon={<DownloadIcon />}>\n          تصدير التقرير\n        </Button>\n      ]}\n    >\n      {/* حالة النظام العامة */}\n      <PageSection title=\"حالة النظام\">\n        <Alert \n          severity={systemHealth === 'excellent' ? 'success' : 'warning'} \n          sx={{ mb: 3 }}\n        >\n          <Typography variant=\"body1\">\n            <strong>حالة النظام: ممتازة</strong> - جميع الخدمات تعمل بشكل طبيعي\n          </Typography>\n          <Typography variant=\"body2\">\n            وقت التشغيل: {systemStats.systemUptime}% | آخر تحديث: منذ دقيقتين\n          </Typography>\n        </Alert>\n      </PageSection>\n\n      {/* الإحصائيات الرئيسية */}\n      <PageSection title=\"الإحصائيات الرئيسية\">\n        <StatsGrid>\n          <StatsCard\n            title=\"إجمالي المستخدمين\"\n            value={systemStats.totalUsers.toLocaleString()}\n            change={`${systemStats.activeUsers} نشط`}\n            changeType=\"positive\"\n            icon={<PeopleIcon />}\n            color=\"primary\"\n          />\n          <StatsCard\n            title=\"إجمالي الشركات\"\n            value={systemStats.totalCompanies}\n            change={`${systemStats.activeCompanies} نشطة`}\n            changeType=\"positive\"\n            icon={<BusinessIcon />}\n            color=\"secondary\"\n          />\n          <StatsCard\n            title=\"إجمالي الإيرادات\"\n            value={`${(systemStats.totalRevenue / 1000000).toFixed(1)}م ر.س`}\n            change={`+${systemStats.monthlyGrowth}% هذا الشهر`}\n            changeType=\"positive\"\n            icon={<MoneyIcon />}\n            color=\"success\"\n          />\n          <StatsCard\n            title=\"وقت التشغيل\"\n            value={`${systemStats.systemUptime}%`}\n            change=\"متاح باستمرار\"\n            changeType=\"positive\"\n            icon={<SecurityIcon />}\n            color=\"warning\"\n          />\n        </StatsGrid>\n      </PageSection>\n\n      <Grid container spacing={3}>\n        {/* الأداء عبر الوقت */}\n        <Grid item xs={12} lg={8}>\n          <PageSection title=\"نمو النظام عبر الوقت\">\n            <UnifiedCard>\n              <UnifiedCardContent>\n                <ResponsiveContainer width=\"100%\" height={300}>\n                  <AreaChart data={performanceData}>\n                    <CartesianGrid strokeDasharray=\"3 3\" />\n                    <XAxis dataKey=\"date\" />\n                    <YAxis />\n                    <RechartsTooltip />\n                    <Area type=\"monotone\" dataKey=\"users\" stackId=\"1\" stroke=\"#8884d8\" fill=\"#8884d8\" name=\"المستخدمون\" />\n                    <Area type=\"monotone\" dataKey=\"companies\" stackId=\"1\" stroke=\"#82ca9d\" fill=\"#82ca9d\" name=\"الشركات\" />\n                  </AreaChart>\n                </ResponsiveContainer>\n              </UnifiedCardContent>\n            </UnifiedCard>\n          </PageSection>\n        </Grid>\n\n        {/* توزيع المستخدمين */}\n        <Grid item xs={12} lg={4}>\n          <PageSection title=\"توزيع المستخدمين\">\n            <UnifiedCard>\n              <UnifiedCardContent>\n                <ResponsiveContainer width=\"100%\" height={300}>\n                  <PieChart>\n                    <Pie\n                      data={userDistribution}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      outerRadius={80}\n                      fill=\"#8884d8\"\n                      dataKey=\"value\"\n                      label={({ name, value }) => `${value}%`}\n                    >\n                      {userDistribution.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={entry.color} />\n                      ))}\n                    </Pie>\n                    <RechartsTooltip />\n                  </PieChart>\n                </ResponsiveContainer>\n                <Box mt={2}>\n                  {userDistribution.map((item, index) => (\n                    <Box key={index} display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={1}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                        <Box \n                          width={12} \n                          height={12} \n                          bgcolor={item.color} \n                          borderRadius=\"50%\" \n                        />\n                        <Typography variant=\"body2\">{item.name}</Typography>\n                      </Box>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {item.value}%\n                      </Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </UnifiedCardContent>\n            </UnifiedCard>\n          </PageSection>\n        </Grid>\n\n        {/* المقاييس الفنية */}\n        <Grid item xs={12} lg={6}>\n          <PageSection title=\"الأداء الفني\">\n            <UnifiedCard>\n              <UnifiedCardContent>\n                <Grid container spacing={2}>\n                  {technicalMetrics.map((metric, index) => (\n                    <Grid item xs={12} key={index}>\n                      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n                        <Typography variant=\"body2\">{metric.name}</Typography>\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"body2\" fontWeight=\"bold\">\n                            {metric.value} {metric.unit}\n                          </Typography>\n                          <Chip\n                            icon={getStatusIcon(metric.status)}\n                            label={metric.status === 'excellent' ? 'ممتاز' : \n                                   metric.status === 'good' ? 'جيد' : \n                                   metric.status === 'warning' ? 'تحذير' : 'حرج'}\n                            color={getStatusColor(metric.status)}\n                            size=\"small\"\n                          />\n                        </Box>\n                      </Box>\n                      <LinearProgress \n                        variant=\"determinate\" \n                        value={metric.name.includes('معدل الأخطاء') ? (1 - metric.value / 100) * 100 : \n                               metric.name.includes('وقت الاستجابة') ? Math.max(0, 100 - metric.value / 5) :\n                               metric.value > 100 ? 100 : metric.value} \n                        color={getStatusColor(metric.status)}\n                        sx={{ height: 8, borderRadius: 4 }}\n                      />\n                    </Grid>\n                  ))}\n                </Grid>\n              </UnifiedCardContent>\n            </UnifiedCard>\n          </PageSection>\n        </Grid>\n\n        {/* أحداث النظام الأخيرة */}\n        <Grid item xs={12} lg={6}>\n          <PageSection title=\"أحداث النظام الأخيرة\">\n            <UnifiedCard>\n              <UnifiedCardContent>\n                <Box maxHeight={300} overflow=\"auto\">\n                  {recentEvents.map((event) => (\n                    <Box key={event.id} display=\"flex\" alignItems=\"flex-start\" gap={2} mb={2} p={2} \n                         border={1} borderColor=\"divider\" borderRadius={2}>\n                      {getSeverityIcon(event.severity)}\n                      <Box flex={1}>\n                        <Typography variant=\"body2\" gutterBottom>\n                          {event.message}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {event.timestamp}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  ))}\n                </Box>\n              </UnifiedCardContent>\n            </UnifiedCard>\n          </PageSection>\n        </Grid>\n      </Grid>\n\n      {/* ملخص سريع */}\n      <PageSection title=\"ملخص سريع\">\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={4}>\n            <Card variant=\"outlined\">\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom color=\"primary\">\n                  الأداء اليوم\n                </Typography>\n                <Typography variant=\"body2\">\n                  • {systemStats.dailyActiveUsers} مستخدم نشط\n                </Typography>\n                <Typography variant=\"body2\">\n                  • 15 شركة جديدة\n                </Typography>\n                <Typography variant=\"body2\">\n                  • 89 حملة تم إطلاقها\n                </Typography>\n                <Typography variant=\"body2\">\n                  • 0 مشاكل فنية\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n          \n          <Grid item xs={12} md={4}>\n            <Card variant=\"outlined\">\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom color=\"secondary\">\n                  التحديثات المطلوبة\n                </Typography>\n                <Typography variant=\"body2\">\n                  • تحديث أمني متاح\n                </Typography>\n                <Typography variant=\"body2\">\n                  • توسيع مساحة التخزين\n                </Typography>\n                <Typography variant=\"body2\">\n                  • مراجعة إعدادات النسخ الاحتياطي\n                </Typography>\n                <Typography variant=\"body2\">\n                  • تحديث شهادات SSL\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n          \n          <Grid item xs={12} md={4}>\n            <Card variant=\"outlined\">\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom color=\"success.main\">\n                  الإنجازات الأخيرة\n                </Typography>\n                <Typography variant=\"body2\">\n                  • وصلنا إلى 1000+ مستخدم\n                </Typography>\n                <Typography variant=\"body2\">\n                  • تحسين الأداء بنسبة 25%\n                </Typography>\n                <Typography variant=\"body2\">\n                  • إطلاق ميزات جديدة\n                </Typography>\n                <Typography variant=\"body2\">\n                  • تحقيق 99.9% وقت تشغيل\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </PageSection>\n    </PageContainer>\n  );\n};\n\nexport default SystemOverview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,SAAS,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SACEC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,kBAAkB,EAClBC,SAAS,EACTC,SAAS,QACJ,4BAA4B;AACnC,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAE7C,OAAO,IAAI8C,eAAe,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9K,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,WAAW,CAAC;;EAE7D;EACA,MAAM2E,WAAW,GAAG;IAClBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,GAAG;IAChBC,cAAc,EAAE,GAAG;IACnBC,eAAe,EAAE,GAAG;IACpBC,YAAY,EAAE,OAAO;IACrBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE;EACpB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,CACtB;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,EACrE;IAAEH,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAQ,CAAC,CACtE;;EAED;EACA,MAAMC,gBAAgB,GAAG,CACvB;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtD;IAAEF,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;;EAED;EACA,MAAMC,gBAAgB,GAAG,CACvB;IAAEH,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,GAAG;IAAEG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAO,CAAC,EACjE;IAAEL,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,EAAE;IAAEG,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAY,CAAC,EACtE;IAAEL,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,EAAE;IAAEG,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAO,CAAC,EACjE;IAAEL,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,EAAE;IAAEG,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAU,CAAC,EACpE;IAAEL,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,IAAI;IAAEG,IAAI,EAAE,OAAO;IAAEC,MAAM,EAAE;EAAY,CAAC,EAC9E;IAAEL,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,IAAI;IAAEG,IAAI,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAY,CAAC,CACtE;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,yCAAyC;IAClDC,SAAS,EAAE,kBAAkB;IAC7BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,8BAA8B;IACvCC,SAAS,EAAE,kBAAkB;IAC7BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,6CAA6C;IACtDC,SAAS,EAAE,kBAAkB;IAC7BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,sCAAsC;IAC/CC,SAAS,EAAE,kBAAkB;IAC7BC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,cAAc,GAAIP,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,UAAU;QAAE,OAAO,OAAO;MAC/B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMQ,aAAa,GAAIR,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,oBAAO7B,OAAA,CAAC1B,SAAS;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,MAAM;QAAE,oBAAOzC,OAAA,CAACtB,QAAQ;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,SAAS;QAAE,oBAAOzC,OAAA,CAAC5B,WAAW;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,UAAU;QAAE,oBAAOzC,OAAA,CAACxB,SAAS;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC;QAAS,oBAAOzC,OAAA,CAACtB,QAAQ;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,eAAe,GAAIP,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,SAAS;QAAE,oBAAOnC,OAAA,CAAC1B,SAAS;UAACoD,KAAK,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,SAAS;QAAE,oBAAOzC,OAAA,CAAC5B,WAAW;UAACsD,KAAK,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,OAAO;QAAE,oBAAOzC,OAAA,CAACxB,SAAS;UAACkD,KAAK,EAAC;QAAO;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD;QAAS,oBAAOzC,OAAA,CAACtB,QAAQ;UAACgD,KAAK,EAAC;QAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3C;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BvC,UAAU,CAAC,IAAI,CAAC;IAChBwC,UAAU,CAAC,MAAMxC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEJ,OAAA,CAACrB,aAAa;IACZkE,KAAK,EAAC,2GAAsB;IAC5BC,QAAQ,EAAC,6PAAgD;IACzDC,WAAW,EAAE,CACX;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAmB,CAAC,EAClD;MAAED,KAAK,EAAE;IAAuB,CAAC,CACjC;IACFE,OAAO,EAAE,cACPlD,OAAA,CAACxD,OAAO;MAAeqG,KAAK,EAAC,iFAAgB;MAAAM,QAAA,eAC3CnD,OAAA,CAACzD,UAAU;QAAC6G,OAAO,EAAET,aAAc;QAAAQ,QAAA,eACjCnD,OAAA,CAAChC,WAAW;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC,GAHF,SAAS;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIb,CAAC,eACVzC,OAAA,CAAC/D,MAAM;MAAcoH,OAAO,EAAC,UAAU;MAACC,SAAS,eAAEtD,OAAA,CAAC9B,YAAY;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAU,QAAA,EAAC;IAErE,GAFY,QAAQ;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEZ,CAAC,CACT;IAAAU,QAAA,gBAGFnD,OAAA,CAACpB,WAAW;MAACiE,KAAK,EAAC,+DAAa;MAAAM,QAAA,eAC9BnD,OAAA,CAAC5D,KAAK;QACJ+F,QAAQ,EAAE9B,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG,SAAU;QAC/DkD,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBAEdnD,OAAA,CAAChE,UAAU;UAACqH,OAAO,EAAC,OAAO;UAAAF,QAAA,gBACzBnD,OAAA;YAAAmD,QAAA,EAAQ;UAAmB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,2JACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;UAACqH,OAAO,EAAC,OAAO;UAAAF,QAAA,GAAC,iEACb,EAAC5C,WAAW,CAACO,YAAY,EAAC,sHACzC;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGdzC,OAAA,CAACpB,WAAW;MAACiE,KAAK,EAAC,+GAAqB;MAAAM,QAAA,eACtCnD,OAAA,CAAChB,SAAS;QAAAmE,QAAA,gBACRnD,OAAA,CAACjB,SAAS;UACR8D,KAAK,EAAC,mGAAmB;UACzBpB,KAAK,EAAElB,WAAW,CAACC,UAAU,CAACiD,cAAc,CAAC,CAAE;UAC/CC,MAAM,EAAE,GAAGnD,WAAW,CAACE,WAAW,MAAO;UACzCkD,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAE5D,OAAA,CAAC5C,UAAU;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBf,KAAK,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFzC,OAAA,CAACjB,SAAS;UACR8D,KAAK,EAAC,iFAAgB;UACtBpB,KAAK,EAAElB,WAAW,CAACG,cAAe;UAClCgD,MAAM,EAAE,GAAGnD,WAAW,CAACI,eAAe,OAAQ;UAC9CgD,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAE5D,OAAA,CAAC1C,YAAY;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBf,KAAK,EAAC;QAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACFzC,OAAA,CAACjB,SAAS;UACR8D,KAAK,EAAC,6FAAkB;UACxBpB,KAAK,EAAE,GAAG,CAAClB,WAAW,CAACK,YAAY,GAAG,OAAO,EAAEiD,OAAO,CAAC,CAAC,CAAC,OAAQ;UACjEH,MAAM,EAAE,IAAInD,WAAW,CAACM,aAAa,aAAc;UACnD8C,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAE5D,OAAA,CAACxC,SAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBf,KAAK,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACFzC,OAAA,CAACjB,SAAS;UACR8D,KAAK,EAAC,+DAAa;UACnBpB,KAAK,EAAE,GAAGlB,WAAW,CAACO,YAAY,GAAI;UACtC4C,MAAM,EAAC,2EAAe;UACtBC,UAAU,EAAC,UAAU;UACrBC,IAAI,eAAE5D,OAAA,CAACtC,YAAY;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBf,KAAK,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEdzC,OAAA,CAACjE,IAAI;MAAC+H,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAZ,QAAA,gBAEzBnD,OAAA,CAACjE,IAAI;QAACiI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACvBnD,OAAA,CAACpB,WAAW;UAACiE,KAAK,EAAC,2GAAsB;UAAAM,QAAA,eACvCnD,OAAA,CAACnB,WAAW;YAAAsE,QAAA,eACVnD,OAAA,CAAClB,kBAAkB;cAAAqE,QAAA,eACjBnD,OAAA,CAACT,mBAAmB;gBAAC4E,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE,GAAI;gBAAAjB,QAAA,eAC5CnD,OAAA,CAACR,SAAS;kBAAC6E,IAAI,EAAEnD,eAAgB;kBAAAiC,QAAA,gBAC/BnD,OAAA,CAACX,aAAa;oBAACiF,eAAe,EAAC;kBAAK;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvCzC,OAAA,CAACb,KAAK;oBAACoF,OAAO,EAAC;kBAAM;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBzC,OAAA,CAACZ,KAAK;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTzC,OAAA,CAACV,eAAe;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnBzC,OAAA,CAACP,IAAI;oBAACuC,IAAI,EAAC,UAAU;oBAACuC,OAAO,EAAC,OAAO;oBAACC,OAAO,EAAC,GAAG;oBAACC,MAAM,EAAC,SAAS;oBAACC,IAAI,EAAC,SAAS;oBAAClD,IAAI,EAAC;kBAAY;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtGzC,OAAA,CAACP,IAAI;oBAACuC,IAAI,EAAC,UAAU;oBAACuC,OAAO,EAAC,WAAW;oBAACC,OAAO,EAAC,GAAG;oBAACC,MAAM,EAAC,SAAS;oBAACC,IAAI,EAAC,SAAS;oBAAClD,IAAI,EAAC;kBAAS;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGPzC,OAAA,CAACjE,IAAI;QAACiI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACvBnD,OAAA,CAACpB,WAAW;UAACiE,KAAK,EAAC,6FAAkB;UAAAM,QAAA,eACnCnD,OAAA,CAACnB,WAAW;YAAAsE,QAAA,eACVnD,OAAA,CAAClB,kBAAkB;cAAAqE,QAAA,gBACjBnD,OAAA,CAACT,mBAAmB;gBAAC4E,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE,GAAI;gBAAAjB,QAAA,eAC5CnD,OAAA,CAACJ,QAAQ;kBAAAuD,QAAA,gBACPnD,OAAA,CAACH,GAAG;oBACFwE,IAAI,EAAE9C,gBAAiB;oBACvBoD,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,WAAW,EAAE,EAAG;oBAChBH,IAAI,EAAC,SAAS;oBACdH,OAAO,EAAC,OAAO;oBACfvB,KAAK,EAAEA,CAAC;sBAAExB,IAAI;sBAAEC;oBAAM,CAAC,KAAK,GAAGA,KAAK,GAAI;oBAAA0B,QAAA,EAEvC5B,gBAAgB,CAACuD,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACjChF,OAAA,CAACF,IAAI;sBAAuB4E,IAAI,EAAEK,KAAK,CAACrD;oBAAM,GAAnC,QAAQsD,KAAK,EAAE;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAsB,CACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNzC,OAAA,CAACV,eAAe;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eACtBzC,OAAA,CAAClE,GAAG;gBAACmJ,EAAE,EAAE,CAAE;gBAAA9B,QAAA,EACR5B,gBAAgB,CAACuD,GAAG,CAAC,CAACd,IAAI,EAAEgB,KAAK,kBAChChF,OAAA,CAAClE,GAAG;kBAAaoJ,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,cAAc,EAAC,eAAe;kBAAC5B,EAAE,EAAE,CAAE;kBAAAL,QAAA,gBACvFnD,OAAA,CAAClE,GAAG;oBAACoJ,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACE,GAAG,EAAE,CAAE;oBAAAlC,QAAA,gBAC7CnD,OAAA,CAAClE,GAAG;sBACFqI,KAAK,EAAE,EAAG;sBACVC,MAAM,EAAE,EAAG;sBACXkB,OAAO,EAAEtB,IAAI,CAACtC,KAAM;sBACpB6D,YAAY,EAAC;oBAAK;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACFzC,OAAA,CAAChE,UAAU;sBAACqH,OAAO,EAAC,OAAO;sBAAAF,QAAA,EAAEa,IAAI,CAACxC;oBAAI;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACNzC,OAAA,CAAChE,UAAU;oBAACqH,OAAO,EAAC,OAAO;oBAACmC,UAAU,EAAC,MAAM;oBAAArC,QAAA,GAC1Ca,IAAI,CAACvC,KAAK,EAAC,GACd;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAZLuC,KAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGPzC,OAAA,CAACjE,IAAI;QAACiI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACvBnD,OAAA,CAACpB,WAAW;UAACiE,KAAK,EAAC,qEAAc;UAAAM,QAAA,eAC/BnD,OAAA,CAACnB,WAAW;YAAAsE,QAAA,eACVnD,OAAA,CAAClB,kBAAkB;cAAAqE,QAAA,eACjBnD,OAAA,CAACjE,IAAI;gBAAC+H,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAZ,QAAA,EACxBxB,gBAAgB,CAACmD,GAAG,CAAC,CAACW,MAAM,EAAET,KAAK,kBAClChF,OAAA,CAACjE,IAAI;kBAACiI,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAAd,QAAA,gBAChBnD,OAAA,CAAClE,GAAG;oBAACoJ,OAAO,EAAC,MAAM;oBAACE,cAAc,EAAC,eAAe;oBAACD,UAAU,EAAC,QAAQ;oBAAC3B,EAAE,EAAE,CAAE;oBAAAL,QAAA,gBAC3EnD,OAAA,CAAChE,UAAU;sBAACqH,OAAO,EAAC,OAAO;sBAAAF,QAAA,EAAEsC,MAAM,CAACjE;oBAAI;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACtDzC,OAAA,CAAClE,GAAG;sBAACoJ,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACE,GAAG,EAAE,CAAE;sBAAAlC,QAAA,gBAC7CnD,OAAA,CAAChE,UAAU;wBAACqH,OAAO,EAAC,OAAO;wBAACmC,UAAU,EAAC,MAAM;wBAAArC,QAAA,GAC1CsC,MAAM,CAAChE,KAAK,EAAC,GAAC,EAACgE,MAAM,CAAC7D,IAAI;sBAAA;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACbzC,OAAA,CAAC1D,IAAI;wBACHsH,IAAI,EAAEvB,aAAa,CAACoD,MAAM,CAAC5D,MAAM,CAAE;wBACnCmB,KAAK,EAAEyC,MAAM,CAAC5D,MAAM,KAAK,WAAW,GAAG,OAAO,GACvC4D,MAAM,CAAC5D,MAAM,KAAK,MAAM,GAAG,KAAK,GAChC4D,MAAM,CAAC5D,MAAM,KAAK,SAAS,GAAG,OAAO,GAAG,KAAM;wBACrDH,KAAK,EAAEU,cAAc,CAACqD,MAAM,CAAC5D,MAAM,CAAE;wBACrC6D,IAAI,EAAC;sBAAO;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzC,OAAA,CAAC3D,cAAc;oBACbgH,OAAO,EAAC,aAAa;oBACrB5B,KAAK,EAAEgE,MAAM,CAACjE,IAAI,CAACmE,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAGF,MAAM,CAAChE,KAAK,GAAG,GAAG,IAAI,GAAG,GACrEgE,MAAM,CAACjE,IAAI,CAACmE,QAAQ,CAAC,eAAe,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAGJ,MAAM,CAAChE,KAAK,GAAG,CAAC,CAAC,GAC3EgE,MAAM,CAAChE,KAAK,GAAG,GAAG,GAAG,GAAG,GAAGgE,MAAM,CAAChE,KAAM;oBAC/CC,KAAK,EAAEU,cAAc,CAACqD,MAAM,CAAC5D,MAAM,CAAE;oBACrC0B,EAAE,EAAE;sBAAEa,MAAM,EAAE,CAAC;sBAAEmB,YAAY,EAAE;oBAAE;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA,GAxBoBuC,KAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBvB,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGPzC,OAAA,CAACjE,IAAI;QAACiI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACvBnD,OAAA,CAACpB,WAAW;UAACiE,KAAK,EAAC,gHAAsB;UAAAM,QAAA,eACvCnD,OAAA,CAACnB,WAAW;YAAAsE,QAAA,eACVnD,OAAA,CAAClB,kBAAkB;cAAAqE,QAAA,eACjBnD,OAAA,CAAClE,GAAG;gBAACgK,SAAS,EAAE,GAAI;gBAACC,QAAQ,EAAC,MAAM;gBAAA5C,QAAA,EACjCrB,YAAY,CAACgD,GAAG,CAAEkB,KAAK,iBACtBhG,OAAA,CAAClE,GAAG;kBAAgBoJ,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,YAAY;kBAACE,GAAG,EAAE,CAAE;kBAAC7B,EAAE,EAAE,CAAE;kBAACyC,CAAC,EAAE,CAAE;kBAC1EC,MAAM,EAAE,CAAE;kBAACC,WAAW,EAAC,SAAS;kBAACZ,YAAY,EAAE,CAAE;kBAAApC,QAAA,GACnDT,eAAe,CAACsD,KAAK,CAAC7D,QAAQ,CAAC,eAChCnC,OAAA,CAAClE,GAAG;oBAACsK,IAAI,EAAE,CAAE;oBAAAjD,QAAA,gBACXnD,OAAA,CAAChE,UAAU;sBAACqH,OAAO,EAAC,OAAO;sBAACgD,YAAY;sBAAAlD,QAAA,EACrC6C,KAAK,CAAC/D;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACbzC,OAAA,CAAChE,UAAU;sBAACqH,OAAO,EAAC,SAAS;sBAAC3B,KAAK,EAAC,gBAAgB;sBAAAyB,QAAA,EACjD6C,KAAK,CAAC9D;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GAVEuD,KAAK,CAACjE,EAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWb,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPzC,OAAA,CAACpB,WAAW;MAACiE,KAAK,EAAC,mDAAW;MAAAM,QAAA,eAC5BnD,OAAA,CAACjE,IAAI;QAAC+H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,gBACzBnD,OAAA,CAACjE,IAAI;UAACiI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACqC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACvBnD,OAAA,CAAC9D,IAAI;YAACmH,OAAO,EAAC,UAAU;YAAAF,QAAA,eACtBnD,OAAA,CAAC7D,WAAW;cAAAgH,QAAA,gBACVnD,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,IAAI;gBAACgD,YAAY;gBAAC3E,KAAK,EAAC,SAAS;gBAAAyB,QAAA,EAAC;cAEtD;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,GAAC,SACxB,EAAC5C,WAAW,CAACU,gBAAgB,EAAC,0DAClC;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPzC,OAAA,CAACjE,IAAI;UAACiI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACqC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACvBnD,OAAA,CAAC9D,IAAI;YAACmH,OAAO,EAAC,UAAU;YAAAF,QAAA,eACtBnD,OAAA,CAAC7D,WAAW;cAAAgH,QAAA,gBACVnD,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,IAAI;gBAACgD,YAAY;gBAAC3E,KAAK,EAAC,WAAW;gBAAAyB,QAAA,EAAC;cAExD;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPzC,OAAA,CAACjE,IAAI;UAACiI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACqC,EAAE,EAAE,CAAE;UAAAnD,QAAA,eACvBnD,OAAA,CAAC9D,IAAI;YAACmH,OAAO,EAAC,UAAU;YAAAF,QAAA,eACtBnD,OAAA,CAAC7D,WAAW;cAAAgH,QAAA,gBACVnD,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,IAAI;gBAACgD,YAAY;gBAAC3E,KAAK,EAAC,cAAc;gBAAAyB,QAAA,EAAC;cAE3D;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAChE,UAAU;gBAACqH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAE5B;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAACvC,EAAA,CAvYID,cAAc;AAAAsG,EAAA,GAAdtG,cAAc;AAyYpB,eAAeA,cAAc;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}