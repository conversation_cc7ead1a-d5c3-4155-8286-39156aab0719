{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js\";\nimport React from 'react';\nimport { Card as MuiCard, CardContent as <PERSON><PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Card = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCard, {\n    className: className,\n    sx: {\n      boxShadow: 1,\n      borderRadius: 2,\n      ...props.sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Card;\nexport const CardContent = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCardContent, {\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c2 = CardContent;\nexport const CardHeader = ({\n  children,\n  className,\n  title,\n  description,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCardHeader, {\n    className: className,\n    title: title,\n    subheader: description,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c3 = CardHeader;\nexport const CardTitle = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `text-lg font-semibold ${className}`,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_c4 = CardTitle;\nexport const CardDescription = ({\n  children,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `text-sm text-gray-600 ${className}`,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_c5 = CardDescription;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c2, \"CardContent\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle\");\n$RefreshReg$(_c5, \"CardDescription\");", "map": {"version": 3, "names": ["React", "Card", "MuiCard", "<PERSON><PERSON><PERSON><PERSON>", "MuiCardContent", "<PERSON><PERSON><PERSON><PERSON>", "MuiCardHeader", "jsxDEV", "_jsxDEV", "children", "className", "props", "sx", "boxShadow", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "_c2", "title", "description", "subheader", "_c3", "CardTitle", "_c4", "CardDescription", "_c5", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';\n\nexport const Card = ({ children, className, ...props }) => {\n  return (\n    <MuiCard \n      className={className}\n      sx={{ \n        boxShadow: 1,\n        borderRadius: 2,\n        ...props.sx \n      }}\n      {...props}\n    >\n      {children}\n    </MuiCard>\n  );\n};\n\nexport const CardContent = ({ children, className, ...props }) => {\n  return (\n    <MuiCardContent \n      className={className}\n      {...props}\n    >\n      {children}\n    </MuiCardContent>\n  );\n};\n\nexport const CardHeader = ({ children, className, title, description, ...props }) => {\n  return (\n    <MuiCardHeader \n      className={className}\n      title={title}\n      subheader={description}\n      {...props}\n    >\n      {children}\n    </MuiCardHeader>\n  );\n};\n\nexport const CardTitle = ({ children, className, ...props }) => {\n  return (\n    <div className={`text-lg font-semibold ${className}`} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport const CardDescription = ({ children, className, ...props }) => {\n  return (\n    <div className={`text-sm text-gray-600 ${className}`} {...props}>\n      {children}\n    </div>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,IAAIC,OAAO,EAAEC,WAAW,IAAIC,cAAc,EAAEC,UAAU,IAAIC,aAAa,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5G,OAAO,MAAMP,IAAI,GAAGA,CAAC;EAAEQ,QAAQ;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACzD,oBACEH,OAAA,CAACN,OAAO;IACNQ,SAAS,EAAEA,SAAU;IACrBE,EAAE,EAAE;MACFC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE,CAAC;MACf,GAAGH,KAAK,CAACC;IACX,CAAE;IAAA,GACED,KAAK;IAAAF,QAAA,EAERA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC;AAACC,EAAA,GAdWlB,IAAI;AAgBjB,OAAO,MAAME,WAAW,GAAGA,CAAC;EAAEM,QAAQ;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAChE,oBACEH,OAAA,CAACJ,cAAc;IACbM,SAAS,EAAEA,SAAU;IAAA,GACjBC,KAAK;IAAAF,QAAA,EAERA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAACE,GAAA,GATWjB,WAAW;AAWxB,OAAO,MAAME,UAAU,GAAGA,CAAC;EAAEI,QAAQ;EAAEC,SAAS;EAAEW,KAAK;EAAEC,WAAW;EAAE,GAAGX;AAAM,CAAC,KAAK;EACnF,oBACEH,OAAA,CAACF,aAAa;IACZI,SAAS,EAAEA,SAAU;IACrBW,KAAK,EAAEA,KAAM;IACbE,SAAS,EAAED,WAAY;IAAA,GACnBX,KAAK;IAAAF,QAAA,EAERA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB,CAAC;AAACM,GAAA,GAXWnB,UAAU;AAavB,OAAO,MAAMoB,SAAS,GAAGA,CAAC;EAAEhB,QAAQ;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EAC9D,oBACEH,OAAA;IAAKE,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAAA,GAAKC,KAAK;IAAAF,QAAA,EAC5DA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACQ,GAAA,GANWD,SAAS;AAQtB,OAAO,MAAME,eAAe,GAAGA,CAAC;EAAElB,QAAQ;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACpE,oBACEH,OAAA;IAAKE,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAAA,GAAKC,KAAK;IAAAF,QAAA,EAC5DA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACU,GAAA,GANWD,eAAe;AAAA,IAAAR,EAAA,EAAAC,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}