{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport { exactProp, HTMLElementType, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef, unstable_setRef as setRef, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://mui.com/material-ui/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://mui.com/material-ui/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, forwardedRef) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  const handleRef = useForkRef(/*#__PURE__*/React.isValidElement(children) ? getReactElementRef(children) : null, forwardedRef);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(forwardedRef, mountNode);\n      return () => {\n        setRef(forwardedRef, null);\n      };\n    }\n    return undefined;\n  }, [forwardedRef, mountNode, disablePortal]);\n  if (disablePortal) {\n    if (/*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return /*#__PURE__*/_jsx(React.Fragment, {\n      children: children\n    });\n  }\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;", "map": {"version": 3, "names": ["React", "ReactDOM", "PropTypes", "exactProp", "HTMLElementType", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useForkRef", "useForkRef", "unstable_setRef", "setRef", "unstable_getReactElementRef", "getReactElementRef", "jsx", "_jsx", "getContainer", "container", "Portal", "forwardRef", "props", "forwardedRef", "children", "disable<PERSON><PERSON><PERSON>", "mountNode", "setMountNode", "useState", "handleRef", "isValidElement", "document", "body", "undefined", "newProps", "ref", "cloneElement", "Fragment", "createPortal", "process", "env", "NODE_ENV", "propTypes", "node", "oneOfType", "func", "bool"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmind_complete/static/unified/node_modules/@mui/material/Portal/Portal.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport { exactProp, HTMLElementType, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef, unstable_setRef as setRef, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://mui.com/material-ui/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://mui.com/material-ui/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, forwardedRef) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  const handleRef = useForkRef( /*#__PURE__*/React.isValidElement(children) ? getReactElementRef(children) : null, forwardedRef);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(forwardedRef, mountNode);\n      return () => {\n        setRef(forwardedRef, null);\n      };\n    }\n    return undefined;\n  }, [forwardedRef, mountNode, disablePortal]);\n  if (disablePortal) {\n    if ( /*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return /*#__PURE__*/_jsx(React.Fragment, {\n      children: children\n    });\n  }\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,EAAEC,eAAe,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,eAAe,IAAIC,MAAM,EAAEC,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AACzN,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC/B,OAAO,OAAOA,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC,CAAC,GAAGA,SAAS;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,MAAMA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAChF,MAAM;IACJC,QAAQ;IACRL,SAAS;IACTM,aAAa,GAAG;EAClB,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMC,SAAS,GAAGlB,UAAU,CAAE,aAAaR,KAAK,CAAC2B,cAAc,CAACN,QAAQ,CAAC,GAAGT,kBAAkB,CAACS,QAAQ,CAAC,GAAG,IAAI,EAAED,YAAY,CAAC;EAC9Hd,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACgB,aAAa,EAAE;MAClBE,YAAY,CAACT,YAAY,CAACC,SAAS,CAAC,IAAIY,QAAQ,CAACC,IAAI,CAAC;IACxD;EACF,CAAC,EAAE,CAACb,SAAS,EAAEM,aAAa,CAAC,CAAC;EAC9BhB,iBAAiB,CAAC,MAAM;IACtB,IAAIiB,SAAS,IAAI,CAACD,aAAa,EAAE;MAC/BZ,MAAM,CAACU,YAAY,EAAEG,SAAS,CAAC;MAC/B,OAAO,MAAM;QACXb,MAAM,CAACU,YAAY,EAAE,IAAI,CAAC;MAC5B,CAAC;IACH;IACA,OAAOU,SAAS;EAClB,CAAC,EAAE,CAACV,YAAY,EAAEG,SAAS,EAAED,aAAa,CAAC,CAAC;EAC5C,IAAIA,aAAa,EAAE;IACjB,IAAK,aAAatB,KAAK,CAAC2B,cAAc,CAACN,QAAQ,CAAC,EAAE;MAChD,MAAMU,QAAQ,GAAG;QACfC,GAAG,EAAEN;MACP,CAAC;MACD,OAAO,aAAa1B,KAAK,CAACiC,YAAY,CAACZ,QAAQ,EAAEU,QAAQ,CAAC;IAC5D;IACA,OAAO,aAAajB,IAAI,CAACd,KAAK,CAACkC,QAAQ,EAAE;MACvCb,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;EACA,OAAO,aAAaP,IAAI,CAACd,KAAK,CAACkC,QAAQ,EAAE;IACvCb,QAAQ,EAAEE,SAAS,GAAG,aAAatB,QAAQ,CAACkC,YAAY,CAACd,QAAQ,EAAEE,SAAS,CAAC,GAAGA;EAClF,CAAC,CAAC;AACJ,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,MAAM,CAACsB,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACElB,QAAQ,EAAEnB,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,SAAS,EAAEd,SAAS,CAAC,sCAAsCuC,SAAS,CAAC,CAACrC,eAAe,EAAEF,SAAS,CAACwC,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACEpB,aAAa,EAAEpB,SAAS,CAACyC;AAC3B,CAAC,GAAG,KAAK,CAAC;AACV,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACArB,MAAM,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGd,SAAS,CAACc,MAAM,CAACsB,SAAS,CAAC;AACxD;AACA,eAAetB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}