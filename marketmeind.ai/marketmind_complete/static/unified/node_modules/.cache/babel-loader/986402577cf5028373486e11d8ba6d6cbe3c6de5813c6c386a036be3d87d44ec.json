{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js\";\nimport React from 'react';\nimport { TextField } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Textarea = ({\n  placeholder,\n  value,\n  onChange,\n  rows = 4,\n  className,\n  disabled = false,\n  fullWidth = true,\n  variant = 'outlined',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(TextField, {\n    placeholder: placeholder,\n    value: value,\n    onChange: onChange,\n    multiline: true,\n    rows: rows,\n    className: className,\n    disabled: disabled,\n    fullWidth: fullWidth,\n    variant: variant,\n    sx: {\n      '& .MuiOutlinedInput-root': {\n        borderRadius: 2\n      },\n      ...props.sx\n    },\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_c = Textarea;\nvar _c;\n$RefreshReg$(_c, \"Textarea\");", "map": {"version": 3, "names": ["React", "TextField", "jsxDEV", "_jsxDEV", "Textarea", "placeholder", "value", "onChange", "rows", "className", "disabled", "fullWidth", "variant", "props", "multiline", "sx", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js"], "sourcesContent": ["import React from 'react';\nimport { TextField } from '@mui/material';\n\nexport const Textarea = ({ \n  placeholder,\n  value,\n  onChange,\n  rows = 4,\n  className,\n  disabled = false,\n  fullWidth = true,\n  variant = 'outlined',\n  ...props \n}) => {\n  return (\n    <TextField\n      placeholder={placeholder}\n      value={value}\n      onChange={onChange}\n      multiline\n      rows={rows}\n      className={className}\n      disabled={disabled}\n      fullWidth={fullWidth}\n      variant={variant}\n      sx={{\n        '& .MuiOutlinedInput-root': {\n          borderRadius: 2,\n        },\n        ...props.sx\n      }}\n      {...props}\n    />\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,WAAW;EACXC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,CAAC;EACRC,SAAS;EACTC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,IAAI;EAChBC,OAAO,GAAG,UAAU;EACpB,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEV,OAAA,CAACF,SAAS;IACRI,WAAW,EAAEA,WAAY;IACzBC,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBO,SAAS;IACTN,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEA,SAAU;IACrBC,QAAQ,EAAEA,QAAS;IACnBC,SAAS,EAAEA,SAAU;IACrBC,OAAO,EAAEA,OAAQ;IACjBG,EAAE,EAAE;MACF,0BAA0B,EAAE;QAC1BC,YAAY,EAAE;MAChB,CAAC;MACD,GAAGH,KAAK,CAACE;IACX,CAAE;IAAA,GACEF;EAAK;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACC,EAAA,GA/BWjB,QAAQ;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}