{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Dashboard as DashboardIcon, People as PeopleIcon, Business as BusinessIcon, Analytics as AnalyticsIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport AdminHome from './AdminHome';\nimport UserManagement from './UserManagement';\nimport CompanyManagement from './CompanyManagement';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = ({\n  onThemeToggle,\n  isDarkMode\n}) => {\n  const menuItems = [{\n    id: 'dashboard',\n    title: 'لوحة التحكم',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/admin'\n  }, {\n    id: 'users',\n    title: 'إدارة المستخدمين',\n    icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/admin/users'\n  }, {\n    id: 'companies',\n    title: 'إدارة الشركات',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/admin/companies'\n  }, {\n    id: 'analytics',\n    title: 'تحليلات النظام',\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/admin/analytics'\n  }, {\n    id: 'settings',\n    title: 'إعدادات النظام',\n    icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard/admin/settings'\n  }];\n  return /*#__PURE__*/_jsxDEV(DashboardLayout, {\n    title: \"MarketMind Admin\",\n    menuItems: menuItems,\n    onThemeToggle: onThemeToggle,\n    isDarkMode: isDarkMode,\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(AdminHome, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/users\",\n        element: /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/companies\",\n        element: /*#__PURE__*/_jsxDEV(CompanyManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/analytics\",\n        element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n          title: \"\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n          description: \"\\u062A\\u062D\\u0644\\u064A\\u0644\\u0627\\u062A \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0644\\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0642\\u062F\\u0645\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/settings\",\n        element: /*#__PURE__*/_jsxDEV(ComingSoon, {\n          title: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\",\n          description: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0645\\u062A\\u0642\\u062F\\u0645\\u0629 \\u0644\\u0644\\u062A\\u062D\\u0643\\u0645 \\u0641\\u064A \\u062C\\u0645\\u064A\\u0639 \\u062C\\u0648\\u0627\\u0646\\u0628 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Dashboard", "DashboardIcon", "People", "PeopleIcon", "Business", "BusinessIcon", "Analytics", "AnalyticsIcon", "Settings", "SettingsIcon", "DashboardLayout", "AdminHome", "UserManagement", "CompanyManagement", "ComingSoon", "jsxDEV", "_jsxDEV", "AdminDashboard", "onThemeToggle", "isDarkMode", "menuItems", "id", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "element", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport {\n  Dashboard as DashboardIcon,\n  People as PeopleIcon,\n  Business as BusinessIcon,\n  Analytics as AnalyticsIcon,\n  Settings as SettingsIcon\n} from '@mui/icons-material';\nimport DashboardLayout from '../../shared/components/layout/DashboardLayout';\nimport AdminHome from './AdminHome';\nimport UserManagement from './UserManagement';\nimport CompanyManagement from './CompanyManagement';\nimport ComingSoon from '../../shared/components/common/ComingSoon';\n\nconst AdminDashboard = ({ onThemeToggle, isDarkMode }) => {\n  const menuItems = [\n    {\n      id: 'dashboard',\n      title: 'لوحة التحكم',\n      icon: <DashboardIcon />,\n      path: '/dashboard/admin'\n    },\n    {\n      id: 'users',\n      title: 'إدارة المستخدمين',\n      icon: <PeopleIcon />,\n      path: '/dashboard/admin/users'\n    },\n    {\n      id: 'companies',\n      title: 'إدارة الشركات',\n      icon: <BusinessIcon />,\n      path: '/dashboard/admin/companies'\n    },\n    {\n      id: 'analytics',\n      title: 'تحليلات النظام',\n      icon: <AnalyticsIcon />,\n      path: '/dashboard/admin/analytics'\n    },\n    {\n      id: 'settings',\n      title: 'إعدادات النظام',\n      icon: <SettingsIcon />,\n      path: '/dashboard/admin/settings'\n    }\n  ];\n\n  return (\n    <DashboardLayout\n      title=\"MarketMind Admin\"\n      menuItems={menuItems}\n      onThemeToggle={onThemeToggle}\n      isDarkMode={isDarkMode}\n    >\n      <Routes>\n        <Route path=\"/\" element={<AdminHome />} />\n        <Route path=\"/users\" element={<UserManagement />} />\n        <Route path=\"/companies\" element={<CompanyManagement />} />\n        <Route path=\"/analytics\" element={\n          <ComingSoon\n            title=\"تحليلات النظام\"\n            description=\"تحليلات شاملة لأداء النظام والإحصائيات المتقدمة\"\n          />\n        } />\n        <Route path=\"/settings\" element={\n          <ComingSoon\n            title=\"إعدادات النظام\"\n            description=\"إعدادات متقدمة للتحكم في جميع جوانب النظام\"\n          />\n        } />\n      </Routes>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gDAAgD;AAC5E,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,UAAU,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,cAAc,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAW,CAAC,KAAK;EACxD,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEP,OAAA,CAACf,aAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,eAAEP,OAAA,CAACb,UAAU;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEP,OAAA,CAACX,YAAY;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,eAAEP,OAAA,CAACT,aAAa;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,eAAEP,OAAA,CAACP,YAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEZ,OAAA,CAACN,eAAe;IACdY,KAAK,EAAC,kBAAkB;IACxBF,SAAS,EAAEA,SAAU;IACrBF,aAAa,EAAEA,aAAc;IAC7BC,UAAU,EAAEA,UAAW;IAAAU,QAAA,eAEvBb,OAAA,CAAClB,MAAM;MAAA+B,QAAA,gBACLb,OAAA,CAACjB,KAAK;QAAC6B,IAAI,EAAC,GAAG;QAACE,OAAO,eAAEd,OAAA,CAACL,SAAS;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1CX,OAAA,CAACjB,KAAK;QAAC6B,IAAI,EAAC,QAAQ;QAACE,OAAO,eAAEd,OAAA,CAACJ,cAAc;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDX,OAAA,CAACjB,KAAK;QAAC6B,IAAI,EAAC,YAAY;QAACE,OAAO,eAAEd,OAAA,CAACH,iBAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DX,OAAA,CAACjB,KAAK;QAAC6B,IAAI,EAAC,YAAY;QAACE,OAAO,eAC9Bd,OAAA,CAACF,UAAU;UACTQ,KAAK,EAAC,iFAAgB;UACtBS,WAAW,EAAC;QAAiD;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJX,OAAA,CAACjB,KAAK;QAAC6B,IAAI,EAAC,WAAW;QAACE,OAAO,eAC7Bd,OAAA,CAACF,UAAU;UACTQ,KAAK,EAAC,iFAAgB;UACtBS,WAAW,EAAC;QAA4C;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEtB,CAAC;AAACK,EAAA,GA5DIf,cAAc;AA8DpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}