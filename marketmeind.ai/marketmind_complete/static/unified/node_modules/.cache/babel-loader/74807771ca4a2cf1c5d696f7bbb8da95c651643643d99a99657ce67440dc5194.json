{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js\";\nimport React from 'react';\nimport { FormControl, InputLabel, Select as MuiSelect, MenuItem } from '@mui/material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const Select = ({\n  children,\n  value,\n  onValueChange,\n  placeholder,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(FormControl, {\n    fullWidth: true,\n    className: className,\n    children: [placeholder && /*#__PURE__*/_jsxDEV(InputLabel, {\n      children: placeholder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(MuiSelect, {\n      value: value,\n      onChange: e => onValueChange && onValueChange(e.target.value),\n      label: placeholder,\n      sx: {\n        borderRadius: 2,\n        ...props.sx\n      },\n      ...props,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = Select;\nexport const SelectContent = ({\n  children,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_c2 = SelectContent;\nexport const SelectItem = ({\n  children,\n  value,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(MenuItem, {\n    value: value,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_c3 = SelectItem;\nexport const SelectTrigger = ({\n  children,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_c4 = SelectTrigger;\nexport const SelectValue = ({\n  placeholder,\n  ...props\n}) => {\n  return null; // This is handled by the Select component itself\n};\n_c5 = SelectValue;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Select\");\n$RefreshReg$(_c2, \"SelectContent\");\n$RefreshReg$(_c3, \"SelectItem\");\n$RefreshReg$(_c4, \"SelectTrigger\");\n$RefreshReg$(_c5, \"SelectValue\");", "map": {"version": 3, "names": ["React", "FormControl", "InputLabel", "Select", "MuiSelect", "MenuItem", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "children", "value", "onValueChange", "placeholder", "className", "props", "fullWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "label", "sx", "borderRadius", "_c", "SelectContent", "_c2", "SelectItem", "_c3", "SelectTrigger", "_c4", "SelectValue", "_c5", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js"], "sourcesContent": ["import React from 'react';\nimport { \n  FormControl,\n  InputLabel,\n  Select as MuiSelect,\n  MenuItem\n} from '@mui/material';\n\nexport const Select = ({ \n  children, \n  value,\n  onValueChange,\n  placeholder,\n  className,\n  ...props \n}) => {\n  return (\n    <FormControl fullWidth className={className}>\n      {placeholder && <InputLabel>{placeholder}</InputLabel>}\n      <MuiSelect\n        value={value}\n        onChange={(e) => onValueChange && onValueChange(e.target.value)}\n        label={placeholder}\n        sx={{\n          borderRadius: 2,\n          ...props.sx\n        }}\n        {...props}\n      >\n        {children}\n      </MuiSelect>\n    </FormControl>\n  );\n};\n\nexport const SelectContent = ({ children, ...props }) => {\n  return <>{children}</>;\n};\n\nexport const SelectItem = ({ children, value, ...props }) => {\n  return (\n    <MenuItem value={value} {...props}>\n      {children}\n    </MenuItem>\n  );\n};\n\nexport const SelectTrigger = ({ children, ...props }) => {\n  return <>{children}</>;\n};\n\nexport const SelectValue = ({ placeholder, ...props }) => {\n  return null; // This is handled by the Select component itself\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,WAAW,EACXC,UAAU,EACVC,MAAM,IAAIC,SAAS,EACnBC,QAAQ,QACH,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvB,OAAO,MAAMN,MAAM,GAAGA,CAAC;EACrBO,QAAQ;EACRC,KAAK;EACLC,aAAa;EACbC,WAAW;EACXC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EACJ,oBACER,OAAA,CAACN,WAAW;IAACe,SAAS;IAACF,SAAS,EAAEA,SAAU;IAAAJ,QAAA,GACzCG,WAAW,iBAAIN,OAAA,CAACL,UAAU;MAAAQ,QAAA,EAAEG;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eACtDb,OAAA,CAACH,SAAS;MACRO,KAAK,EAAEA,KAAM;MACbU,QAAQ,EAAGC,CAAC,IAAKV,aAAa,IAAIA,aAAa,CAACU,CAAC,CAACC,MAAM,CAACZ,KAAK,CAAE;MAChEa,KAAK,EAAEX,WAAY;MACnBY,EAAE,EAAE;QACFC,YAAY,EAAE,CAAC;QACf,GAAGX,KAAK,CAACU;MACX,CAAE;MAAA,GACEV,KAAK;MAAAL,QAAA,EAERA;IAAQ;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB,CAAC;AAACO,EAAA,GAzBWxB,MAAM;AA2BnB,OAAO,MAAMyB,aAAa,GAAGA,CAAC;EAAElB,QAAQ;EAAE,GAAGK;AAAM,CAAC,KAAK;EACvD,oBAAOR,OAAA,CAAAE,SAAA;IAAAC,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACmB,GAAA,GAFWD,aAAa;AAI1B,OAAO,MAAME,UAAU,GAAGA,CAAC;EAAEpB,QAAQ;EAAEC,KAAK;EAAE,GAAGI;AAAM,CAAC,KAAK;EAC3D,oBACER,OAAA,CAACF,QAAQ;IAACM,KAAK,EAAEA,KAAM;IAAA,GAAKI,KAAK;IAAAL,QAAA,EAC9BA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEf,CAAC;AAACW,GAAA,GANWD,UAAU;AAQvB,OAAO,MAAME,aAAa,GAAGA,CAAC;EAAEtB,QAAQ;EAAE,GAAGK;AAAM,CAAC,KAAK;EACvD,oBAAOR,OAAA,CAAAE,SAAA;IAAAC,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACuB,GAAA,GAFWD,aAAa;AAI1B,OAAO,MAAME,WAAW,GAAGA,CAAC;EAAErB,WAAW;EAAE,GAAGE;AAAM,CAAC,KAAK;EACxD,OAAO,IAAI,CAAC,CAAC;AACf,CAAC;AAACoB,GAAA,GAFWD,WAAW;AAAA,IAAAP,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}