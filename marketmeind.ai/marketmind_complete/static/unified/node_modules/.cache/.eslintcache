[{"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/index.js": "1", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/App.js": "2", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/LandingPage.js": "3", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/RegisterPage.js": "4", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/LoginPage.js": "5", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/ForgotPasswordPage.js": "6", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/UnauthorizedPage.js": "7", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessDashboard.js": "8", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js": "9", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js": "10", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js": "11", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/contexts/AuthContext.js": "12", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/routing/ProtectedRoute.js": "13", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/theme/index.js": "14", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalHome.js": "15", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerHome.js": "16", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js": "17", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js": "18", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/DashboardLayout.js": "19", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/common/ComingSoon.js": "20", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/StatsCard.js": "21", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/index.js": "22", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js": "23", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js": "24", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/button.js": "25", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js": "26", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js": "27", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js": "28", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js": "29", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js": "30", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js": "31", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/UnifiedCard.js": "32", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/table.js": "33", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js": "34", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/PageContainer.js": "35", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/Profile.js": "36", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/ContentGeneration.js": "37", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/TrendPrediction.js": "38", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/SentimentAnalysis.js": "39", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignCreate.js": "40", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignActive.js": "41", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignAnalytics.js": "42", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CustomerManagement.js": "43", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/CompanyManagement.js": "44", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/UserManagement.js": "45", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/SystemOverview.js": "46"}, {"size": 232, "mtime": 1750700266000, "results": "47", "hashOfConfig": "48"}, {"size": 5451, "mtime": 1753686621199, "results": "49", "hashOfConfig": "48"}, {"size": 11891, "mtime": 1750703422000, "results": "50", "hashOfConfig": "48"}, {"size": 7358, "mtime": 1750701840000, "results": "51", "hashOfConfig": "48"}, {"size": 10391, "mtime": 1750701831000, "results": "52", "hashOfConfig": "48"}, {"size": 4175, "mtime": 1750701850000, "results": "53", "hashOfConfig": "48"}, {"size": 4445, "mtime": 1750701860000, "results": "54", "hashOfConfig": "48"}, {"size": 4065, "mtime": 1753687558926, "results": "55", "hashOfConfig": "48"}, {"size": 2108, "mtime": 1753688119278, "results": "56", "hashOfConfig": "48"}, {"size": 2592, "mtime": 1753687259759, "results": "57", "hashOfConfig": "48"}, {"size": 2292, "mtime": 1753687992196, "results": "58", "hashOfConfig": "48"}, {"size": 10433, "mtime": 1750702557000, "results": "59", "hashOfConfig": "48"}, {"size": 2427, "mtime": 1750701812000, "results": "60", "hashOfConfig": "48"}, {"size": 6215, "mtime": 1750702229000, "results": "61", "hashOfConfig": "48"}, {"size": 6638, "mtime": 1750703498000, "results": "62", "hashOfConfig": "48"}, {"size": 6671, "mtime": 1750703675000, "results": "63", "hashOfConfig": "48"}, {"size": 8951, "mtime": 1753686544767, "results": "64", "hashOfConfig": "48"}, {"size": 6532, "mtime": 1750703615000, "results": "65", "hashOfConfig": "48"}, {"size": 12147, "mtime": 1753686097114, "results": "66", "hashOfConfig": "48"}, {"size": 3718, "mtime": 1750703698000, "results": "67", "hashOfConfig": "48"}, {"size": 4809, "mtime": 1750701812000, "results": "68", "hashOfConfig": "48"}, {"size": 433, "mtime": 1753686433796, "results": "69", "hashOfConfig": "48"}, {"size": 1243, "mtime": 1750704318000, "results": "70", "hashOfConfig": "48"}, {"size": 660, "mtime": 1750704333000, "results": "71", "hashOfConfig": "48"}, {"size": 679, "mtime": 1750704327000, "results": "72", "hashOfConfig": "48"}, {"size": 485, "mtime": 1750704393000, "results": "73", "hashOfConfig": "48"}, {"size": 1845, "mtime": 1750704386000, "results": "74", "hashOfConfig": "48"}, {"size": 456, "mtime": 1750704349000, "results": "75", "hashOfConfig": "48"}, {"size": 602, "mtime": 1750704356000, "results": "76", "hashOfConfig": "48"}, {"size": 637, "mtime": 1750704399000, "results": "77", "hashOfConfig": "48"}, {"size": 1130, "mtime": 1750704376000, "results": "78", "hashOfConfig": "48"}, {"size": 6421, "mtime": 1753686206197, "results": "79", "hashOfConfig": "48"}, {"size": 1751, "mtime": 1750704343000, "results": "80", "hashOfConfig": "48"}, {"size": 1743, "mtime": 1750704367000, "results": "81", "hashOfConfig": "48"}, {"size": 6038, "mtime": 1753686420903, "results": "82", "hashOfConfig": "48"}, {"size": 13002, "mtime": 1753687216357, "results": "83", "hashOfConfig": "48"}, {"size": 15664, "mtime": 1750704475000, "results": "84", "hashOfConfig": "48"}, {"size": 13603, "mtime": 1753687163102, "results": "85", "hashOfConfig": "48"}, {"size": 9901, "mtime": 1753687063412, "results": "86", "hashOfConfig": "48"}, {"size": 15335, "mtime": 1753687331081, "results": "87", "hashOfConfig": "48"}, {"size": 14434, "mtime": 1753687392292, "results": "88", "hashOfConfig": "48"}, {"size": 14565, "mtime": 1753687454145, "results": "89", "hashOfConfig": "48"}, {"size": 16232, "mtime": 1753687519118, "results": "90", "hashOfConfig": "48"}, {"size": 20111, "mtime": 1753687944191, "results": "91", "hashOfConfig": "48"}, {"size": 18749, "mtime": 1753687646408, "results": "92", "hashOfConfig": "48"}, {"size": 16336, "mtime": 1753688077510, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "885h43", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/App.js", ["232", "233"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/LandingPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/RegisterPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/LoginPage.js", ["234"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/ForgotPasswordPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/UnauthorizedPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js", ["235"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/contexts/AuthContext.js", ["236"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/routing/ProtectedRoute.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/theme/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalHome.js", ["237"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerHome.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js", ["238", "239", "240", "241"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js", ["242"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/DashboardLayout.js", ["243"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/common/ComingSoon.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/StatsCard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/button.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js", ["244"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/UnifiedCard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/table.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/PageContainer.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/Profile.js", ["245"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/ContentGeneration.js", ["246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/TrendPrediction.js", ["276", "277", "278"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/SentimentAnalysis.js", ["279", "280"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignCreate.js", ["281", "282", "283", "284", "285", "286", "287", "288", "289", "290"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignActive.js", ["291", "292"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CampaignAnalytics.js", ["293", "294", "295", "296", "297", "298"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/CustomerManagement.js", ["299", "300", "301", "302"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/CompanyManagement.js", ["303", "304", "305", "306", "307", "308", "309"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/UserManagement.js", ["310", "311", "312", "313", "314"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/SystemOverview.js", ["315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329", "330", "331"], [], {"ruleId": "332", "severity": 1, "message": "333", "line": 3, "column": 25, "nodeType": "334", "messageId": "335", "endLine": 3, "endColumn": 36}, {"ruleId": "332", "severity": 1, "message": "336", "line": 43, "column": 9, "nodeType": "334", "messageId": "335", "endLine": 43, "endColumn": 23}, {"ruleId": "332", "severity": 1, "message": "337", "line": 45, "column": 9, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 13}, {"ruleId": "332", "severity": 1, "message": "338", "line": 5, "column": 24, "nodeType": "334", "messageId": "335", "endLine": 5, "endColumn": 33}, {"ruleId": "339", "severity": 1, "message": "340", "line": 313, "column": 6, "nodeType": "341", "endLine": 313, "endColumn": 19, "suggestions": "342"}, {"ruleId": "332", "severity": 1, "message": "343", "line": 18, "column": 11, "nodeType": "334", "messageId": "335", "endLine": 18, "endColumn": 19}, {"ruleId": "332", "severity": 1, "message": "344", "line": 17, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 17, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "345", "line": 29, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 29, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "346", "line": 30, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 30, "endColumn": 12}, {"ruleId": "332", "severity": 1, "message": "347", "line": 31, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 31, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "348", "line": 19, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 19, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "349", "line": 50, "column": 9, "nodeType": "334", "messageId": "335", "endLine": 50, "endColumn": 17}, {"ruleId": "332", "severity": 1, "message": "350", "line": 2, "column": 29, "nodeType": "334", "messageId": "335", "endLine": 2, "endColumn": 39}, {"ruleId": "332", "severity": 1, "message": "351", "line": 15, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 15, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "352", "line": 13, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 13, "endColumn": 11}, {"ruleId": "332", "severity": 1, "message": "353", "line": 15, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 15, "endColumn": 11}, {"ruleId": "332", "severity": 1, "message": "354", "line": 16, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 16, "endColumn": 8}, {"ruleId": "332", "severity": 1, "message": "355", "line": 17, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 17, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "356", "line": 19, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 19, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "357", "line": 36, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 36, "endColumn": 15}, {"ruleId": "332", "severity": 1, "message": "358", "line": 38, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 38, "endColumn": 15}, {"ruleId": "332", "severity": 1, "message": "359", "line": 38, "column": 17, "nodeType": "334", "messageId": "335", "endLine": 38, "endColumn": 33}, {"ruleId": "332", "severity": 1, "message": "360", "line": 40, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 40, "endColumn": 7}, {"ruleId": "332", "severity": 1, "message": "361", "line": 41, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 41, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "362", "line": 42, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 42, "endColumn": 11}, {"ruleId": "332", "severity": 1, "message": "363", "line": 43, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 43, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "364", "line": 54, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 54, "endColumn": 9}, {"ruleId": "332", "severity": 1, "message": "365", "line": 55, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 55, "endColumn": 16}, {"ruleId": "332", "severity": 1, "message": "366", "line": 56, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 56, "endColumn": 20}, {"ruleId": "332", "severity": 1, "message": "367", "line": 57, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 57, "endColumn": 15}, {"ruleId": "332", "severity": 1, "message": "368", "line": 58, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 58, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "369", "line": 59, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 59, "endColumn": 16}, {"ruleId": "332", "severity": 1, "message": "370", "line": 68, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 68, "endColumn": 18}, {"ruleId": "332", "severity": 1, "message": "371", "line": 70, "column": 7, "nodeType": "334", "messageId": "335", "endLine": 70, "endColumn": 15}, {"ruleId": "332", "severity": 1, "message": "372", "line": 73, "column": 11, "nodeType": "334", "messageId": "335", "endLine": 73, "endColumn": 22}, {"ruleId": "332", "severity": 1, "message": "373", "line": 76, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 76, "endColumn": 17}, {"ruleId": "332", "severity": 1, "message": "374", "line": 76, "column": 19, "nodeType": "334", "messageId": "335", "endLine": 76, "endColumn": 29}, {"ruleId": "332", "severity": 1, "message": "375", "line": 79, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 79, "endColumn": 25}, {"ruleId": "332", "severity": 1, "message": "376", "line": 80, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 80, "endColumn": 26}, {"ruleId": "332", "severity": 1, "message": "377", "line": 81, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 81, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "378", "line": 82, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 82, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "379", "line": 83, "column": 23, "nodeType": "334", "messageId": "335", "endLine": 83, "endColumn": 37}, {"ruleId": "339", "severity": 1, "message": "380", "line": 167, "column": 6, "nodeType": "341", "endLine": 167, "endColumn": 8, "suggestions": "381"}, {"ruleId": "332", "severity": 1, "message": "382", "line": 188, "column": 9, "nodeType": "334", "messageId": "335", "endLine": 188, "endColumn": 36}, {"ruleId": "332", "severity": 1, "message": "383", "line": 23, "column": 16, "nodeType": "334", "messageId": "335", "endLine": 23, "endColumn": 29}, {"ruleId": "332", "severity": 1, "message": "384", "line": 24, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 24, "endColumn": 27}, {"ruleId": "339", "severity": 1, "message": "385", "line": 109, "column": 6, "nodeType": "341", "endLine": 109, "endColumn": 40, "suggestions": "386"}, {"ruleId": "332", "severity": 1, "message": "387", "line": 39, "column": 17, "nodeType": "334", "messageId": "335", "endLine": 39, "endColumn": 25}, {"ruleId": "339", "severity": 1, "message": "388", "line": 77, "column": 6, "nodeType": "341", "endLine": 77, "endColumn": 8, "suggestions": "389"}, {"ruleId": "332", "severity": 1, "message": "390", "line": 12, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 12, "endColumn": 7}, {"ruleId": "332", "severity": 1, "message": "391", "line": 19, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 19, "endColumn": 9}, {"ruleId": "332", "severity": 1, "message": "392", "line": 20, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 20, "endColumn": 19}, {"ruleId": "332", "severity": 1, "message": "393", "line": 21, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 21, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "394", "line": 24, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 24, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "395", "line": 25, "column": 13, "nodeType": "334", "messageId": "335", "endLine": 25, "endColumn": 23}, {"ruleId": "332", "severity": 1, "message": "396", "line": 26, "column": 18, "nodeType": "334", "messageId": "335", "endLine": 26, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "397", "line": 27, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 27, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "398", "line": 28, "column": 14, "nodeType": "334", "messageId": "335", "endLine": 28, "endColumn": 25}, {"ruleId": "332", "severity": 1, "message": "345", "line": 34, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 34, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "351", "line": 9, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 9, "endColumn": 10}, {"ruleId": "339", "severity": 1, "message": "399", "line": 109, "column": 6, "nodeType": "341", "endLine": 109, "endColumn": 8, "suggestions": "400"}, {"ruleId": "332", "severity": 1, "message": "401", "line": 1, "column": 27, "nodeType": "334", "messageId": "335", "endLine": 1, "endColumn": 36}, {"ruleId": "332", "severity": 1, "message": "402", "line": 11, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 11, "endColumn": 7}, {"ruleId": "332", "severity": 1, "message": "403", "line": 12, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 12, "endColumn": 14}, {"ruleId": "332", "severity": 1, "message": "404", "line": 22, "column": 17, "nodeType": "334", "messageId": "335", "endLine": 22, "endColumn": 31}, {"ruleId": "332", "severity": 1, "message": "405", "line": 23, "column": 19, "nodeType": "334", "messageId": "335", "endLine": 23, "endColumn": 35}, {"ruleId": "332", "severity": 1, "message": "406", "line": 100, "column": 9, "nodeType": "334", "messageId": "335", "endLine": 100, "endColumn": 26}, {"ruleId": "332", "severity": 1, "message": "351", "line": 11, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 11, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "407", "line": 39, "column": 17, "nodeType": "334", "messageId": "335", "endLine": 39, "endColumn": 29}, {"ruleId": "332", "severity": 1, "message": "373", "line": 58, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 58, "endColumn": 17}, {"ruleId": "339", "severity": 1, "message": "408", "line": 136, "column": 6, "nodeType": "341", "endLine": 136, "endColumn": 8, "suggestions": "409"}, {"ruleId": "332", "severity": 1, "message": "351", "line": 11, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 11, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "358", "line": 18, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 18, "endColumn": 8}, {"ruleId": "332", "severity": 1, "message": "410", "line": 34, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 34, "endColumn": 17}, {"ruleId": "332", "severity": 1, "message": "411", "line": 44, "column": 13, "nodeType": "334", "messageId": "335", "endLine": 44, "endColumn": 23}, {"ruleId": "332", "severity": 1, "message": "394", "line": 45, "column": 15, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 27}, {"ruleId": "332", "severity": 1, "message": "373", "line": 62, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 62, "endColumn": 17}, {"ruleId": "339", "severity": 1, "message": "412", "line": 152, "column": 6, "nodeType": "341", "endLine": 152, "endColumn": 8, "suggestions": "413"}, {"ruleId": "332", "severity": 1, "message": "351", "line": 11, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 11, "endColumn": 10}, {"ruleId": "332", "severity": 1, "message": "391", "line": 30, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 30, "endColumn": 9}, {"ruleId": "332", "severity": 1, "message": "392", "line": 31, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 31, "endColumn": 19}, {"ruleId": "332", "severity": 1, "message": "373", "line": 61, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 61, "endColumn": 17}, {"ruleId": "339", "severity": 1, "message": "414", "line": 158, "column": 6, "nodeType": "341", "endLine": 158, "endColumn": 8, "suggestions": "415"}, {"ruleId": "332", "severity": 1, "message": "401", "line": 1, "column": 27, "nodeType": "334", "messageId": "335", "endLine": 1, "endColumn": 36}, {"ruleId": "332", "severity": 1, "message": "416", "line": 14, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 14, "endColumn": 8}, {"ruleId": "332", "severity": 1, "message": "417", "line": 15, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 15, "endColumn": 12}, {"ruleId": "332", "severity": 1, "message": "418", "line": 16, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 16, "endColumn": 12}, {"ruleId": "332", "severity": 1, "message": "419", "line": 17, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 17, "endColumn": 17}, {"ruleId": "332", "severity": 1, "message": "420", "line": 18, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 18, "endColumn": 12}, {"ruleId": "332", "severity": 1, "message": "421", "line": 19, "column": 3, "nodeType": "334", "messageId": "335", "endLine": 19, "endColumn": 11}, {"ruleId": "332", "severity": 1, "message": "404", "line": 22, "column": 17, "nodeType": "334", "messageId": "335", "endLine": 22, "endColumn": 31}, {"ruleId": "332", "severity": 1, "message": "405", "line": 23, "column": 19, "nodeType": "334", "messageId": "335", "endLine": 23, "endColumn": 35}, {"ruleId": "332", "severity": 1, "message": "422", "line": 28, "column": 14, "nodeType": "334", "messageId": "335", "endLine": 28, "endColumn": 25}, {"ruleId": "332", "severity": 1, "message": "423", "line": 29, "column": 12, "nodeType": "334", "messageId": "335", "endLine": 29, "endColumn": 21}, {"ruleId": "332", "severity": 1, "message": "424", "line": 45, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 19}, {"ruleId": "332", "severity": 1, "message": "425", "line": 45, "column": 21, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 25}, {"ruleId": "332", "severity": 1, "message": "426", "line": 45, "column": 122, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 130}, {"ruleId": "332", "severity": 1, "message": "427", "line": 45, "column": 132, "nodeType": "334", "messageId": "335", "endLine": 45, "endColumn": 135}, {"ruleId": "332", "severity": 1, "message": "373", "line": 48, "column": 10, "nodeType": "334", "messageId": "335", "endLine": 48, "endColumn": 17}, {"ruleId": "332", "severity": 1, "message": "428", "line": 49, "column": 24, "nodeType": "334", "messageId": "335", "endLine": 49, "endColumn": 39}, "no-unused-vars", "'createTheme' is defined but never used.", "Identifier", "unusedVar", "'getThemeByPath' is assigned a value but never used.", "'from' is assigned a value but never used.", "'OwnerIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refreshToken'. Either include it or remove the dependency array.", "ArrayExpression", ["429"], "'HelpIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'PageSection' is defined but never used.", "'StatsGrid' is defined but never used.", "'UnifiedGrid' is defined but never used.", "'SecurityIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'AlertTitle' is defined but never used.", "'Tooltip' is defined but never used.", "'Sparkles' is defined but never used.", "'Download' is defined but never used.", "'Brain' is defined but never used.", "'Loader2' is defined but never used.", "'AlertCircle' is defined but never used.", "'Label' is defined but never used.", "'Alert' is defined but never used.", "'AlertDescription' is defined but never used.", "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogDescription' is defined but never used.", "'DialogHeader' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogTrigger' is defined but never used.", "'Textarea' is defined but never used.", "'API_BASE' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'selectedContent' is assigned a value but never used.", "'showCreateDialog' is assigned a value but never used.", "'generationResults' is assigned a value but never used.", "'generationLoading' is assigned a value but never used.", "'setContentForm' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'sampleContents'. Either include it or remove the dependency array.", ["430"], "'generatePersonalizedContent' is assigned a value but never used.", "'DateRangeIcon' is defined but never used.", "'CategoryIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'generatePredictions' and 'sampleTrends'. Either include them or remove the dependency array.", ["431"], "'setStats' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'sampleHistory'. Either include it or remove the dependency array.", ["432"], "'Chip' is defined but never used.", "'Switch' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Divider' is defined but never used.", "'CampaignIcon' is defined but never used.", "'TargetIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'PreviewIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'sampleCampaigns'. Either include it or remove the dependency array.", ["433"], "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'TrendingDownIcon' is defined but never used.", "'costPerConversion' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'sampleCustomers'. Either include it or remove the dependency array.", ["434"], "'AddIcon' is defined but never used.", "'PeopleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'sampleCompanies'. Either include it or remove the dependency array.", ["435"], "React Hook useEffect has a missing dependency: 'sampleUsers'. Either include it or remove the dependency array.", ["436"], "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'StorageIcon' is defined but never used.", "'SpeedIcon' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'setSystemHealth' is assigned a value but never used.", {"desc": "437", "fix": "438"}, {"desc": "439", "fix": "440"}, {"desc": "441", "fix": "442"}, {"desc": "443", "fix": "444"}, {"desc": "445", "fix": "446"}, {"desc": "447", "fix": "448"}, {"desc": "449", "fix": "450"}, {"desc": "451", "fix": "452"}, "Update the dependencies array to be: [accessToken, refreshToken]", {"range": "453", "text": "454"}, "Update the dependencies array to be: [sampleContents]", {"range": "455", "text": "456"}, "Update the dependencies array to be: [generatePredictions, sampleTrends, selectedCategory, selectedPeriod]", {"range": "457", "text": "458"}, "Update the dependencies array to be: [sampleHistory]", {"range": "459", "text": "460"}, "Update the dependencies array to be: [sampleCampaigns]", {"range": "461", "text": "462"}, "Update the dependencies array to be: [sampleCustomers]", {"range": "463", "text": "464"}, "Update the dependencies array to be: [sampleCompanies]", {"range": "465", "text": "466"}, "Update the dependencies array to be: [sampleUsers]", {"range": "467", "text": "468"}, [9618, 9631], "[accessToken, refreshToken]", [4289, 4291], "[sampleContents]", [2539, 2573], "[generatePredictions, sampleTrends, selectedCategory, selectedPeriod]", [1814, 1816], "[sampleHistory]", [2350, 2352], "[sampleCampaigns]", [3153, 3155], "[sampleCustomers]", [3637, 3639], "[sampleCompanies]", [3698, 3700], "[sampleUsers]"]