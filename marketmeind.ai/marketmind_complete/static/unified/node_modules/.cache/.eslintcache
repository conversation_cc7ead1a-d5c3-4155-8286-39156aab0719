[{"/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/index.js": "1", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/App.js": "2", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/LandingPage.js": "3", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/RegisterPage.js": "4", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/LoginPage.js": "5", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/ForgotPasswordPage.js": "6", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/UnauthorizedPage.js": "7", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessDashboard.js": "8", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js": "9", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js": "10", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js": "11", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/contexts/AuthContext.js": "12", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/routing/ProtectedRoute.js": "13", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/theme/index.js": "14", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalHome.js": "15", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerHome.js": "16", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js": "17", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js": "18", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/DashboardLayout.js": "19", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/common/ComingSoon.js": "20", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/StatsCard.js": "21", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/index.js": "22", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js": "23", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js": "24", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/button.js": "25", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js": "26", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js": "27", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js": "28", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js": "29", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js": "30", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js": "31", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/UnifiedCard.js": "32", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/table.js": "33", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js": "34", "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/PageContainer.js": "35"}, {"size": 232, "mtime": 1750700266000, "results": "36", "hashOfConfig": "37"}, {"size": 5451, "mtime": 1753686621199, "results": "38", "hashOfConfig": "37"}, {"size": 11891, "mtime": 1750703422000, "results": "39", "hashOfConfig": "37"}, {"size": 7358, "mtime": 1750701840000, "results": "40", "hashOfConfig": "37"}, {"size": 10391, "mtime": 1750701831000, "results": "41", "hashOfConfig": "37"}, {"size": 4175, "mtime": 1750701850000, "results": "42", "hashOfConfig": "37"}, {"size": 4445, "mtime": 1750701860000, "results": "43", "hashOfConfig": "37"}, {"size": 4628, "mtime": 1750703757000, "results": "44", "hashOfConfig": "37"}, {"size": 2263, "mtime": 1750703810000, "results": "45", "hashOfConfig": "37"}, {"size": 3214, "mtime": 1750703724000, "results": "46", "hashOfConfig": "37"}, {"size": 2587, "mtime": 1750703783000, "results": "47", "hashOfConfig": "37"}, {"size": 10433, "mtime": 1750702557000, "results": "48", "hashOfConfig": "37"}, {"size": 2427, "mtime": 1750701812000, "results": "49", "hashOfConfig": "37"}, {"size": 6215, "mtime": 1750702229000, "results": "50", "hashOfConfig": "37"}, {"size": 6638, "mtime": 1750703498000, "results": "51", "hashOfConfig": "37"}, {"size": 6671, "mtime": 1750703675000, "results": "52", "hashOfConfig": "37"}, {"size": 8951, "mtime": 1753686544767, "results": "53", "hashOfConfig": "37"}, {"size": 6532, "mtime": 1750703615000, "results": "54", "hashOfConfig": "37"}, {"size": 12147, "mtime": 1753686097114, "results": "55", "hashOfConfig": "37"}, {"size": 3718, "mtime": 1750703698000, "results": "56", "hashOfConfig": "37"}, {"size": 4809, "mtime": 1750701812000, "results": "57", "hashOfConfig": "37"}, {"size": 433, "mtime": 1753686433796, "results": "58", "hashOfConfig": "37"}, {"size": 1243, "mtime": 1750704318000, "results": "59", "hashOfConfig": "37"}, {"size": 660, "mtime": 1750704333000, "results": "60", "hashOfConfig": "37"}, {"size": 679, "mtime": 1750704327000, "results": "61", "hashOfConfig": "37"}, {"size": 485, "mtime": 1750704393000, "results": "62", "hashOfConfig": "37"}, {"size": 1845, "mtime": 1750704386000, "results": "63", "hashOfConfig": "37"}, {"size": 456, "mtime": 1750704349000, "results": "64", "hashOfConfig": "37"}, {"size": 602, "mtime": 1750704356000, "results": "65", "hashOfConfig": "37"}, {"size": 637, "mtime": 1750704399000, "results": "66", "hashOfConfig": "37"}, {"size": 1130, "mtime": 1750704376000, "results": "67", "hashOfConfig": "37"}, {"size": 6421, "mtime": 1753686206197, "results": "68", "hashOfConfig": "37"}, {"size": 1751, "mtime": 1750704343000, "results": "69", "hashOfConfig": "37"}, {"size": 1743, "mtime": 1750704367000, "results": "70", "hashOfConfig": "37"}, {"size": 6038, "mtime": 1753686420903, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "885h43", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/App.js", ["177", "178"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/LandingPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/RegisterPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/LoginPage.js", ["179"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/ForgotPasswordPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/auth/UnauthorizedPage.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerDashboard.js", ["180"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminDashboard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/contexts/AuthContext.js", ["181"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/routing/ProtectedRoute.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/theme/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/personal/PersonalHome.js", ["182"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/owner/OwnerHome.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/business/BusinessHome.js", ["183", "184", "185", "186"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/pages/admin/AdminHome.js", ["187"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/DashboardLayout.js", ["188"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/common/ComingSoon.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/StatsCard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/index.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/card.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/input.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/button.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/label.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/tabs.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/badge.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/alert.js", ["189"], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/textarea.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/select.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/UnifiedCard.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/table.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/ui/dialog.js", [], [], "/Users/<USER>/Desktop/marketmeind.ai/marketmeind.ai/marketmind_complete/static/unified/src/shared/components/layout/PageContainer.js", [], [], {"ruleId": "190", "severity": 1, "message": "191", "line": 3, "column": 25, "nodeType": "192", "messageId": "193", "endLine": 3, "endColumn": 36}, {"ruleId": "190", "severity": 1, "message": "194", "line": 43, "column": 9, "nodeType": "192", "messageId": "193", "endLine": 43, "endColumn": 23}, {"ruleId": "190", "severity": 1, "message": "195", "line": 45, "column": 9, "nodeType": "192", "messageId": "193", "endLine": 45, "endColumn": 13}, {"ruleId": "190", "severity": 1, "message": "196", "line": 5, "column": 24, "nodeType": "192", "messageId": "193", "endLine": 5, "endColumn": 33}, {"ruleId": "197", "severity": 1, "message": "198", "line": 313, "column": 6, "nodeType": "199", "endLine": 313, "endColumn": 19, "suggestions": "200"}, {"ruleId": "190", "severity": 1, "message": "201", "line": 18, "column": 11, "nodeType": "192", "messageId": "193", "endLine": 18, "endColumn": 19}, {"ruleId": "190", "severity": 1, "message": "202", "line": 17, "column": 15, "nodeType": "192", "messageId": "193", "endLine": 17, "endColumn": 27}, {"ruleId": "190", "severity": 1, "message": "203", "line": 29, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 29, "endColumn": 14}, {"ruleId": "190", "severity": 1, "message": "204", "line": 30, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 30, "endColumn": 12}, {"ruleId": "190", "severity": 1, "message": "205", "line": 31, "column": 3, "nodeType": "192", "messageId": "193", "endLine": 31, "endColumn": 14}, {"ruleId": "190", "severity": 1, "message": "206", "line": 19, "column": 15, "nodeType": "192", "messageId": "193", "endLine": 19, "endColumn": 27}, {"ruleId": "190", "severity": 1, "message": "207", "line": 50, "column": 9, "nodeType": "192", "messageId": "193", "endLine": 50, "endColumn": 17}, {"ruleId": "190", "severity": 1, "message": "208", "line": 2, "column": 29, "nodeType": "192", "messageId": "193", "endLine": 2, "endColumn": 39}, "no-unused-vars", "'createTheme' is defined but never used.", "Identifier", "unusedVar", "'getThemeByPath' is assigned a value but never used.", "'from' is assigned a value but never used.", "'OwnerIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refreshToken'. Either include it or remove the dependency array.", "ArrayExpression", ["209"], "'HelpIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'PageSection' is defined but never used.", "'StatsGrid' is defined but never used.", "'UnifiedGrid' is defined but never used.", "'SecurityIcon' is defined but never used.", "'isMobile' is assigned a value but never used.", "'AlertTitle' is defined but never used.", {"desc": "210", "fix": "211"}, "Update the dependencies array to be: [accessToken, refreshToken]", {"range": "212", "text": "213"}, [9618, 9631], "[accessToken, refreshToken]"]