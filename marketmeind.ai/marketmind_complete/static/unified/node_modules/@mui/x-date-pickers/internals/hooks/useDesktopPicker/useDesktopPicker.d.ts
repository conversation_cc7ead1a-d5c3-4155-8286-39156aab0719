import * as React from 'react';
import { UseDesktopPickerParams, UseDesktopPickerProps } from './useDesktopPicker.types';
import { DateOrTimeViewWithMeridiem } from '../../models';
/**
 * Hook managing all the single-date desktop pickers:
 * - DesktopDatePicker
 * - DesktopDateTimePicker
 * - DesktopTimePicker
 */
export declare const useDesktopPicker: <TDate, TView extends DateOrTimeViewWithMeridiem, TExternalProps extends UseDesktopPickerProps<TDate, TView, any, TExternalProps>>({ props, getOpenDialogAriaText, ...pickerParams }: UseDesktopPickerParams<TDate, TView, TExternalProps>) => {
    renderPicker: () => React.JSX.Element;
};
