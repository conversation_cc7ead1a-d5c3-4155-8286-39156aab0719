import { UsePickerParams, UsePickerProps, UsePickerResponse } from './usePicker.types';
import { InferError } from '../useValidation';
import { FieldSection } from '../../../models';
import { DateOrTimeViewWithMeridiem } from '../../models';
export declare const usePicker: <TValue, TDate, TView extends DateOrTimeViewWithMeridiem, TSection extends FieldSection, TExternalProps extends UsePickerProps<TValue, TDate, TView, TSection, any, any, any>, TAdditionalProps extends {}>({ props, valueManager, valueType, wrapperVariant, inputRef, additionalViewProps, validator, autoFocusView, }: UsePickerParams<TValue, TDate, TView, TSection, TExternalProps, TAdditionalProps>) => UsePickerResponse<TValue, TView, TSection, InferError<TExternalProps>>;
