import * as React from 'react';
import { ExportedPickersLayoutSlotsComponent, ExportedPickersLayoutSlotsComponentsProps } from '../../../PickersLayout/PickersLayout.types';
import { BasePickerProps } from '../../models/props/basePickerProps';
import { UncapitalizeObjectKeys } from '../../utils/slots-migration';
import { UsePickerParams } from '../usePicker';
import { UsePickerViewsProps } from '../usePicker/usePickerViews';
import { FieldSection } from '../../../models';
import { DateOrTimeViewWithMeridiem } from '../../models';
export interface UseStaticPickerSlotsComponent<TDate, TView extends DateOrTimeViewWithMeridiem> extends ExportedPickersLayoutSlotsComponent<TDate | null, TDate, TView> {
}
export interface UseStaticPickerSlotsComponentsProps<TDate, TView extends DateOrTimeViewWithMeridiem> extends ExportedPickersLayoutSlotsComponentsProps<TDate | null, TDate, TView> {
}
export interface StaticOnlyPickerProps {
    /**
     * Force static wrapper inner components to be rendered in mobile or desktop mode.
     * @default "mobile"
     */
    displayStaticWrapperAs: 'desktop' | 'mobile';
    /**
     * If `true`, the view is focused during the first mount.
     */
    autoFocus?: boolean;
    /**
     * Callback fired when component requests to be closed.
     * Can be fired when selecting (by default on `desktop` mode) or clearing a value.
     * @deprecated Please avoid using as it will be removed in next major version.
     */
    onClose?: () => void;
}
export interface UseStaticPickerProps<TDate, TView extends DateOrTimeViewWithMeridiem, TError, TExternalProps extends UsePickerViewsProps<TDate | null, TDate, TView, any, any>> extends BasePickerProps<TDate | null, TDate, TView, TError, TExternalProps, {}>, StaticOnlyPickerProps {
    /**
     * Overridable components.
     * @default {}
     * @deprecated Please use `slots`.
     */
    components?: UseStaticPickerSlotsComponent<TDate, TView>;
    /**
     * The props used for each component slot.
     * @default {}
     * @deprecated Please use `slotProps`.
     */
    componentsProps?: UseStaticPickerSlotsComponentsProps<TDate, TView>;
    /**
     * Overridable component slots.
     * @default {}
     */
    slots?: UncapitalizeObjectKeys<UseStaticPickerSlotsComponent<TDate | null, TView>>;
    /**
     * The props used for each component slot.
     * @default {}
     */
    slotProps?: UseStaticPickerSlotsComponentsProps<TDate, TView>;
}
export interface UseStaticPickerParams<TDate, TView extends DateOrTimeViewWithMeridiem, TExternalProps extends UseStaticPickerProps<TDate, TView, any, TExternalProps>> extends Pick<UsePickerParams<TDate | null, TDate, TView, FieldSection, TExternalProps, {}>, 'valueManager' | 'valueType' | 'validator'> {
    props: TExternalProps;
    /**
     * Ref to pass to the root element
     */
    ref?: React.Ref<HTMLDivElement>;
}
