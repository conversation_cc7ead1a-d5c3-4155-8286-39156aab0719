import { UseFieldForwardedProps, UseFieldInternalProps, UseFieldParams, UseFieldState, FieldSelectedSectionsIndexes } from './useField.types';
import { FieldSection, FieldSelectedSections } from '../../../models';
export interface UpdateSectionValueParams<TSection extends FieldSection> {
    /**
     * The section on which we want to apply the new value.
     */
    activeSection: TSection;
    /**
     * Value to apply to the active section.
     */
    newSectionValue: string;
    /**
     * If `true`, the focus will move to the next section.
     */
    shouldGoToNextSection: boolean;
}
export declare const useFieldState: <TValue, TDate, TSection extends FieldSection, TForwardedProps extends UseFieldForwardedProps, TInternalProps extends UseFieldInternalProps<any, any, any, any>>(params: UseFieldParams<TValue, TDate, TSection, TForwardedProps, TInternalProps>) => {
    state: UseFieldState<TValue, TSection>;
    selectedSectionIndexes: FieldSelectedSectionsIndexes | null;
    setSelectedSections: (newSelectedSections: FieldSelectedSections) => void;
    clearValue: () => void;
    clearActiveSection: () => void;
    updateSectionValue: ({ activeSection, newSectionValue, shouldGoToNextSection, }: UpdateSectionValueParams<TSection>) => void;
    updateValueFromValueStr: (valueStr: string) => void;
    setTempAndroidValueStr: (tempValueStrAndroid: string | null) => void;
    sectionsValueBoundaries: import("./useField.types").FieldSectionsValueBoundaries<TDate>;
    placeholder: string;
    timezone: string;
};
