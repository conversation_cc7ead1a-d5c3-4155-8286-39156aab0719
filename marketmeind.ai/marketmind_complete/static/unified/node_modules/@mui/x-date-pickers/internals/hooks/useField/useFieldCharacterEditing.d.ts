import { FieldSection, PickersTimezone } from '../../../models';
import { FieldSectionsValueBoundaries } from './useField.types';
import { UpdateSectionValueParams } from './useFieldState';
interface ApplyCharacterEditingParams {
    keyPressed: string;
    sectionIndex: number;
}
interface UseFieldEditingParams<TDate, TSection extends FieldSection> {
    sections: TSection[];
    updateSectionValue: (params: UpdateSectionValueParams<TSection>) => void;
    sectionsValueBoundaries: FieldSectionsValueBoundaries<TDate>;
    setTempAndroidValueStr: (newValue: string | null) => void;
    timezone: PickersTimezone;
}
/**
 * Update the active section value when the user pressed a key that is not a navigation key (arrow key for example).
 * This hook has two main editing behaviors
 *
 * 1. The numeric editing when the user presses a digit
 * 2. The letter editing when the user presses another key
 */
export declare const useFieldCharacterEditing: <TDate, TSection extends FieldSection>({ sections, updateSectionValue, sectionsValueBoundaries, setTempAndroidValueStr, timezone, }: UseFieldEditingParams<TDate, TSection>) => {
    applyCharacterEditing: (params: ApplyCharacterEditingParams) => void;
    resetCharacterQuery: () => void;
};
export {};
