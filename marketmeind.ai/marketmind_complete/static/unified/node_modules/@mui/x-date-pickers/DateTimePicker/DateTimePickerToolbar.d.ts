import * as React from 'react';
import { BaseToolbarProps, ExportedBaseToolbarProps } from '../internals/models/props/toolbar';
import { DateTimePickerToolbarClasses } from './dateTimePickerToolbarClasses';
import { DateOrTimeViewWithMeridiem, WrapperVariant } from '../internals/models';
export interface ExportedDateTimePickerToolbarProps extends ExportedBaseToolbarProps {
    ampm?: boolean;
    ampmInClock?: boolean;
    /**
     * Override or extend the styles applied to the component.
     */
    classes?: Partial<DateTimePickerToolbarClasses>;
}
export interface DateTimePickerToolbarProps<TDate> extends ExportedDateTimePickerToolbarProps, BaseToolbarProps<TDate | null, DateOrTimeViewWithMeridiem> {
    toolbarVariant?: WrapperVariant;
}
/**
 * Demos:
 *
 * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)
 * - [Custom components](https://mui.com/x/react-date-pickers/custom-components/)
 *
 * API:
 *
 * - [DateTimePickerToolbar API](https://mui.com/x/api/date-pickers/date-time-picker-toolbar/)
 */
declare function DateTimePickerToolbar<TDate extends unknown>(inProps: DateTimePickerToolbarProps<TDate>): React.JSX.Element;
declare namespace DateTimePickerToolbar {
    var propTypes: any;
}
export { DateTimePickerToolbar };
