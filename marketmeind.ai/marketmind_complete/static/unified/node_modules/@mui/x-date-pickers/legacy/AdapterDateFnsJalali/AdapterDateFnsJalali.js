import _extends from "@babel/runtime/helpers/esm/extends";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
/* eslint-disable class-methods-use-this */
import addSeconds from 'date-fns-jalali/addSeconds';
import addMinutes from 'date-fns-jalali/addMinutes';
import addHours from 'date-fns-jalali/addHours';
import addDays from 'date-fns-jalali/addDays';
import addWeeks from 'date-fns-jalali/addWeeks';
import addMonths from 'date-fns-jalali/addMonths';
import addYears from 'date-fns-jalali/addYears';
import differenceInYears from 'date-fns-jalali/differenceInYears';
import differenceInQuarters from 'date-fns-jalali/differenceInQuarters';
import differenceInMonths from 'date-fns-jalali/differenceInMonths';
import differenceInWeeks from 'date-fns-jalali/differenceInWeeks';
import differenceInDays from 'date-fns-jalali/differenceInDays';
import differenceInHours from 'date-fns-jalali/differenceInHours';
import differenceInMinutes from 'date-fns-jalali/differenceInMinutes';
import differenceInSeconds from 'date-fns-jalali/differenceInSeconds';
import differenceInMilliseconds from 'date-fns-jalali/differenceInMilliseconds';
import eachDayOfInterval from 'date-fns-jalali/eachDayOfInterval';
import endOfDay from 'date-fns-jalali/endOfDay';
import endOfWeek from 'date-fns-jalali/endOfWeek';
import endOfYear from 'date-fns-jalali/endOfYear';
import dateFnsFormat from 'date-fns-jalali/format';
import formatISO from 'date-fns-jalali/formatISO';
import getHours from 'date-fns-jalali/getHours';
import getSeconds from 'date-fns-jalali/getSeconds';
import getMilliseconds from 'date-fns-jalali/getMilliseconds';
import getWeek from 'date-fns-jalali/getWeek';
import getYear from 'date-fns-jalali/getYear';
import getMonth from 'date-fns-jalali/getMonth';
import getDate from 'date-fns-jalali/getDate';
import getDaysInMonth from 'date-fns-jalali/getDaysInMonth';
import getMinutes from 'date-fns-jalali/getMinutes';
import isAfter from 'date-fns-jalali/isAfter';
import isBefore from 'date-fns-jalali/isBefore';
import isEqual from 'date-fns-jalali/isEqual';
import isSameDay from 'date-fns-jalali/isSameDay';
import isSameYear from 'date-fns-jalali/isSameYear';
import isSameMonth from 'date-fns-jalali/isSameMonth';
import isSameHour from 'date-fns-jalali/isSameHour';
import isValid from 'date-fns-jalali/isValid';
import dateFnsParse from 'date-fns-jalali/parse';
import parseISO from 'date-fns-jalali/parseISO';
import setDate from 'date-fns-jalali/setDate';
import setHours from 'date-fns-jalali/setHours';
import setMinutes from 'date-fns-jalali/setMinutes';
import setMonth from 'date-fns-jalali/setMonth';
import setSeconds from 'date-fns-jalali/setSeconds';
import setMilliseconds from 'date-fns-jalali/setMilliseconds';
import setYear from 'date-fns-jalali/setYear';
import startOfDay from 'date-fns-jalali/startOfDay';
import startOfMonth from 'date-fns-jalali/startOfMonth';
import endOfMonth from 'date-fns-jalali/endOfMonth';
import startOfWeek from 'date-fns-jalali/startOfWeek';
import startOfYear from 'date-fns-jalali/startOfYear';
import isWithinInterval from 'date-fns-jalali/isWithinInterval';
import defaultLocale from 'date-fns-jalali/locale/fa-IR';
// @ts-ignore
import longFormatters from 'date-fns-jalali/_lib/format/longFormatters';
var formatTokenMap = {
  // Year
  y: {
    sectionType: 'year',
    contentType: 'digit',
    maxLength: 4
  },
  yy: 'year',
  yyy: {
    sectionType: 'year',
    contentType: 'digit',
    maxLength: 4
  },
  yyyy: 'year',
  // Month
  M: {
    sectionType: 'month',
    contentType: 'digit',
    maxLength: 2
  },
  MM: 'month',
  MMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  MMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  L: {
    sectionType: 'month',
    contentType: 'digit',
    maxLength: 2
  },
  LL: 'month',
  LLL: {
    sectionType: 'month',
    contentType: 'letter'
  },
  LLLL: {
    sectionType: 'month',
    contentType: 'letter'
  },
  // Day of the month
  d: {
    sectionType: 'day',
    contentType: 'digit',
    maxLength: 2
  },
  dd: 'day',
  do: {
    sectionType: 'day',
    contentType: 'digit-with-letter'
  },
  // Day of the week
  E: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  EE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  EEE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  EEEE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  EEEEE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  i: {
    sectionType: 'weekDay',
    contentType: 'digit',
    maxLength: 1
  },
  ii: 'weekDay',
  iii: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  iiii: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  e: {
    sectionType: 'weekDay',
    contentType: 'digit',
    maxLength: 1
  },
  ee: 'weekDay',
  eee: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  eeee: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  eeeee: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  eeeeee: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  c: {
    sectionType: 'weekDay',
    contentType: 'digit',
    maxLength: 1
  },
  cc: 'weekDay',
  ccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  cccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  ccccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  cccccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  // Meridiem
  a: 'meridiem',
  aa: 'meridiem',
  aaa: 'meridiem',
  // Hours
  H: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  HH: 'hours',
  h: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  hh: 'hours',
  // Minutes
  m: {
    sectionType: 'minutes',
    contentType: 'digit',
    maxLength: 2
  },
  mm: 'minutes',
  // Seconds
  s: {
    sectionType: 'seconds',
    contentType: 'digit',
    maxLength: 2
  },
  ss: 'seconds'
};
var defaultFormats = {
  year: 'yyyy',
  month: 'LLLL',
  monthShort: 'MMM',
  dayOfMonth: 'd',
  weekday: 'EEEE',
  weekdayShort: 'EEEEEE',
  hours24h: 'HH',
  hours12h: 'hh',
  meridiem: 'aa',
  minutes: 'mm',
  seconds: 'ss',
  fullDate: 'PPP',
  fullDateWithWeekday: 'PPPP',
  keyboardDate: 'P',
  shortDate: 'd MMM',
  normalDate: 'd MMMM',
  normalDateWithWeekday: 'EEE, d MMMM',
  monthAndYear: 'LLLL yyyy',
  monthAndDate: 'd MMMM',
  fullDateTime: 'PPP p',
  fullDateTime12h: 'PPP hh:mm aa',
  fullDateTime24h: 'PPP HH:mm',
  fullTime: 'p',
  fullTime12h: 'hh:mm aaa',
  fullTime24h: 'HH:mm',
  keyboardDateTime: 'P p',
  keyboardDateTime12h: 'P hh:mm aa',
  keyboardDateTime24h: 'P HH:mm'
};
var NUMBER_SYMBOL_MAP = {
  '1': '۱',
  '2': '۲',
  '3': '۳',
  '4': '۴',
  '5': '۵',
  '6': '۶',
  '7': '۷',
  '8': '۸',
  '9': '۹',
  '0': '۰'
};

/**
 * Based on `@date-io/date-fns-jalali`
 *
 * MIT License
 *
 * Copyright (c) 2017 Dmitriy Kovalenko
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
export var AdapterDateFnsJalali = /*#__PURE__*/_createClass(function AdapterDateFnsJalali() {
  var _this = this;
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _locale = _ref.locale,
    formats = _ref.formats;
  _classCallCheck(this, AdapterDateFnsJalali);
  this.isMUIAdapter = true;
  this.isTimezoneCompatible = false;
  this.lib = 'date-fns-jalali';
  this.locale = void 0;
  this.formats = void 0;
  this.formatTokenMap = formatTokenMap;
  this.escapedCharacters = {
    start: "'",
    end: "'"
  };
  this.date = function (value) {
    if (typeof value === 'undefined') {
      return new Date();
    }
    if (value === null) {
      return null;
    }
    return new Date(value);
  };
  this.dateWithTimezone = function (value) {
    return _this.date(value);
  };
  this.getTimezone = function () {
    return 'default';
  };
  this.setTimezone = function (value) {
    return value;
  };
  this.toJsDate = function (value) {
    return value;
  };
  this.parseISO = function (isoString) {
    return parseISO(isoString);
  };
  this.toISO = function (value) {
    return formatISO(value, {
      format: 'extended'
    });
  };
  this.parse = function (value, format) {
    if (value === '') {
      return null;
    }
    return dateFnsParse(value, format, new Date(), {
      locale: _this.locale
    });
  };
  this.getCurrentLocaleCode = function () {
    var _this$locale;
    return ((_this$locale = _this.locale) == null ? void 0 : _this$locale.code) || 'fa-IR';
  };
  // Note: date-fns input types are more lenient than this adapter, so we need to expose our more
  // strict signature and delegate to the more lenient signature. Otherwise, we have downstream type errors upon usage.
  this.is12HourCycleInCurrentLocale = function () {
    if (_this.locale) {
      return /a/.test(_this.locale.formatLong.time());
    }

    // By default, date-fns-jalali is using fa-IR locale with am/pm enabled
    return true;
  };
  this.expandFormat = function (format) {
    var _this$locale2;
    // @see https://github.com/date-fns/date-fns/blob/master/src/format/index.js#L31
    var longFormatRegexp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
    var locale = (_this$locale2 = _this.locale) != null ? _this$locale2 : defaultLocale;
    return format.match(longFormatRegexp).map(function (token) {
      var firstCharacter = token[0];
      if (firstCharacter === 'p' || firstCharacter === 'P') {
        var longFormatter = longFormatters[firstCharacter];
        return longFormatter(token, locale.formatLong, {});
      }
      return token;
    }).join('');
  };
  this.getFormatHelperText = function (format) {
    return _this.expandFormat(format).replace(/(aaa|aa|a)/g, '(a|p)m').toLocaleLowerCase();
  };
  this.isNull = function (value) {
    return value === null;
  };
  this.isValid = function (value) {
    return isValid(_this.date(value));
  };
  this.format = function (value, formatKey) {
    return _this.formatByString(value, _this.formats[formatKey]);
  };
  this.formatByString = function (value, formatString) {
    return dateFnsFormat(value, formatString, {
      locale: _this.locale
    });
  };
  this.formatNumber = function (numberToFormat) {
    return numberToFormat.replace(/\d/g, function (match) {
      return NUMBER_SYMBOL_MAP[match];
    }).replace(/,/g, '،');
  };
  this.getDiff = function (value, comparing, unit) {
    switch (unit) {
      case 'years':
        return differenceInYears(value, _this.date(comparing));
      case 'quarters':
        return differenceInQuarters(value, _this.date(comparing));
      case 'months':
        return differenceInMonths(value, _this.date(comparing));
      case 'weeks':
        return differenceInWeeks(value, _this.date(comparing));
      case 'days':
        return differenceInDays(value, _this.date(comparing));
      case 'hours':
        return differenceInHours(value, _this.date(comparing));
      case 'minutes':
        return differenceInMinutes(value, _this.date(comparing));
      case 'seconds':
        return differenceInSeconds(value, _this.date(comparing));
      default:
        {
          return differenceInMilliseconds(value, _this.date(comparing));
        }
    }
  };
  this.isEqual = function (value, comparing) {
    if (value === null && comparing === null) {
      return true;
    }
    return isEqual(value, comparing);
  };
  this.isSameYear = function (value, comparing) {
    return isSameYear(value, comparing);
  };
  this.isSameMonth = function (value, comparing) {
    return isSameMonth(value, comparing);
  };
  this.isSameDay = function (value, comparing) {
    return isSameDay(value, comparing);
  };
  this.isSameHour = function (value, comparing) {
    return isSameHour(value, comparing);
  };
  this.isAfter = function (value, comparing) {
    return isAfter(value, comparing);
  };
  this.isAfterYear = function (value, comparing) {
    return isAfter(value, endOfYear(comparing));
  };
  this.isAfterDay = function (value, comparing) {
    return isAfter(value, endOfDay(comparing));
  };
  this.isBefore = function (value, comparing) {
    return isBefore(value, comparing);
  };
  this.isBeforeYear = function (value, comparing) {
    return isBefore(value, startOfYear(comparing));
  };
  this.isBeforeDay = function (value, comparing) {
    return isBefore(value, startOfDay(comparing));
  };
  this.isWithinRange = function (value, _ref2) {
    var _ref3 = _slicedToArray(_ref2, 2),
      start = _ref3[0],
      end = _ref3[1];
    return isWithinInterval(value, {
      start: start,
      end: end
    });
  };
  this.startOfYear = function (value) {
    return startOfYear(value);
  };
  this.startOfMonth = function (value) {
    return startOfMonth(value);
  };
  this.startOfWeek = function (value) {
    return startOfWeek(value, {
      locale: _this.locale
    });
  };
  this.startOfDay = function (value) {
    return startOfDay(value);
  };
  this.endOfYear = function (value) {
    return endOfYear(value);
  };
  this.endOfMonth = function (value) {
    return endOfMonth(value);
  };
  this.endOfWeek = function (value) {
    return endOfWeek(value, {
      locale: _this.locale
    });
  };
  this.endOfDay = function (value) {
    return endOfDay(value);
  };
  this.addYears = function (value, amount) {
    return addYears(value, amount);
  };
  this.addMonths = function (value, amount) {
    return addMonths(value, amount);
  };
  this.addWeeks = function (value, amount) {
    return addWeeks(value, amount);
  };
  this.addDays = function (value, amount) {
    return addDays(value, amount);
  };
  this.addHours = function (value, amount) {
    return addHours(value, amount);
  };
  this.addMinutes = function (value, amount) {
    return addMinutes(value, amount);
  };
  this.addSeconds = function (value, amount) {
    return addSeconds(value, amount);
  };
  this.getYear = function (value) {
    return getYear(value);
  };
  this.getMonth = function (value) {
    return getMonth(value);
  };
  this.getDate = function (value) {
    return getDate(value);
  };
  this.getHours = function (value) {
    return getHours(value);
  };
  this.getMinutes = function (value) {
    return getMinutes(value);
  };
  this.getSeconds = function (value) {
    return getSeconds(value);
  };
  this.getMilliseconds = function (value) {
    return getMilliseconds(value);
  };
  this.setYear = function (value, year) {
    return setYear(value, year);
  };
  this.setMonth = function (value, month) {
    return setMonth(value, month);
  };
  this.setDate = function (value, date) {
    return setDate(value, date);
  };
  this.setHours = function (value, hours) {
    return setHours(value, hours);
  };
  this.setMinutes = function (value, minutes) {
    return setMinutes(value, minutes);
  };
  this.setSeconds = function (value, seconds) {
    return setSeconds(value, seconds);
  };
  this.setMilliseconds = function (value, milliseconds) {
    return setMilliseconds(value, milliseconds);
  };
  this.getDaysInMonth = function (value) {
    return getDaysInMonth(value);
  };
  this.getNextMonth = function (value) {
    return addMonths(value, 1);
  };
  this.getPreviousMonth = function (value) {
    return addMonths(value, -1);
  };
  this.getMonthArray = function (value) {
    var firstMonth = startOfYear(value);
    var monthArray = [firstMonth];
    while (monthArray.length < 12) {
      var prevMonth = monthArray[monthArray.length - 1];
      monthArray.push(_this.getNextMonth(prevMonth));
    }
    return monthArray;
  };
  this.mergeDateAndTime = function (dateParam, timeParam) {
    return _this.setSeconds(_this.setMinutes(_this.setHours(dateParam, _this.getHours(timeParam)), _this.getMinutes(timeParam)), _this.getSeconds(timeParam));
  };
  this.getWeekdays = function () {
    var now = new Date();
    return eachDayOfInterval({
      start: startOfWeek(now, {
        locale: _this.locale
      }),
      end: endOfWeek(now, {
        locale: _this.locale
      })
    }).map(function (day) {
      return _this.formatByString(day, 'EEEEEE');
    });
  };
  this.getWeekArray = function (value) {
    var start = startOfWeek(startOfMonth(value), {
      locale: _this.locale
    });
    var end = endOfWeek(endOfMonth(value), {
      locale: _this.locale
    });
    var count = 0;
    var current = start;
    var nestedWeeks = [];
    while (isBefore(current, end)) {
      var weekNumber = Math.floor(count / 7);
      nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
      nestedWeeks[weekNumber].push(current);
      current = addDays(current, 1);
      count += 1;
    }
    return nestedWeeks;
  };
  this.getWeekNumber = function (date) {
    return getWeek(date, {
      locale: _this.locale
    });
  };
  this.getYearRange = function (start, end) {
    var startDate = startOfYear(start);
    var endDate = endOfYear(end);
    var years = [];
    var current = startDate;
    while (isBefore(current, endDate)) {
      years.push(current);
      current = addYears(current, 1);
    }
    return years;
  };
  this.getMeridiemText = function (ampm) {
    return ampm === 'am' ? 'ق.ظ' : 'ب.ظ';
  };
  this.locale = _locale;
  this.formats = _extends({}, defaultFormats, formats);
});