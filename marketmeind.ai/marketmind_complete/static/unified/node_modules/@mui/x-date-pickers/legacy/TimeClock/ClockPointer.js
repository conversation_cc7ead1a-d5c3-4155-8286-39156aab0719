import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
var _excluded = ["className", "hasSelected", "isInner", "type", "viewValue"];
import * as React from 'react';
import clsx from 'clsx';
import { styled, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { CLOCK_WIDTH, CLOCK_HOUR_WIDTH } from './shared';
import { getClockPointerUtilityClass } from './clockPointerClasses';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    thumb: ['thumb']
  };
  return composeClasses(slots, getClockPointerUtilityClass, classes);
};
var ClockPointerRoot = styled('div', {
  name: 'Mui<PERSON>lockPointer',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.root;
  }
})(function (_ref) {
  var theme = _ref.theme,
    ownerState = _ref.ownerState;
  return _extends({
    width: 2,
    backgroundColor: (theme.vars || theme).palette.primary.main,
    position: 'absolute',
    left: 'calc(50% - 1px)',
    bottom: '50%',
    transformOrigin: 'center bottom 0px'
  }, ownerState.shouldAnimate && {
    transition: theme.transitions.create(['transform', 'height'])
  });
});
var ClockPointerThumb = styled('div', {
  name: 'MuiClockPointer',
  slot: 'Thumb',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.thumb;
  }
})(function (_ref2) {
  var theme = _ref2.theme,
    ownerState = _ref2.ownerState;
  return _extends({
    width: 4,
    height: 4,
    backgroundColor: (theme.vars || theme).palette.primary.contrastText,
    borderRadius: '50%',
    position: 'absolute',
    top: -21,
    left: "calc(50% - ".concat(CLOCK_HOUR_WIDTH / 2, "px)"),
    border: "".concat((CLOCK_HOUR_WIDTH - 4) / 2, "px solid ").concat((theme.vars || theme).palette.primary.main),
    boxSizing: 'content-box'
  }, ownerState.hasSelected && {
    backgroundColor: (theme.vars || theme).palette.primary.main
  });
});

/**
 * @ignore - internal component.
 */
export function ClockPointer(inProps) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiClockPointer'
  });
  var className = props.className,
    hasSelected = props.hasSelected,
    isInner = props.isInner,
    type = props.type,
    viewValue = props.viewValue,
    other = _objectWithoutProperties(props, _excluded);
  var previousType = React.useRef(type);
  React.useEffect(function () {
    previousType.current = type;
  }, [type]);
  var ownerState = _extends({}, props, {
    shouldAnimate: previousType.current !== type
  });
  var classes = useUtilityClasses(ownerState);
  var getAngleStyle = function getAngleStyle() {
    var max = type === 'hours' ? 12 : 60;
    var angle = 360 / max * viewValue;
    if (type === 'hours' && viewValue > 12) {
      angle -= 360; // round up angle to max 360 degrees
    }
    return {
      height: Math.round((isInner ? 0.26 : 0.4) * CLOCK_WIDTH),
      transform: "rotateZ(".concat(angle, "deg)")
    };
  };
  return /*#__PURE__*/_jsx(ClockPointerRoot, _extends({
    style: getAngleStyle(),
    className: clsx(className, classes.root),
    ownerState: ownerState
  }, other, {
    children: /*#__PURE__*/_jsx(ClockPointerThumb, {
      ownerState: ownerState,
      className: classes.thumb
    })
  }));
}