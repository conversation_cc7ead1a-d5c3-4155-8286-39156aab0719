import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
var _excluded = ["autoFocus", "onViewChange", "value", "defaultValue", "referenceDate", "disableFuture", "disablePast", "defaultCalendarMonth", "onChange", "onYearChange", "onMonthChange", "reduceAnimations", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "view", "views", "openTo", "className", "disabled", "readOnly", "minDate", "maxDate", "disableHighlightToday", "focusedView", "onFocusedViewChange", "showDaysOutsideCurrentMonth", "fixedWeekNumber", "dayOfWeekFormatter", "components", "componentsProps", "slots", "slotProps", "loading", "renderLoading", "displayWeekNumber", "yearsPerRow", "monthsPerRow", "timezone"];
import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { useSlotProps } from '@mui/base/utils';
import { styled, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses, unstable_useId as useId, unstable_useEventCallback as useEventCallback } from '@mui/utils';
import { useCalendarState } from './useCalendarState';
import { useDefaultDates, useUtils } from '../internals/hooks/useUtils';
import { PickersFadeTransitionGroup } from './PickersFadeTransitionGroup';
import { DayCalendar } from './DayCalendar';
import { MonthCalendar } from '../MonthCalendar';
import { YearCalendar } from '../YearCalendar';
import { useViews } from '../internals/hooks/useViews';
import { PickersCalendarHeader } from '../PickersCalendarHeader';
import { findClosestEnabledDate, applyDefaultDate, mergeDateAndTime } from '../internals/utils/date-utils';
import { PickerViewRoot } from '../internals/components/PickerViewRoot';
import { useDefaultReduceAnimations } from '../internals/hooks/useDefaultReduceAnimations';
import { getDateCalendarUtilityClass } from './dateCalendarClasses';
import { useControlledValueWithTimezone } from '../internals/hooks/useValueWithTimezone';
import { singleItemValueManager } from '../internals/utils/valueManagers';
import { VIEW_HEIGHT } from '../internals/constants/dimensions';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    viewTransitionContainer: ['viewTransitionContainer']
  };
  return composeClasses(slots, getDateCalendarUtilityClass, classes);
};
function useDateCalendarDefaultizedProps(props, name) {
  var _themeProps$loading, _themeProps$disablePa, _themeProps$disableFu, _themeProps$openTo, _themeProps$views, _themeProps$reduceAni, _themeProps$renderLoa;
  var utils = useUtils();
  var defaultDates = useDefaultDates();
  var defaultReduceAnimations = useDefaultReduceAnimations();
  var themeProps = useThemeProps({
    props: props,
    name: name
  });
  return _extends({}, themeProps, {
    loading: (_themeProps$loading = themeProps.loading) != null ? _themeProps$loading : false,
    disablePast: (_themeProps$disablePa = themeProps.disablePast) != null ? _themeProps$disablePa : false,
    disableFuture: (_themeProps$disableFu = themeProps.disableFuture) != null ? _themeProps$disableFu : false,
    openTo: (_themeProps$openTo = themeProps.openTo) != null ? _themeProps$openTo : 'day',
    views: (_themeProps$views = themeProps.views) != null ? _themeProps$views : ['year', 'day'],
    reduceAnimations: (_themeProps$reduceAni = themeProps.reduceAnimations) != null ? _themeProps$reduceAni : defaultReduceAnimations,
    renderLoading: (_themeProps$renderLoa = themeProps.renderLoading) != null ? _themeProps$renderLoa : function () {
      return /*#__PURE__*/_jsx("span", {
        children: "..."
      });
    },
    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),
    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate)
  });
}
var DateCalendarRoot = styled(PickerViewRoot, {
  name: 'MuiDateCalendar',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.root;
  }
})({
  display: 'flex',
  flexDirection: 'column',
  height: VIEW_HEIGHT
});
var DateCalendarViewTransitionContainer = styled(PickersFadeTransitionGroup, {
  name: 'MuiDateCalendar',
  slot: 'ViewTransitionContainer',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.viewTransitionContainer;
  }
})({});
/**
 * Demos:
 *
 * - [DatePicker](https://mui.com/x/react-date-pickers/date-picker/)
 * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)
 * - [Validation](https://mui.com/x/react-date-pickers/validation/)
 *
 * API:
 *
 * - [DateCalendar API](https://mui.com/x/api/date-pickers/date-calendar/)
 */
export var DateCalendar = /*#__PURE__*/React.forwardRef(function DateCalendar(inProps, ref) {
  var _ref, _slots$calendarHeader, _slotProps$calendarHe;
  var utils = useUtils();
  var id = useId();
  var props = useDateCalendarDefaultizedProps(inProps, 'MuiDateCalendar');
  var autoFocus = props.autoFocus,
    onViewChange = props.onViewChange,
    valueProp = props.value,
    defaultValue = props.defaultValue,
    referenceDateProp = props.referenceDate,
    disableFuture = props.disableFuture,
    disablePast = props.disablePast,
    defaultCalendarMonth = props.defaultCalendarMonth,
    onChange = props.onChange,
    onYearChange = props.onYearChange,
    onMonthChange = props.onMonthChange,
    reduceAnimations = props.reduceAnimations,
    shouldDisableDate = props.shouldDisableDate,
    shouldDisableMonth = props.shouldDisableMonth,
    shouldDisableYear = props.shouldDisableYear,
    inView = props.view,
    views = props.views,
    openTo = props.openTo,
    className = props.className,
    disabled = props.disabled,
    readOnly = props.readOnly,
    minDate = props.minDate,
    maxDate = props.maxDate,
    disableHighlightToday = props.disableHighlightToday,
    inFocusedView = props.focusedView,
    onFocusedViewChange = props.onFocusedViewChange,
    showDaysOutsideCurrentMonth = props.showDaysOutsideCurrentMonth,
    fixedWeekNumber = props.fixedWeekNumber,
    dayOfWeekFormatter = props.dayOfWeekFormatter,
    components = props.components,
    componentsProps = props.componentsProps,
    slots = props.slots,
    slotProps = props.slotProps,
    loading = props.loading,
    renderLoading = props.renderLoading,
    displayWeekNumber = props.displayWeekNumber,
    yearsPerRow = props.yearsPerRow,
    monthsPerRow = props.monthsPerRow,
    timezoneProp = props.timezone,
    other = _objectWithoutProperties(props, _excluded);
  var _useControlledValueWi = useControlledValueWithTimezone({
      name: 'DateCalendar',
      timezone: timezoneProp,
      value: valueProp,
      defaultValue: defaultValue,
      onChange: onChange,
      valueManager: singleItemValueManager
    }),
    value = _useControlledValueWi.value,
    handleValueChange = _useControlledValueWi.handleValueChange,
    timezone = _useControlledValueWi.timezone;
  var _useViews = useViews({
      view: inView,
      views: views,
      openTo: openTo,
      onChange: handleValueChange,
      onViewChange: onViewChange,
      autoFocus: autoFocus,
      focusedView: inFocusedView,
      onFocusedViewChange: onFocusedViewChange
    }),
    view = _useViews.view,
    setView = _useViews.setView,
    focusedView = _useViews.focusedView,
    setFocusedView = _useViews.setFocusedView,
    goToNextView = _useViews.goToNextView,
    setValueAndGoToNextView = _useViews.setValueAndGoToNextView;
  var _useCalendarState = useCalendarState({
      value: value,
      defaultCalendarMonth: defaultCalendarMonth,
      referenceDate: referenceDateProp,
      reduceAnimations: reduceAnimations,
      onMonthChange: onMonthChange,
      minDate: minDate,
      maxDate: maxDate,
      shouldDisableDate: shouldDisableDate,
      disablePast: disablePast,
      disableFuture: disableFuture,
      timezone: timezone
    }),
    referenceDate = _useCalendarState.referenceDate,
    calendarState = _useCalendarState.calendarState,
    changeFocusedDay = _useCalendarState.changeFocusedDay,
    changeMonth = _useCalendarState.changeMonth,
    handleChangeMonth = _useCalendarState.handleChangeMonth,
    isDateDisabled = _useCalendarState.isDateDisabled,
    onMonthSwitchingAnimationEnd = _useCalendarState.onMonthSwitchingAnimationEnd;

  // When disabled, limit the view to the selected date
  var minDateWithDisabled = disabled && value || minDate;
  var maxDateWithDisabled = disabled && value || maxDate;
  var gridLabelId = "".concat(id, "-grid-label");
  var hasFocus = focusedView !== null;
  var CalendarHeader = (_ref = (_slots$calendarHeader = slots == null ? void 0 : slots.calendarHeader) != null ? _slots$calendarHeader : components == null ? void 0 : components.CalendarHeader) != null ? _ref : PickersCalendarHeader;
  var calendarHeaderProps = useSlotProps({
    elementType: CalendarHeader,
    externalSlotProps: (_slotProps$calendarHe = slotProps == null ? void 0 : slotProps.calendarHeader) != null ? _slotProps$calendarHe : componentsProps == null ? void 0 : componentsProps.calendarHeader,
    additionalProps: {
      views: views,
      view: view,
      currentMonth: calendarState.currentMonth,
      onViewChange: setView,
      onMonthChange: function onMonthChange(newMonth, direction) {
        return handleChangeMonth({
          newMonth: newMonth,
          direction: direction
        });
      },
      minDate: minDateWithDisabled,
      maxDate: maxDateWithDisabled,
      disabled: disabled,
      disablePast: disablePast,
      disableFuture: disableFuture,
      reduceAnimations: reduceAnimations,
      timezone: timezone,
      labelId: gridLabelId,
      slots: slots,
      slotProps: slotProps
    },
    ownerState: props
  });
  var handleDateMonthChange = useEventCallback(function (newDate) {
    var startOfMonth = utils.startOfMonth(newDate);
    var endOfMonth = utils.endOfMonth(newDate);
    var closestEnabledDate = isDateDisabled(newDate) ? findClosestEnabledDate({
      utils: utils,
      date: newDate,
      minDate: utils.isBefore(minDate, startOfMonth) ? startOfMonth : minDate,
      maxDate: utils.isAfter(maxDate, endOfMonth) ? endOfMonth : maxDate,
      disablePast: disablePast,
      disableFuture: disableFuture,
      isDateDisabled: isDateDisabled,
      timezone: timezone
    }) : newDate;
    if (closestEnabledDate) {
      setValueAndGoToNextView(closestEnabledDate, 'finish');
      onMonthChange == null || onMonthChange(startOfMonth);
    } else {
      goToNextView();
      changeMonth(startOfMonth);
    }
    changeFocusedDay(closestEnabledDate, true);
  });
  var handleDateYearChange = useEventCallback(function (newDate) {
    var startOfYear = utils.startOfYear(newDate);
    var endOfYear = utils.endOfYear(newDate);
    var closestEnabledDate = isDateDisabled(newDate) ? findClosestEnabledDate({
      utils: utils,
      date: newDate,
      minDate: utils.isBefore(minDate, startOfYear) ? startOfYear : minDate,
      maxDate: utils.isAfter(maxDate, endOfYear) ? endOfYear : maxDate,
      disablePast: disablePast,
      disableFuture: disableFuture,
      isDateDisabled: isDateDisabled,
      timezone: timezone
    }) : newDate;
    if (closestEnabledDate) {
      setValueAndGoToNextView(closestEnabledDate, 'finish');
      onYearChange == null || onYearChange(closestEnabledDate);
    } else {
      goToNextView();
      changeMonth(startOfYear);
    }
    changeFocusedDay(closestEnabledDate, true);
  });
  var handleSelectedDayChange = useEventCallback(function (day) {
    if (day) {
      // If there is a date already selected, then we want to keep its time
      return handleValueChange(mergeDateAndTime(utils, day, value != null ? value : referenceDate), 'finish', view);
    }
    return handleValueChange(day, 'finish', view);
  });
  React.useEffect(function () {
    if (value != null && utils.isValid(value)) {
      changeMonth(value);
    }
  }, [value]); // eslint-disable-line

  var ownerState = props;
  var classes = useUtilityClasses(ownerState);
  var baseDateValidationProps = {
    disablePast: disablePast,
    disableFuture: disableFuture,
    maxDate: maxDate,
    minDate: minDate
  };
  var commonViewProps = {
    disableHighlightToday: disableHighlightToday,
    readOnly: readOnly,
    disabled: disabled,
    timezone: timezone,
    gridLabelId: gridLabelId
  };
  var prevOpenViewRef = React.useRef(view);
  React.useEffect(function () {
    // If the view change and the focus was on the previous view
    // Then we update the focus.
    if (prevOpenViewRef.current === view) {
      return;
    }
    if (focusedView === prevOpenViewRef.current) {
      setFocusedView(view, true);
    }
    prevOpenViewRef.current = view;
  }, [focusedView, setFocusedView, view]);
  var selectedDays = React.useMemo(function () {
    return [value];
  }, [value]);
  return /*#__PURE__*/_jsxs(DateCalendarRoot, _extends({
    ref: ref,
    className: clsx(classes.root, className),
    ownerState: ownerState
  }, other, {
    children: [/*#__PURE__*/_jsx(CalendarHeader, _extends({}, calendarHeaderProps)), /*#__PURE__*/_jsx(DateCalendarViewTransitionContainer, {
      reduceAnimations: reduceAnimations,
      className: classes.viewTransitionContainer,
      transKey: view,
      ownerState: ownerState,
      children: /*#__PURE__*/_jsxs("div", {
        children: [view === 'year' && /*#__PURE__*/_jsx(YearCalendar, _extends({}, baseDateValidationProps, commonViewProps, {
          value: value,
          onChange: handleDateYearChange,
          shouldDisableYear: shouldDisableYear,
          hasFocus: hasFocus,
          onFocusedViewChange: function onFocusedViewChange(isViewFocused) {
            return setFocusedView('year', isViewFocused);
          },
          yearsPerRow: yearsPerRow,
          referenceDate: referenceDate
        })), view === 'month' && /*#__PURE__*/_jsx(MonthCalendar, _extends({}, baseDateValidationProps, commonViewProps, {
          hasFocus: hasFocus,
          className: className,
          value: value,
          onChange: handleDateMonthChange,
          shouldDisableMonth: shouldDisableMonth,
          onFocusedViewChange: function onFocusedViewChange(isViewFocused) {
            return setFocusedView('month', isViewFocused);
          },
          monthsPerRow: monthsPerRow,
          referenceDate: referenceDate
        })), view === 'day' && /*#__PURE__*/_jsx(DayCalendar, _extends({}, calendarState, baseDateValidationProps, commonViewProps, {
          onMonthSwitchingAnimationEnd: onMonthSwitchingAnimationEnd,
          onFocusedDayChange: changeFocusedDay,
          reduceAnimations: reduceAnimations,
          selectedDays: selectedDays,
          onSelectedDaysChange: handleSelectedDayChange,
          shouldDisableDate: shouldDisableDate,
          shouldDisableMonth: shouldDisableMonth,
          shouldDisableYear: shouldDisableYear,
          hasFocus: hasFocus,
          onFocusedViewChange: function onFocusedViewChange(isViewFocused) {
            return setFocusedView('day', isViewFocused);
          },
          showDaysOutsideCurrentMonth: showDaysOutsideCurrentMonth,
          fixedWeekNumber: fixedWeekNumber,
          dayOfWeekFormatter: dayOfWeekFormatter,
          displayWeekNumber: displayWeekNumber,
          components: components,
          componentsProps: componentsProps,
          slots: slots,
          slotProps: slotProps,
          loading: loading,
          renderLoading: renderLoading
        }))]
      })
    })]
  }));
});
process.env.NODE_ENV !== "production" ? DateCalendar.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * If `true`, the main element is focused during the first mount.
   * This main element is:
   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).
   * - the `input` element if there is a field rendered.
   */
  autoFocus: PropTypes.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  className: PropTypes.string,
  /**
   * Overridable components.
   * @default {}
   * @deprecated Please use `slots`.
   */
  components: PropTypes.object,
  /**
   * The props used for each component slot.
   * @default {}
   * @deprecated Please use `slotProps`.
   */
  componentsProps: PropTypes.object,
  /**
   * Formats the day of week displayed in the calendar header.
   * @param {string} day The day of week provided by the adapter.  Deprecated, will be removed in v7: Use `date` instead.
   * @param {TDate} date The date of the day of week provided by the adapter.
   * @returns {string} The name to display.
   * @default (_day: string, date: TDate) => adapter.format(date, 'weekdayShort').charAt(0).toUpperCase()
   */
  dayOfWeekFormatter: PropTypes.func,
  /**
   * Default calendar month displayed when `value` and `defaultValue` are empty.
   * @deprecated Consider using `referenceDate` instead.
   */
  defaultCalendarMonth: PropTypes.any,
  /**
   * The default selected value.
   * Used when the component is not controlled.
   */
  defaultValue: PropTypes.any,
  /**
   * If `true`, the picker and text field are disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  /**
   * If `true`, disable values after the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disableFuture: PropTypes.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: PropTypes.bool,
  /**
   * If `true`, disable values before the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disablePast: PropTypes.bool,
  /**
   * If `true`, the week number will be display in the calendar.
   */
  displayWeekNumber: PropTypes.bool,
  /**
   * Calendar will show more weeks in order to match this value.
   * Put it to 6 for having fix number of week in Gregorian calendars
   * @default undefined
   */
  fixedWeekNumber: PropTypes.number,
  /**
   * Controlled focused view.
   */
  focusedView: PropTypes.oneOf(['day', 'month', 'year']),
  /**
   * If `true`, calls `renderLoading` instead of rendering the day calendar.
   * Can be used to preload information and show it in calendar.
   * @default false
   */
  loading: PropTypes.bool,
  /**
   * Maximal selectable date.
   */
  maxDate: PropTypes.any,
  /**
   * Minimal selectable date.
   */
  minDate: PropTypes.any,
  /**
   * Months rendered per row.
   * @default 3
   */
  monthsPerRow: PropTypes.oneOf([3, 4]),
  /**
   * Callback fired when the value changes.
   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.
   * @template TView The view type. Will be one of date or time views.
   * @param {TValue} value The new value.
   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.
   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.
   */
  onChange: PropTypes.func,
  /**
   * Callback fired on focused view change.
   * @template TView
   * @param {TView} view The new view to focus or not.
   * @param {boolean} hasFocus `true` if the view should be focused.
   */
  onFocusedViewChange: PropTypes.func,
  /**
   * Callback fired on month change.
   * @template TDate
   * @param {TDate} month The new month.
   */
  onMonthChange: PropTypes.func,
  /**
   * Callback fired on view change.
   * @template TView
   * @param {TView} view The new view.
   */
  onViewChange: PropTypes.func,
  /**
   * Callback fired on year change.
   * @template TDate
   * @param {TDate} year The new year.
   */
  onYearChange: PropTypes.func,
  /**
   * The default visible view.
   * Used when the component view is not controlled.
   * Must be a valid option from `views` list.
   */
  openTo: PropTypes.oneOf(['day', 'month', 'year']),
  /**
   * Make picker read only.
   * @default false
   */
  readOnly: PropTypes.bool,
  /**
   * If `true`, disable heavy animations.
   * @default `@media(prefers-reduced-motion: reduce)` || `navigator.userAgent` matches Android <10 or iOS <13
   */
  reduceAnimations: PropTypes.bool,
  /**
   * The date used to generate the new value when both `value` and `defaultValue` are empty.
   * @default The closest valid date using the validation props, except callbacks such as `shouldDisableDate`.
   */
  referenceDate: PropTypes.any,
  /**
   * Component displaying when passed `loading` true.
   * @returns {React.ReactNode} The node to render when loading.
   * @default () => <span data-mui-test="loading-progress">...</span>
   */
  renderLoading: PropTypes.func,
  /**
   * Disable specific date.
   *
   * Warning: This function can be called multiple times (e.g. when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.
   *
   * @template TDate
   * @param {TDate} day The date to test.
   * @returns {boolean} If `true` the date will be disabled.
   */
  shouldDisableDate: PropTypes.func,
  /**
   * Disable specific month.
   * @template TDate
   * @param {TDate} month The month to test.
   * @returns {boolean} If `true`, the month will be disabled.
   */
  shouldDisableMonth: PropTypes.func,
  /**
   * Disable specific year.
   * @template TDate
   * @param {TDate} year The year to test.
   * @returns {boolean} If `true`, the year will be disabled.
   */
  shouldDisableYear: PropTypes.func,
  /**
   * If `true`, days outside the current month are rendered:
   *
   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.
   *
   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.
   *
   * - ignored if `calendars` equals more than `1` on range pickers.
   * @default false
   */
  showDaysOutsideCurrentMonth: PropTypes.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: PropTypes.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * Choose which timezone to use for the value.
   * Example: "default", "system", "UTC", "America/New_York".
   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.
   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documention} for more details.
   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.
   */
  timezone: PropTypes.string,
  /**
   * The selected value.
   * Used when the component is controlled.
   */
  value: PropTypes.any,
  /**
   * The visible view.
   * Used when the component view is controlled.
   * Must be a valid option from `views` list.
   */
  view: PropTypes.oneOf(['day', 'month', 'year']),
  /**
   * Available views.
   */
  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired),
  /**
   * Years rendered per row.
   * @default 3
   */
  yearsPerRow: PropTypes.oneOf([3, 4])
} : void 0;