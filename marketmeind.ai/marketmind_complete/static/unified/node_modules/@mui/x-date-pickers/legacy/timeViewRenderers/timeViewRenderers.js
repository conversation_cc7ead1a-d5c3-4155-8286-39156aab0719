import * as React from 'react';
import { TimeClock } from '../TimeClock';
import { DigitalClock } from '../DigitalClock';
import { MultiSectionDigitalClock } from '../MultiSectionDigitalClock';
import { isTimeView } from '../internals/utils/time-utils';
import { jsx as _jsx } from "react/jsx-runtime";
export var renderTimeViewClock = function renderTimeViewClock(_ref) {
  var view = _ref.view,
    onViewChange = _ref.onViewChange,
    focusedView = _ref.focusedView,
    onFocusedViewChange = _ref.onFocusedViewChange,
    views = _ref.views,
    value = _ref.value,
    defaultValue = _ref.defaultValue,
    referenceDate = _ref.referenceDate,
    onChange = _ref.onChange,
    className = _ref.className,
    classes = _ref.classes,
    disableFuture = _ref.disableFuture,
    disablePast = _ref.disablePast,
    minTime = _ref.minTime,
    maxTime = _ref.maxTime,
    shouldDisableTime = _ref.shouldDisableTime,
    shouldDisableClock = _ref.shouldDisableClock,
    minutesStep = _ref.minutesStep,
    ampm = _ref.ampm,
    ampmInClock = _ref.ampmInClock,
    components = _ref.components,
    componentsProps = _ref.componentsProps,
    slots = _ref.slots,
    slotProps = _ref.slotProps,
    readOnly = _ref.readOnly,
    disabled = _ref.disabled,
    sx = _ref.sx,
    autoFocus = _ref.autoFocus,
    showViewSwitcher = _ref.showViewSwitcher,
    disableIgnoringDatePartForTimeValidation = _ref.disableIgnoringDatePartForTimeValidation,
    timezone = _ref.timezone;
  return /*#__PURE__*/_jsx(TimeClock, {
    view: view,
    onViewChange: onViewChange,
    focusedView: focusedView && isTimeView(focusedView) ? focusedView : null,
    onFocusedViewChange: onFocusedViewChange,
    views: views.filter(isTimeView),
    value: value,
    defaultValue: defaultValue,
    referenceDate: referenceDate,
    onChange: onChange,
    className: className,
    classes: classes,
    disableFuture: disableFuture,
    disablePast: disablePast,
    minTime: minTime,
    maxTime: maxTime,
    shouldDisableTime: shouldDisableTime,
    shouldDisableClock: shouldDisableClock,
    minutesStep: minutesStep,
    ampm: ampm,
    ampmInClock: ampmInClock,
    components: components,
    componentsProps: componentsProps,
    slots: slots,
    slotProps: slotProps,
    readOnly: readOnly,
    disabled: disabled,
    sx: sx,
    autoFocus: autoFocus,
    showViewSwitcher: showViewSwitcher,
    disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
    timezone: timezone
  });
};
export var renderDigitalClockTimeView = function renderDigitalClockTimeView(_ref2) {
  var view = _ref2.view,
    onViewChange = _ref2.onViewChange,
    focusedView = _ref2.focusedView,
    onFocusedViewChange = _ref2.onFocusedViewChange,
    views = _ref2.views,
    value = _ref2.value,
    defaultValue = _ref2.defaultValue,
    referenceDate = _ref2.referenceDate,
    onChange = _ref2.onChange,
    className = _ref2.className,
    classes = _ref2.classes,
    disableFuture = _ref2.disableFuture,
    disablePast = _ref2.disablePast,
    minTime = _ref2.minTime,
    maxTime = _ref2.maxTime,
    shouldDisableTime = _ref2.shouldDisableTime,
    shouldDisableClock = _ref2.shouldDisableClock,
    minutesStep = _ref2.minutesStep,
    ampm = _ref2.ampm,
    components = _ref2.components,
    componentsProps = _ref2.componentsProps,
    slots = _ref2.slots,
    slotProps = _ref2.slotProps,
    readOnly = _ref2.readOnly,
    disabled = _ref2.disabled,
    sx = _ref2.sx,
    autoFocus = _ref2.autoFocus,
    disableIgnoringDatePartForTimeValidation = _ref2.disableIgnoringDatePartForTimeValidation,
    timeSteps = _ref2.timeSteps,
    skipDisabled = _ref2.skipDisabled,
    timezone = _ref2.timezone;
  return /*#__PURE__*/_jsx(DigitalClock, {
    view: view,
    onViewChange: onViewChange,
    focusedView: focusedView,
    onFocusedViewChange: onFocusedViewChange,
    views: views.filter(isTimeView),
    value: value,
    defaultValue: defaultValue,
    referenceDate: referenceDate,
    onChange: onChange,
    className: className,
    classes: classes,
    disableFuture: disableFuture,
    disablePast: disablePast,
    minTime: minTime,
    maxTime: maxTime,
    shouldDisableTime: shouldDisableTime,
    shouldDisableClock: shouldDisableClock,
    minutesStep: minutesStep,
    ampm: ampm,
    components: components,
    componentsProps: componentsProps,
    slots: slots,
    slotProps: slotProps,
    readOnly: readOnly,
    disabled: disabled,
    sx: sx,
    autoFocus: autoFocus,
    disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
    timeStep: timeSteps == null ? void 0 : timeSteps.minutes,
    skipDisabled: skipDisabled,
    timezone: timezone
  });
};
export var renderMultiSectionDigitalClockTimeView = function renderMultiSectionDigitalClockTimeView(_ref3) {
  var view = _ref3.view,
    onViewChange = _ref3.onViewChange,
    focusedView = _ref3.focusedView,
    onFocusedViewChange = _ref3.onFocusedViewChange,
    views = _ref3.views,
    value = _ref3.value,
    defaultValue = _ref3.defaultValue,
    referenceDate = _ref3.referenceDate,
    onChange = _ref3.onChange,
    className = _ref3.className,
    classes = _ref3.classes,
    disableFuture = _ref3.disableFuture,
    disablePast = _ref3.disablePast,
    minTime = _ref3.minTime,
    maxTime = _ref3.maxTime,
    shouldDisableTime = _ref3.shouldDisableTime,
    shouldDisableClock = _ref3.shouldDisableClock,
    minutesStep = _ref3.minutesStep,
    ampm = _ref3.ampm,
    components = _ref3.components,
    componentsProps = _ref3.componentsProps,
    slots = _ref3.slots,
    slotProps = _ref3.slotProps,
    readOnly = _ref3.readOnly,
    disabled = _ref3.disabled,
    sx = _ref3.sx,
    autoFocus = _ref3.autoFocus,
    disableIgnoringDatePartForTimeValidation = _ref3.disableIgnoringDatePartForTimeValidation,
    timeSteps = _ref3.timeSteps,
    skipDisabled = _ref3.skipDisabled,
    timezone = _ref3.timezone;
  return /*#__PURE__*/_jsx(MultiSectionDigitalClock, {
    view: view,
    onViewChange: onViewChange,
    focusedView: focusedView,
    onFocusedViewChange: onFocusedViewChange,
    views: views.filter(isTimeView),
    value: value,
    defaultValue: defaultValue,
    referenceDate: referenceDate,
    onChange: onChange,
    className: className,
    classes: classes,
    disableFuture: disableFuture,
    disablePast: disablePast,
    minTime: minTime,
    maxTime: maxTime,
    shouldDisableTime: shouldDisableTime,
    shouldDisableClock: shouldDisableClock,
    minutesStep: minutesStep,
    ampm: ampm,
    components: components,
    componentsProps: componentsProps,
    slots: slots,
    slotProps: slotProps,
    readOnly: readOnly,
    disabled: disabled,
    sx: sx,
    autoFocus: autoFocus,
    disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
    timeSteps: timeSteps,
    skipDisabled: skipDisabled,
    timezone: timezone
  });
};