import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _extends from "@babel/runtime/helpers/esm/extends";
var _excluded = ["ampm", "ampmInClock", "value", "onChange", "view", "isLandscape", "onViewChange", "toolbarFormat", "toolbarPlaceholder", "views", "disabled", "readOnly", "toolbarVariant"];
import * as React from 'react';
import PropTypes from 'prop-types';
import { styled, useThemeProps, useTheme } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { PickersToolbarText } from '../internals/components/PickersToolbarText';
import { PickersToolbar } from '../internals/components/PickersToolbar';
import { PickersToolbarButton } from '../internals/components/PickersToolbarButton';
import { useLocaleText, useUtils } from '../internals/hooks/useUtils';
import { dateTimePickerToolbarClasses, getDateTimePickerToolbarUtilityClass } from './dateTimePickerToolbarClasses';
import { useMeridiemMode } from '../internals/hooks/date-helpers-hooks';
import { MULTI_SECTION_CLOCK_SECTION_WIDTH } from '../internals/constants/dimensions';
import { formatMeridiem } from '../internals/utils/date-utils';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes,
    theme = ownerState.theme,
    isLandscape = ownerState.isLandscape;
  var slots = {
    root: ['root'],
    dateContainer: ['dateContainer'],
    timeContainer: ['timeContainer', theme.direction === 'rtl' && 'timeLabelReverse'],
    timeDigitsContainer: ['timeDigitsContainer', theme.direction === 'rtl' && 'timeLabelReverse'],
    separator: ['separator'],
    ampmSelection: ['ampmSelection', isLandscape && 'ampmLandscape'],
    ampmLabel: ['ampmLabel']
  };
  return composeClasses(slots, getDateTimePickerToolbarUtilityClass, classes);
};
var DateTimePickerToolbarRoot = styled(PickersToolbar, {
  name: 'MuiDateTimePickerToolbar',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.root;
  }
})(function (_ref) {
  var theme = _ref.theme,
    ownerState = _ref.ownerState;
  return {
    paddingLeft: ownerState.toolbarVariant === 'desktop' && !ownerState.isLandscape ? 24 : 16,
    paddingRight: ownerState.toolbarVariant === 'desktop' && !ownerState.isLandscape ? 0 : 16,
    borderBottom: ownerState.toolbarVariant === 'desktop' ? "1px solid ".concat((theme.vars || theme).palette.divider) : undefined,
    borderRight: ownerState.toolbarVariant === 'desktop' && ownerState.isLandscape ? "1px solid ".concat((theme.vars || theme).palette.divider) : undefined,
    justifyContent: 'space-around',
    position: 'relative'
  };
});
DateTimePickerToolbarRoot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  as: PropTypes.elementType,
  classes: PropTypes.object,
  className: PropTypes.string,
  isLandscape: PropTypes.bool.isRequired,
  isMobileKeyboardViewOpen: PropTypes.bool,
  landscapeDirection: PropTypes.oneOf(['column', 'row']),
  ownerState: PropTypes.object.isRequired,
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  toggleMobileKeyboardView: PropTypes.func,
  toolbarTitle: PropTypes.node,
  viewType: PropTypes.oneOf(['date', 'time'])
};
var DateTimePickerToolbarDateContainer = styled('div', {
  name: 'MuiDateTimePickerToolbar',
  slot: 'DateContainer',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.dateContainer;
  }
})({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start'
});
var DateTimePickerToolbarTimeContainer = styled('div', {
  name: 'MuiDateTimePickerToolbar',
  slot: 'TimeContainer',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.timeContainer;
  }
})(function (_ref2) {
  var theme = _ref2.theme,
    ownerState = _ref2.ownerState;
  var direction = ownerState.isLandscape && ownerState.toolbarVariant !== 'desktop' ? 'column' : 'row';
  return _extends({
    display: 'flex',
    flexDirection: direction
  }, ownerState.toolbarVariant === 'desktop' && _extends({}, !ownerState.isLandscape && {
    gap: 9,
    marginRight: 4,
    alignSelf: 'flex-end'
  }), theme.direction === 'rtl' && {
    flexDirection: "".concat(direction, "-reverse")
  });
});
var DateTimePickerToolbarTimeDigitsContainer = styled('div', {
  name: 'MuiDateTimePickerToolbar',
  slot: 'TimeDigitsContainer',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.timeDigitsContainer;
  }
})(function (_ref3) {
  var theme = _ref3.theme,
    ownerState = _ref3.ownerState;
  return _extends({
    display: 'flex'
  }, ownerState.toolbarVariant === 'desktop' && {
    gap: 1.5
  }, theme.direction === 'rtl' && {
    flexDirection: 'row-reverse'
  });
});
DateTimePickerToolbarTimeContainer.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  as: PropTypes.elementType,
  ownerState: PropTypes.object.isRequired,
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])
};
var DateTimePickerToolbarSeparator = styled(PickersToolbarText, {
  name: 'MuiDateTimePickerToolbar',
  slot: 'Separator',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.separator;
  }
})(function (_ref4) {
  var ownerState = _ref4.ownerState;
  return {
    margin: ownerState.toolbarVariant === 'desktop' ? 0 : '0 4px 0 2px',
    cursor: 'default'
  };
});

// Taken from TimePickerToolbar
var DateTimePickerToolbarAmPmSelection = styled('div', {
  name: 'MuiDateTimePickerToolbar',
  slot: 'AmPmSelection',
  overridesResolver: function overridesResolver(props, styles) {
    return [_defineProperty({}, ".".concat(dateTimePickerToolbarClasses.ampmLabel), styles.ampmLabel), _defineProperty({}, "&.".concat(dateTimePickerToolbarClasses.ampmLandscape), styles.ampmLandscape), styles.ampmSelection];
  }
})(function (_ref7) {
  var ownerState = _ref7.ownerState;
  return _extends({
    display: 'flex',
    flexDirection: 'column',
    marginRight: 'auto',
    marginLeft: 12
  }, ownerState.isLandscape && {
    margin: '4px 0 auto',
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%'
  }, _defineProperty({}, "& .".concat(dateTimePickerToolbarClasses.ampmLabel), {
    fontSize: 17
  }));
});

/**
 * Demos:
 *
 * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)
 * - [Custom components](https://mui.com/x/react-date-pickers/custom-components/)
 *
 * API:
 *
 * - [DateTimePickerToolbar API](https://mui.com/x/api/date-pickers/date-time-picker-toolbar/)
 */
function DateTimePickerToolbar(inProps) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiDateTimePickerToolbar'
  });
  var ampm = props.ampm,
    ampmInClock = props.ampmInClock,
    value = props.value,
    onChange = props.onChange,
    view = props.view,
    isLandscape = props.isLandscape,
    onViewChange = props.onViewChange,
    toolbarFormat = props.toolbarFormat,
    _props$toolbarPlaceho = props.toolbarPlaceholder,
    toolbarPlaceholder = _props$toolbarPlaceho === void 0 ? '––' : _props$toolbarPlaceho,
    views = props.views,
    disabled = props.disabled,
    readOnly = props.readOnly,
    _props$toolbarVariant = props.toolbarVariant,
    toolbarVariant = _props$toolbarVariant === void 0 ? 'mobile' : _props$toolbarVariant,
    other = _objectWithoutProperties(props, _excluded);
  var ownerState = props;
  var utils = useUtils();
  var _useMeridiemMode = useMeridiemMode(value, ampm, onChange),
    meridiemMode = _useMeridiemMode.meridiemMode,
    handleMeridiemChange = _useMeridiemMode.handleMeridiemChange;
  var showAmPmControl = Boolean(ampm && !ampmInClock);
  var isDesktop = toolbarVariant === 'desktop';
  var localeText = useLocaleText();
  var theme = useTheme();
  var classes = useUtilityClasses(_extends({}, ownerState, {
    theme: theme
  }));
  var formatHours = function formatHours(time) {
    return ampm ? utils.format(time, 'hours12h') : utils.format(time, 'hours24h');
  };
  var dateText = React.useMemo(function () {
    if (!value) {
      return toolbarPlaceholder;
    }
    if (toolbarFormat) {
      return utils.formatByString(value, toolbarFormat);
    }
    return utils.format(value, 'shortDate');
  }, [value, toolbarFormat, toolbarPlaceholder, utils]);
  return /*#__PURE__*/_jsxs(DateTimePickerToolbarRoot, _extends({
    toolbarTitle: localeText.dateTimePickerToolbarTitle,
    isLandscape: isLandscape,
    className: classes.root
  }, other, {
    ownerState: ownerState,
    children: [/*#__PURE__*/_jsxs(DateTimePickerToolbarDateContainer, {
      className: classes.dateContainer,
      ownerState: ownerState,
      children: [views.includes('year') && /*#__PURE__*/_jsx(PickersToolbarButton, {
        tabIndex: -1,
        variant: "subtitle1",
        onClick: function onClick() {
          return onViewChange('year');
        },
        selected: view === 'year',
        value: value ? utils.format(value, 'year') : '–'
      }), views.includes('day') && /*#__PURE__*/_jsx(PickersToolbarButton, {
        tabIndex: -1,
        variant: isDesktop ? 'h5' : 'h4',
        onClick: function onClick() {
          return onViewChange('day');
        },
        selected: view === 'day',
        value: dateText
      })]
    }), /*#__PURE__*/_jsxs(DateTimePickerToolbarTimeContainer, {
      className: classes.timeContainer,
      ownerState: ownerState,
      children: [/*#__PURE__*/_jsxs(DateTimePickerToolbarTimeDigitsContainer, {
        className: classes.timeDigitsContainer,
        ownerState: ownerState,
        children: [views.includes('hours') && /*#__PURE__*/_jsx(PickersToolbarButton, {
          variant: isDesktop ? 'h5' : 'h3',
          width: isDesktop && !isLandscape ? MULTI_SECTION_CLOCK_SECTION_WIDTH : undefined,
          onClick: function onClick() {
            return onViewChange('hours');
          },
          selected: view === 'hours',
          value: value ? formatHours(value) : '--'
        }), views.includes('minutes') && /*#__PURE__*/_jsxs(React.Fragment, {
          children: [/*#__PURE__*/_jsx(DateTimePickerToolbarSeparator, {
            variant: isDesktop ? 'h5' : 'h3',
            value: ":",
            className: classes.separator,
            ownerState: ownerState
          }), /*#__PURE__*/_jsx(PickersToolbarButton, {
            variant: isDesktop ? 'h5' : 'h3',
            width: isDesktop && !isLandscape ? MULTI_SECTION_CLOCK_SECTION_WIDTH : undefined,
            onClick: function onClick() {
              return onViewChange('minutes');
            },
            selected: view === 'minutes',
            value: value ? utils.format(value, 'minutes') : '--'
          })]
        }), views.includes('seconds') && /*#__PURE__*/_jsxs(React.Fragment, {
          children: [/*#__PURE__*/_jsx(DateTimePickerToolbarSeparator, {
            variant: isDesktop ? 'h5' : 'h3',
            value: ":",
            className: classes.separator,
            ownerState: ownerState
          }), /*#__PURE__*/_jsx(PickersToolbarButton, {
            variant: isDesktop ? 'h5' : 'h3',
            width: isDesktop && !isLandscape ? MULTI_SECTION_CLOCK_SECTION_WIDTH : undefined,
            onClick: function onClick() {
              return onViewChange('seconds');
            },
            selected: view === 'seconds',
            value: value ? utils.format(value, 'seconds') : '--'
          })]
        })]
      }), showAmPmControl && !isDesktop && /*#__PURE__*/_jsxs(DateTimePickerToolbarAmPmSelection, {
        className: classes.ampmSelection,
        ownerState: ownerState,
        children: [/*#__PURE__*/_jsx(PickersToolbarButton, {
          variant: "subtitle2",
          selected: meridiemMode === 'am',
          typographyClassName: classes.ampmLabel,
          value: formatMeridiem(utils, 'am'),
          onClick: readOnly ? undefined : function () {
            return handleMeridiemChange('am');
          },
          disabled: disabled
        }), /*#__PURE__*/_jsx(PickersToolbarButton, {
          variant: "subtitle2",
          selected: meridiemMode === 'pm',
          typographyClassName: classes.ampmLabel,
          value: formatMeridiem(utils, 'pm'),
          onClick: readOnly ? undefined : function () {
            return handleMeridiemChange('pm');
          },
          disabled: disabled
        })]
      }), ampm && isDesktop && /*#__PURE__*/_jsx(PickersToolbarButton, {
        variant: "h5",
        onClick: function onClick() {
          return onViewChange('meridiem');
        },
        selected: view === 'meridiem',
        value: value && meridiemMode ? formatMeridiem(utils, meridiemMode) : '--',
        width: MULTI_SECTION_CLOCK_SECTION_WIDTH
      })]
    })]
  }));
}
process.env.NODE_ENV !== "production" ? DateTimePickerToolbar.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  ampm: PropTypes.bool,
  ampmInClock: PropTypes.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  /**
   * className applied to the root component.
   */
  className: PropTypes.string,
  disabled: PropTypes.bool,
  /**
   * If `true`, show the toolbar even in desktop mode.
   * @default `true` for Desktop, `false` for Mobile.
   */
  hidden: PropTypes.bool,
  isLandscape: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  /**
   * Callback called when a toolbar is clicked
   * @template TView
   * @param {TView} view The view to open
   */
  onViewChange: PropTypes.func.isRequired,
  readOnly: PropTypes.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  titleId: PropTypes.string,
  /**
   * Toolbar date format.
   */
  toolbarFormat: PropTypes.string,
  /**
   * Toolbar value placeholder—it is displayed when the value is empty.
   * @default "––"
   */
  toolbarPlaceholder: PropTypes.node,
  toolbarVariant: PropTypes.oneOf(['desktop', 'mobile']),
  value: PropTypes.any,
  /**
   * Currently visible picker view.
   */
  view: PropTypes.oneOf(['day', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'year']).isRequired,
  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'hours', 'meridiem', 'minutes', 'month', 'seconds', 'year']).isRequired).isRequired
} : void 0;
export { DateTimePickerToolbar };