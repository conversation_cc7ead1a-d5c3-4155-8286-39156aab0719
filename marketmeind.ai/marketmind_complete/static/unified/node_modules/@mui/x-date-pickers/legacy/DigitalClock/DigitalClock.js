import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["ampm", "timeStep", "autoFocus", "components", "componentsProps", "slots", "slotProps", "value", "defaultValue", "referenceDate", "disableIgnoringDatePartForTimeValidation", "maxTime", "minTime", "disableFuture", "disablePast", "minutesStep", "shouldDisableClock", "shouldDisableTime", "onChange", "view", "openTo", "onViewChange", "focusedView", "onFocusedViewChange", "className", "disabled", "readOnly", "views", "skipDisabled", "timezone"];
import * as React from 'react';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { useSlotProps } from '@mui/base/utils';
import { alpha, styled, useThemeProps } from '@mui/material/styles';
import useEventCallback from '@mui/utils/useEventCallback';
import composeClasses from '@mui/utils/composeClasses';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import useForkRef from '@mui/utils/useForkRef';
import { useUtils, useNow, useLocaleText } from '../internals/hooks/useUtils';
import { createIsAfterIgnoreDatePart } from '../internals/utils/time-utils';
import { PickerViewRoot } from '../internals/components/PickerViewRoot';
import { getDigitalClockUtilityClass } from './digitalClockClasses';
import { useViews } from '../internals/hooks/useViews';
import { DIGITAL_CLOCK_VIEW_HEIGHT } from '../internals/constants/dimensions';
import { useControlledValueWithTimezone } from '../internals/hooks/useValueWithTimezone';
import { singleItemValueManager } from '../internals/utils/valueManagers';
import { useClockReferenceDate } from '../internals/hooks/useClockReferenceDate';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    list: ['list'],
    item: ['item']
  };
  return composeClasses(slots, getDigitalClockUtilityClass, classes);
};
var DigitalClockRoot = styled(PickerViewRoot, {
  name: 'MuiDigitalClock',
  slot: 'Root',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.root;
  }
})(function (_ref) {
  var ownerState = _ref.ownerState;
  return {
    overflowY: 'auto',
    width: '100%',
    '@media (prefers-reduced-motion: no-preference)': {
      scrollBehavior: ownerState.alreadyRendered ? 'smooth' : 'auto'
    },
    maxHeight: DIGITAL_CLOCK_VIEW_HEIGHT
  };
});
var DigitalClockList = styled(MenuList, {
  name: 'MuiDigitalClock',
  slot: 'List',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.list;
  }
})({
  padding: 0
});
var DigitalClockItem = styled(MenuItem, {
  name: 'MuiDigitalClock',
  slot: 'Item',
  overridesResolver: function overridesResolver(props, styles) {
    return styles.item;
  }
})(function (_ref2) {
  var theme = _ref2.theme;
  return {
    padding: '8px 16px',
    margin: '2px 4px',
    '&:first-of-type': {
      marginTop: 4
    },
    '&:hover': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.primary.mainChannel, " / ").concat(theme.vars.palette.action.hoverOpacity, ")") : alpha(theme.palette.primary.main, theme.palette.action.hoverOpacity)
    },
    '&.Mui-selected': {
      backgroundColor: (theme.vars || theme).palette.primary.main,
      color: (theme.vars || theme).palette.primary.contrastText,
      '&:focus-visible, &:hover': {
        backgroundColor: (theme.vars || theme).palette.primary.dark
      }
    },
    '&.Mui-focusVisible': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.primary.mainChannel, " / ").concat(theme.vars.palette.action.focusOpacity, ")") : alpha(theme.palette.primary.main, theme.palette.action.focusOpacity)
    }
  };
});
/**
 * Demos:
 *
 * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)
 * - [DigitalClock](https://mui.com/x/react-date-pickers/digital-clock/)
 *
 * API:
 *
 * - [DigitalClock API](https://mui.com/x/api/date-pickers/digital-clock/)
 */
export var DigitalClock = /*#__PURE__*/React.forwardRef(function DigitalClock(inProps, ref) {
  var _ref3, _slots$digitalClockIt, _slotProps$digitalClo;
  var utils = useUtils();
  var containerRef = React.useRef(null);
  var handleRef = useForkRef(ref, containerRef);
  var props = useThemeProps({
    props: inProps,
    name: 'MuiDigitalClock'
  });
  var _props$ampm = props.ampm,
    ampm = _props$ampm === void 0 ? utils.is12HourCycleInCurrentLocale() : _props$ampm,
    _props$timeStep = props.timeStep,
    timeStep = _props$timeStep === void 0 ? 30 : _props$timeStep,
    autoFocus = props.autoFocus,
    components = props.components,
    componentsProps = props.componentsProps,
    slots = props.slots,
    slotProps = props.slotProps,
    valueProp = props.value,
    defaultValue = props.defaultValue,
    referenceDateProp = props.referenceDate,
    _props$disableIgnorin = props.disableIgnoringDatePartForTimeValidation,
    disableIgnoringDatePartForTimeValidation = _props$disableIgnorin === void 0 ? false : _props$disableIgnorin,
    maxTime = props.maxTime,
    minTime = props.minTime,
    disableFuture = props.disableFuture,
    disablePast = props.disablePast,
    _props$minutesStep = props.minutesStep,
    minutesStep = _props$minutesStep === void 0 ? 1 : _props$minutesStep,
    shouldDisableClock = props.shouldDisableClock,
    shouldDisableTime = props.shouldDisableTime,
    onChange = props.onChange,
    inView = props.view,
    openTo = props.openTo,
    onViewChange = props.onViewChange,
    focusedView = props.focusedView,
    onFocusedViewChange = props.onFocusedViewChange,
    className = props.className,
    disabled = props.disabled,
    readOnly = props.readOnly,
    _props$views = props.views,
    views = _props$views === void 0 ? ['hours'] : _props$views,
    _props$skipDisabled = props.skipDisabled,
    skipDisabled = _props$skipDisabled === void 0 ? false : _props$skipDisabled,
    timezoneProp = props.timezone,
    other = _objectWithoutProperties(props, _excluded);
  var _useControlledValueWi = useControlledValueWithTimezone({
      name: 'DigitalClock',
      timezone: timezoneProp,
      value: valueProp,
      defaultValue: defaultValue,
      onChange: onChange,
      valueManager: singleItemValueManager
    }),
    value = _useControlledValueWi.value,
    handleRawValueChange = _useControlledValueWi.handleValueChange,
    timezone = _useControlledValueWi.timezone;
  var localeText = useLocaleText();
  var now = useNow(timezone);
  var ownerState = React.useMemo(function () {
    return _extends({}, props, {
      alreadyRendered: !!containerRef.current
    });
  }, [props]);
  var classes = useUtilityClasses(ownerState);
  var ClockItem = (_ref3 = (_slots$digitalClockIt = slots == null ? void 0 : slots.digitalClockItem) != null ? _slots$digitalClockIt : components == null ? void 0 : components.DigitalClockItem) != null ? _ref3 : DigitalClockItem;
  var clockItemProps = useSlotProps({
    elementType: ClockItem,
    externalSlotProps: (_slotProps$digitalClo = slotProps == null ? void 0 : slotProps.digitalClockItem) != null ? _slotProps$digitalClo : componentsProps == null ? void 0 : componentsProps.digitalClockItem,
    ownerState: {},
    className: classes.item
  });
  var valueOrReferenceDate = useClockReferenceDate({
    value: value,
    referenceDate: referenceDateProp,
    utils: utils,
    props: props,
    timezone: timezone
  });
  var handleValueChange = useEventCallback(function (newValue) {
    return handleRawValueChange(newValue, 'finish', 'hours');
  });
  var _useViews = useViews({
      view: inView,
      views: views,
      openTo: openTo,
      onViewChange: onViewChange,
      onChange: handleValueChange,
      focusedView: focusedView,
      onFocusedViewChange: onFocusedViewChange
    }),
    setValueAndGoToNextView = _useViews.setValueAndGoToNextView;
  var handleItemSelect = useEventCallback(function (newValue) {
    setValueAndGoToNextView(newValue, 'finish');
  });
  React.useEffect(function () {
    if (containerRef.current === null) {
      return;
    }
    var selectedItem = containerRef.current.querySelector('[role="listbox"] [role="option"][aria-selected="true"]');
    if (!selectedItem) {
      return;
    }
    var offsetTop = selectedItem.offsetTop;

    // Subtracting the 4px of extra margin intended for the first visible section item
    containerRef.current.scrollTop = offsetTop - 4;
  });
  var isTimeDisabled = React.useCallback(function (valueToCheck) {
    var isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);
    var containsValidTime = function containsValidTime() {
      if (minTime && isAfter(minTime, valueToCheck)) {
        return false;
      }
      if (maxTime && isAfter(valueToCheck, maxTime)) {
        return false;
      }
      if (disableFuture && isAfter(valueToCheck, now)) {
        return false;
      }
      if (disablePast && isAfter(now, valueToCheck)) {
        return false;
      }
      return true;
    };
    var isValidValue = function isValidValue() {
      if (utils.getMinutes(valueToCheck) % minutesStep !== 0) {
        return false;
      }
      if (shouldDisableClock != null && shouldDisableClock(utils.toJsDate(valueToCheck).getTime(), 'hours')) {
        return false;
      }
      if (shouldDisableTime) {
        return !shouldDisableTime(valueToCheck, 'hours');
      }
      return true;
    };
    return !containsValidTime() || !isValidValue();
  }, [disableIgnoringDatePartForTimeValidation, utils, minTime, maxTime, disableFuture, now, disablePast, minutesStep, shouldDisableClock, shouldDisableTime]);
  var timeOptions = React.useMemo(function () {
    var startOfDay = utils.startOfDay(valueOrReferenceDate);
    return [startOfDay].concat(_toConsumableArray(Array.from({
      length: Math.ceil(24 * 60 / timeStep) - 1
    }, function (_, index) {
      return utils.addMinutes(startOfDay, timeStep * (index + 1));
    })));
  }, [valueOrReferenceDate, timeStep, utils]);
  return /*#__PURE__*/_jsx(DigitalClockRoot, _extends({
    ref: handleRef,
    className: clsx(classes.root, className),
    ownerState: ownerState
  }, other, {
    children: /*#__PURE__*/_jsx(DigitalClockList, {
      autoFocusItem: autoFocus || !!focusedView,
      role: "listbox",
      "aria-label": localeText.timePickerToolbarTitle,
      className: classes.list,
      children: timeOptions.map(function (option) {
        if (skipDisabled && isTimeDisabled(option)) {
          return null;
        }
        var isSelected = utils.isEqual(option, value);
        return /*#__PURE__*/_jsx(ClockItem, _extends({
          onClick: function onClick() {
            return !readOnly && handleItemSelect(option);
          },
          selected: isSelected,
          disabled: disabled || isTimeDisabled(option),
          disableRipple: readOnly,
          role: "option"
          // aria-readonly is not supported here and does not have any effect
          ,
          "aria-disabled": readOnly,
          "aria-selected": isSelected
        }, clockItemProps, {
          children: utils.format(option, ampm ? 'fullTime12h' : 'fullTime24h')
        }), utils.toISO(option));
      })
    })
  }));
});
process.env.NODE_ENV !== "production" ? DigitalClock.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * 12h/24h view for hour selection clock.
   * @default `utils.is12HourCycleInCurrentLocale()`
   */
  ampm: PropTypes.bool,
  /**
   * If `true`, the main element is focused during the first mount.
   * This main element is:
   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).
   * - the `input` element if there is a field rendered.
   */
  autoFocus: PropTypes.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  className: PropTypes.string,
  /**
   * Overrideable components.
   * @default {}
   * @deprecated Please use `slots`.
   */
  components: PropTypes.object,
  /**
   * The props used for each component slot.
   * @default {}
   * @deprecated Please use `slotProps`.
   */
  componentsProps: PropTypes.object,
  /**
   * The default selected value.
   * Used when the component is not controlled.
   */
  defaultValue: PropTypes.any,
  /**
   * If `true`, the picker views and text field are disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  /**
   * If `true`, disable values after the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disableFuture: PropTypes.bool,
  /**
   * Do not ignore date part when validating min/max time.
   * @default false
   */
  disableIgnoringDatePartForTimeValidation: PropTypes.bool,
  /**
   * If `true`, disable values before the current date for date components, time for time components and both for date time components.
   * @default false
   */
  disablePast: PropTypes.bool,
  /**
   * Controlled focused view.
   */
  focusedView: PropTypes.oneOf(['hours']),
  /**
   * Maximal selectable time.
   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.
   */
  maxTime: PropTypes.any,
  /**
   * Minimal selectable time.
   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.
   */
  minTime: PropTypes.any,
  /**
   * Step over minutes.
   * @default 1
   */
  minutesStep: PropTypes.number,
  /**
   * Callback fired when the value changes.
   * @template TValue The value type. Will be either the same type as `value` or `null`. Can be in `[start, end]` format in case of range value.
   * @template TView The view type. Will be one of date or time views.
   * @param {TValue} value The new value.
   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.
   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.
   */
  onChange: PropTypes.func,
  /**
   * Callback fired on focused view change.
   * @template TView
   * @param {TView} view The new view to focus or not.
   * @param {boolean} hasFocus `true` if the view should be focused.
   */
  onFocusedViewChange: PropTypes.func,
  /**
   * Callback fired on view change.
   * @template TView
   * @param {TView} view The new view.
   */
  onViewChange: PropTypes.func,
  /**
   * The default visible view.
   * Used when the component view is not controlled.
   * Must be a valid option from `views` list.
   */
  openTo: PropTypes.oneOf(['hours']),
  /**
   * If `true`, the picker views and text field are read-only.
   * @default false
   */
  readOnly: PropTypes.bool,
  /**
   * The date used to generate the new value when both `value` and `defaultValue` are empty.
   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.
   */
  referenceDate: PropTypes.any,
  /**
   * Disable specific clock time.
   * @param {number} clockValue The value to check.
   * @param {TimeView} view The clock type of the timeValue.
   * @returns {boolean} If `true` the time will be disabled.
   * @deprecated Consider using `shouldDisableTime`.
   */
  shouldDisableClock: PropTypes.func,
  /**
   * Disable specific time.
   * @template TDate
   * @param {TDate} value The value to check.
   * @param {TimeView} view The clock type of the timeValue.
   * @returns {boolean} If `true` the time will be disabled.
   */
  shouldDisableTime: PropTypes.func,
  /**
   * If `true`, disabled digital clock items will not be rendered.
   * @default false
   */
  skipDisabled: PropTypes.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overrideable component slots.
   * @default {}
   */
  slots: PropTypes.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  /**
   * The time steps between two time options.
   * For example, if `timeStep = 45`, then the available time options will be `[00:00, 00:45, 01:30, 02:15, 03:00, etc.]`.
   * @default 30
   */
  timeStep: PropTypes.number,
  /**
   * Choose which timezone to use for the value.
   * Example: "default", "system", "UTC", "America/New_York".
   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.
   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documention} for more details.
   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.
   */
  timezone: PropTypes.string,
  /**
   * The selected value.
   * Used when the component is controlled.
   */
  value: PropTypes.any,
  /**
   * The visible view.
   * Used when the component view is controlled.
   * Must be a valid option from `views` list.
   */
  view: PropTypes.oneOf(['hours']),
  /**
   * Available views.
   * @default ['hours']
   */
  views: PropTypes.arrayOf(PropTypes.oneOf(['hours']))
} : void 0;