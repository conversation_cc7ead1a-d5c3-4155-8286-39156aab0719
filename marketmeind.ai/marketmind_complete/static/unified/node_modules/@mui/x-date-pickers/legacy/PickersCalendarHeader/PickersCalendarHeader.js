import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _extends from "@babel/runtime/helpers/esm/extends";
var _excluded = ["slots", "slotProps", "components", "componentsProps", "currentMonth", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "onViewChange", "view", "reduceAnimations", "views", "labelId", "className", "timezone"],
  _excluded2 = ["ownerState"];
import * as React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import Fade from '@mui/material/Fade';
import { styled, useThemeProps } from '@mui/material/styles';
import { useSlotProps } from '@mui/base/utils';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import IconButton from '@mui/material/IconButton';
import { useLocaleText, useUtils } from '../internals/hooks/useUtils';
import { PickersFadeTransitionGroup } from '../DateCalendar/PickersFadeTransitionGroup';
import { ArrowDropDownIcon } from '../icons';
import { PickersArrowSwitcher } from '../internals/components/PickersArrowSwitcher';
import { usePreviousMonthDisabled, useNextMonthDisabled } from '../internals/hooks/date-helpers-hooks';
import { getPickersCalendarHeaderUtilityClass, pickersCalendarHeaderClasses } from './pickersCalendarHeaderClasses';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    labelContainer: ['labelContainer'],
    label: ['label'],
    switchViewButton: ['switchViewButton'],
    switchViewIcon: ['switchViewIcon']
  };
  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);
};
var PickersCalendarHeaderRoot = styled('div', {
  name: 'MuiPickersCalendarHeader',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.root;
  }
})({
  display: 'flex',
  alignItems: 'center',
  marginTop: 16,
  marginBottom: 8,
  paddingLeft: 24,
  paddingRight: 12,
  // prevent jumping in safari
  maxHeight: 30,
  minHeight: 30
});
var PickersCalendarHeaderLabelContainer = styled('div', {
  name: 'MuiPickersCalendarHeader',
  slot: 'LabelContainer',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.labelContainer;
  }
})(function (_ref) {
  var theme = _ref.theme;
  return _extends({
    display: 'flex',
    overflow: 'hidden',
    alignItems: 'center',
    cursor: 'pointer',
    marginRight: 'auto'
  }, theme.typography.body1, {
    fontWeight: theme.typography.fontWeightMedium
  });
});
var PickersCalendarHeaderLabel = styled('div', {
  name: 'MuiPickersCalendarHeader',
  slot: 'Label',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.label;
  }
})({
  marginRight: 6
});
var PickersCalendarHeaderSwitchViewButton = styled(IconButton, {
  name: 'MuiPickersCalendarHeader',
  slot: 'SwitchViewButton',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.switchViewButton;
  }
})(function (_ref2) {
  var ownerState = _ref2.ownerState;
  return _extends({
    marginRight: 'auto'
  }, ownerState.view === 'year' && _defineProperty({}, ".".concat(pickersCalendarHeaderClasses.switchViewIcon), {
    transform: 'rotate(180deg)'
  }));
});
var PickersCalendarHeaderSwitchViewIcon = styled(ArrowDropDownIcon, {
  name: 'MuiPickersCalendarHeader',
  slot: 'SwitchViewIcon',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.switchViewIcon;
  }
})(function (_ref4) {
  var theme = _ref4.theme;
  return {
    willChange: 'transform',
    transition: theme.transitions.create('transform'),
    transform: 'rotate(0deg)'
  };
});
/**
 * Demos:
 *
 * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)
 * - [DateRangeCalendar](https://mui.com/x/react-date-pickers/date-range-calendar/)
 * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)
 *
 * API:
 *
 * - [PickersCalendarHeader API](https://mui.com/x/api/date-pickers/pickers-calendar-header/)
 */
var PickersCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersCalendarHeader(inProps, ref) {
  var _ref5, _slots$switchViewButt, _ref6, _slots$switchViewIcon;
  var localeText = useLocaleText();
  var utils = useUtils();
  var props = useThemeProps({
    props: inProps,
    name: 'MuiPickersCalendarHeader'
  });
  var slots = props.slots,
    slotProps = props.slotProps,
    components = props.components,
    componentsProps = props.componentsProps,
    month = props.currentMonth,
    disabled = props.disabled,
    disableFuture = props.disableFuture,
    disablePast = props.disablePast,
    maxDate = props.maxDate,
    minDate = props.minDate,
    onMonthChange = props.onMonthChange,
    onViewChange = props.onViewChange,
    view = props.view,
    reduceAnimations = props.reduceAnimations,
    views = props.views,
    labelId = props.labelId,
    className = props.className,
    timezone = props.timezone,
    other = _objectWithoutProperties(props, _excluded);
  var ownerState = props;
  var classes = useUtilityClasses(props);
  var SwitchViewButton = (_ref5 = (_slots$switchViewButt = slots == null ? void 0 : slots.switchViewButton) != null ? _slots$switchViewButt : components == null ? void 0 : components.SwitchViewButton) != null ? _ref5 : PickersCalendarHeaderSwitchViewButton;
  var switchViewButtonProps = useSlotProps({
    elementType: SwitchViewButton,
    externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewButton,
    additionalProps: {
      size: 'small',
      'aria-label': localeText.calendarViewSwitchingButtonAriaLabel(view)
    },
    ownerState: ownerState,
    className: classes.switchViewButton
  });
  var SwitchViewIcon = (_ref6 = (_slots$switchViewIcon = slots == null ? void 0 : slots.switchViewIcon) != null ? _slots$switchViewIcon : components == null ? void 0 : components.SwitchViewIcon) != null ? _ref6 : PickersCalendarHeaderSwitchViewIcon;
  // The spread is here to avoid this bug mui/material-ui#34056
  var _useSlotProps = useSlotProps({
      elementType: SwitchViewIcon,
      externalSlotProps: slotProps == null ? void 0 : slotProps.switchViewIcon,
      ownerState: undefined,
      className: classes.switchViewIcon
    }),
    switchViewIconOwnerState = _useSlotProps.ownerState,
    switchViewIconProps = _objectWithoutProperties(_useSlotProps, _excluded2);
  var selectNextMonth = function selectNextMonth() {
    return onMonthChange(utils.addMonths(month, 1), 'left');
  };
  var selectPreviousMonth = function selectPreviousMonth() {
    return onMonthChange(utils.addMonths(month, -1), 'right');
  };
  var isNextMonthDisabled = useNextMonthDisabled(month, {
    disableFuture: disableFuture,
    maxDate: maxDate,
    timezone: timezone
  });
  var isPreviousMonthDisabled = usePreviousMonthDisabled(month, {
    disablePast: disablePast,
    minDate: minDate,
    timezone: timezone
  });
  var handleToggleView = function handleToggleView() {
    if (views.length === 1 || !onViewChange || disabled) {
      return;
    }
    if (views.length === 2) {
      onViewChange(views.find(function (el) {
        return el !== view;
      }) || views[0]);
    } else {
      // switching only between first 2
      var nextIndexToOpen = views.indexOf(view) !== 0 ? 0 : 1;
      onViewChange(views[nextIndexToOpen]);
    }
  };

  // No need to display more information
  if (views.length === 1 && views[0] === 'year') {
    return null;
  }
  return /*#__PURE__*/_jsxs(PickersCalendarHeaderRoot, _extends({}, other, {
    ownerState: ownerState,
    className: clsx(className, classes.root),
    ref: ref,
    children: [/*#__PURE__*/_jsxs(PickersCalendarHeaderLabelContainer, {
      role: "presentation",
      onClick: handleToggleView,
      ownerState: ownerState
      // putting this on the label item element below breaks when using transition
      ,
      "aria-live": "polite",
      className: classes.labelContainer,
      children: [/*#__PURE__*/_jsx(PickersFadeTransitionGroup, {
        reduceAnimations: reduceAnimations,
        transKey: utils.format(month, 'monthAndYear'),
        children: /*#__PURE__*/_jsx(PickersCalendarHeaderLabel, {
          id: labelId,
          ownerState: ownerState,
          className: classes.label,
          children: utils.format(month, 'monthAndYear')
        })
      }), views.length > 1 && !disabled && /*#__PURE__*/_jsx(SwitchViewButton, _extends({}, switchViewButtonProps, {
        children: /*#__PURE__*/_jsx(SwitchViewIcon, _extends({}, switchViewIconProps))
      }))]
    }), /*#__PURE__*/_jsx(Fade, {
      in: view === 'day',
      children: /*#__PURE__*/_jsx(PickersArrowSwitcher, {
        slots: slots,
        slotProps: slotProps,
        onGoToPrevious: selectPreviousMonth,
        isPreviousDisabled: isPreviousMonthDisabled,
        previousLabel: localeText.previousMonth,
        onGoToNext: selectNextMonth,
        isNextDisabled: isNextMonthDisabled,
        nextLabel: localeText.nextMonth
      })
    })]
  }));
});
process.env.NODE_ENV !== "production" ? PickersCalendarHeader.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: PropTypes.object,
  /**
   * className applied to the root element.
   */
  className: PropTypes.string,
  /**
   * Overridable components.
   * @default {}
   * @deprecated Please use `slots`.
   */
  components: PropTypes.object,
  /**
   * The props used for each component slot.
   * @default {}
   * @deprecated Please use `slotProps`.
   */
  componentsProps: PropTypes.object,
  currentMonth: PropTypes.any.isRequired,
  disabled: PropTypes.bool,
  disableFuture: PropTypes.bool,
  disablePast: PropTypes.bool,
  labelId: PropTypes.string,
  maxDate: PropTypes.any.isRequired,
  minDate: PropTypes.any.isRequired,
  onMonthChange: PropTypes.func.isRequired,
  onViewChange: PropTypes.func,
  reduceAnimations: PropTypes.bool.isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: PropTypes.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: PropTypes.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),
  timezone: PropTypes.string.isRequired,
  view: PropTypes.oneOf(['day', 'month', 'year']).isRequired,
  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired).isRequired
} : void 0;
export { PickersCalendarHeader };