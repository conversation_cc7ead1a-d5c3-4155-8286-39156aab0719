import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _excluded = ["className", "disabled", "index", "inner", "label", "selected"];
import * as React from 'react';
import clsx from 'clsx';
import { styled, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { CLOCK_WIDTH, CLOCK_HOUR_WIDTH } from './shared';
import { getClockNumberUtilityClass, clockNumberClasses } from './clockNumberClasses';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes,
    selected = ownerState.selected,
    disabled = ownerState.disabled;
  var slots = {
    root: ['root', selected && 'selected', disabled && 'disabled']
  };
  return composeClasses(slots, getClockNumberUtilityClass, classes);
};
var ClockNumberRoot = styled('span', {
  name: 'MuiClockNumber',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return [styles.root, _defineProperty({}, "&.".concat(clockNumberClasses.disabled), styles.disabled), _defineProperty({}, "&.".concat(clockNumberClasses.selected), styles.selected)];
  }
})(function (_ref3) {
  var theme = _ref3.theme,
    ownerState = _ref3.ownerState;
  return _extends(_defineProperty(_defineProperty({
    height: CLOCK_HOUR_WIDTH,
    width: CLOCK_HOUR_WIDTH,
    position: 'absolute',
    left: "calc((100% - ".concat(CLOCK_HOUR_WIDTH, "px) / 2)"),
    display: 'inline-flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
    color: (theme.vars || theme).palette.text.primary,
    fontFamily: theme.typography.fontFamily,
    '&:focused': {
      backgroundColor: (theme.vars || theme).palette.background.paper
    }
  }, "&.".concat(clockNumberClasses.selected), {
    color: (theme.vars || theme).palette.primary.contrastText
  }), "&.".concat(clockNumberClasses.disabled), {
    pointerEvents: 'none',
    color: (theme.vars || theme).palette.text.disabled
  }), ownerState.inner && _extends({}, theme.typography.body2, {
    color: (theme.vars || theme).palette.text.secondary
  }));
});

/**
 * @ignore - internal component.
 */
export function ClockNumber(inProps) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiClockNumber'
  });
  var className = props.className,
    disabled = props.disabled,
    index = props.index,
    inner = props.inner,
    label = props.label,
    selected = props.selected,
    other = _objectWithoutProperties(props, _excluded);
  var ownerState = props;
  var classes = useUtilityClasses(ownerState);
  var angle = index % 12 / 12 * Math.PI * 2 - Math.PI / 2;
  var length = (CLOCK_WIDTH - CLOCK_HOUR_WIDTH - 2) / 2 * (inner ? 0.65 : 1);
  var x = Math.round(Math.cos(angle) * length);
  var y = Math.round(Math.sin(angle) * length);
  return /*#__PURE__*/_jsx(ClockNumberRoot, _extends({
    className: clsx(className, classes.root),
    "aria-disabled": disabled ? true : undefined,
    "aria-selected": selected ? true : undefined,
    role: "option",
    style: {
      transform: "translate(".concat(x, "px, ").concat(y + (CLOCK_WIDTH - CLOCK_HOUR_WIDTH) / 2, "px")
    },
    ownerState: ownerState
  }, other, {
    children: label
  }));
}