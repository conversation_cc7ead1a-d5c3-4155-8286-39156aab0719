import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["autoFocus", "onChange", "className", "disabled", "readOnly", "items", "active", "slots", "slotProps", "skipDisabled"];
import * as React from 'react';
import clsx from 'clsx';
import { alpha, styled, useThemeProps } from '@mui/material/styles';
import composeClasses from '@mui/utils/composeClasses';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import useForkRef from '@mui/utils/useForkRef';
import { getMultiSectionDigitalClockSectionUtilityClass } from './multiSectionDigitalClockSectionClasses';
import { DIGITAL_CLOCK_VIEW_HEIGHT, MULTI_SECTION_CLOCK_SECTION_WIDTH } from '../internals/constants/dimensions';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes;
  var slots = {
    root: ['root'],
    item: ['item']
  };
  return composeClasses(slots, getMultiSectionDigitalClockSectionUtilityClass, classes);
};
var MultiSectionDigitalClockSectionRoot = styled(MenuList, {
  name: 'MuiMultiSectionDigitalClockSection',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.root;
  }
})(function (_ref) {
  var theme = _ref.theme,
    ownerState = _ref.ownerState;
  return {
    maxHeight: DIGITAL_CLOCK_VIEW_HEIGHT,
    width: 56,
    padding: 0,
    overflow: 'hidden',
    '@media (prefers-reduced-motion: no-preference)': {
      scrollBehavior: ownerState.alreadyRendered ? 'smooth' : 'auto'
    },
    '@media (pointer: fine)': {
      '&:hover': {
        overflowY: 'auto'
      }
    },
    '@media (pointer: none), (pointer: coarse)': {
      overflowY: 'auto'
    },
    '&:not(:first-of-type)': {
      borderLeft: "1px solid ".concat((theme.vars || theme).palette.divider)
    },
    '&:after': {
      display: 'block',
      content: '""',
      // subtracting the height of one item, extra margin and borders to make sure the max height is correct
      height: 'calc(100% - 40px - 6px)'
    }
  };
});
var MultiSectionDigitalClockSectionItem = styled(MenuItem, {
  name: 'MuiMultiSectionDigitalClockSection',
  slot: 'Item',
  overridesResolver: function overridesResolver(_, styles) {
    return styles.item;
  }
})(function (_ref2) {
  var theme = _ref2.theme;
  return {
    padding: 8,
    margin: '2px 4px',
    width: MULTI_SECTION_CLOCK_SECTION_WIDTH,
    justifyContent: 'center',
    '&:first-of-type': {
      marginTop: 4
    },
    '&:hover': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.primary.mainChannel, " / ").concat(theme.vars.palette.action.hoverOpacity, ")") : alpha(theme.palette.primary.main, theme.palette.action.hoverOpacity)
    },
    '&.Mui-selected': {
      backgroundColor: (theme.vars || theme).palette.primary.main,
      color: (theme.vars || theme).palette.primary.contrastText,
      '&:focus-visible, &:hover': {
        backgroundColor: (theme.vars || theme).palette.primary.dark
      }
    },
    '&.Mui-focusVisible': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.primary.mainChannel, " / ").concat(theme.vars.palette.action.focusOpacity, ")") : alpha(theme.palette.primary.main, theme.palette.action.focusOpacity)
    }
  };
});
/**
 * @ignore - internal component.
 */
export var MultiSectionDigitalClockSection = /*#__PURE__*/React.forwardRef(function MultiSectionDigitalClockSection(inProps, ref) {
  var _slots$digitalClockSe;
  var containerRef = React.useRef(null);
  var handleRef = useForkRef(ref, containerRef);
  var previousActive = React.useRef(null);
  var props = useThemeProps({
    props: inProps,
    name: 'MuiMultiSectionDigitalClockSection'
  });
  var autoFocus = props.autoFocus,
    onChange = props.onChange,
    className = props.className,
    disabled = props.disabled,
    readOnly = props.readOnly,
    items = props.items,
    active = props.active,
    slots = props.slots,
    slotProps = props.slotProps,
    skipDisabled = props.skipDisabled,
    other = _objectWithoutProperties(props, _excluded);
  var ownerState = React.useMemo(function () {
    return _extends({}, props, {
      alreadyRendered: !!containerRef.current
    });
  }, [props]);
  var classes = useUtilityClasses(ownerState);
  var DigitalClockSectionItem = (_slots$digitalClockSe = slots == null ? void 0 : slots.digitalClockSectionItem) != null ? _slots$digitalClockSe : MultiSectionDigitalClockSectionItem;
  React.useEffect(function () {
    if (containerRef.current === null) {
      return;
    }
    var activeItem = containerRef.current.querySelector('[role="option"][aria-selected="true"]');
    if (active && autoFocus && activeItem) {
      activeItem.focus();
    }
    if (!activeItem || previousActive.current === activeItem) {
      return;
    }
    previousActive.current = activeItem;
    var offsetTop = activeItem.offsetTop;

    // Subtracting the 4px of extra margin intended for the first visible section item
    containerRef.current.scrollTop = offsetTop - 4;
  });
  return /*#__PURE__*/_jsx(MultiSectionDigitalClockSectionRoot, _extends({
    ref: handleRef,
    className: clsx(classes.root, className),
    ownerState: ownerState,
    autoFocusItem: autoFocus && active,
    role: "listbox"
  }, other, {
    children: items.map(function (option) {
      var _option$isDisabled, _option$isDisabled2;
      if (skipDisabled && (_option$isDisabled = option.isDisabled) != null && _option$isDisabled.call(option, option.value)) {
        return null;
      }
      var isSelected = option.isSelected(option.value);
      return /*#__PURE__*/_jsx(DigitalClockSectionItem, _extends({
        onClick: function onClick() {
          return !readOnly && onChange(option.value);
        },
        selected: isSelected,
        disabled: disabled || ((_option$isDisabled2 = option.isDisabled) == null ? void 0 : _option$isDisabled2.call(option, option.value)),
        disableRipple: readOnly,
        role: "option"
        // aria-readonly is not supported here and does not have any effect
        ,
        "aria-disabled": readOnly,
        "aria-label": option.ariaLabel,
        "aria-selected": isSelected,
        className: classes.item
      }, slotProps == null ? void 0 : slotProps.digitalClockSectionItem, {
        children: option.label
      }), option.label);
    })
  }));
});