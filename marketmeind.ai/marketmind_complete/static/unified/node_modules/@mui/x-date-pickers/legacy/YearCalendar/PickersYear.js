import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _excluded = ["autoFocus", "className", "children", "disabled", "selected", "value", "tabIndex", "onClick", "onKeyDown", "onFocus", "onBlur", "aria-current", "yearsPerRow"];
import * as React from 'react';
import clsx from 'clsx';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { alpha, styled, useThemeProps } from '@mui/material/styles';
import { getPickersYearUtilityClass, pickersYearClasses } from './pickersYearClasses';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var disabled = ownerState.disabled,
    selected = ownerState.selected,
    classes = ownerState.classes;
  var slots = {
    root: ['root'],
    yearButton: ['yearButton', disabled && 'disabled', selected && 'selected']
  };
  return composeClasses(slots, getPickersYearUtilityClass, classes);
};
var PickersYearRoot = styled('div', {
  name: 'MuiPickersYear',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return [styles.root];
  }
})(function (_ref) {
  var ownerState = _ref.ownerState;
  return {
    flexBasis: ownerState.yearsPerRow === 3 ? '33.3%' : '25%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };
});
var PickersYearButton = styled('button', {
  name: 'MuiPickersYear',
  slot: 'YearButton',
  overridesResolver: function overridesResolver(_, styles) {
    return [styles.yearButton, _defineProperty({}, "&.".concat(pickersYearClasses.disabled), styles.disabled), _defineProperty({}, "&.".concat(pickersYearClasses.selected), styles.selected)];
  }
})(function (_ref4) {
  var theme = _ref4.theme;
  return _extends({
    color: 'unset',
    backgroundColor: 'transparent',
    border: 0,
    outline: 0
  }, theme.typography.subtitle1, _defineProperty(_defineProperty({
    margin: '6px 0',
    height: 36,
    width: 72,
    borderRadius: 18,
    cursor: 'pointer',
    '&:focus': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.action.activeChannel, " / ").concat(theme.vars.palette.action.focusOpacity, ")") : alpha(theme.palette.action.active, theme.palette.action.focusOpacity)
    },
    '&:hover': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.action.activeChannel, " / ").concat(theme.vars.palette.action.hoverOpacity, ")") : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
    },
    '&:disabled': {
      cursor: 'auto',
      pointerEvents: 'none'
    }
  }, "&.".concat(pickersYearClasses.disabled), {
    color: (theme.vars || theme).palette.text.secondary
  }), "&.".concat(pickersYearClasses.selected), {
    color: (theme.vars || theme).palette.primary.contrastText,
    backgroundColor: (theme.vars || theme).palette.primary.main,
    '&:focus, &:hover': {
      backgroundColor: (theme.vars || theme).palette.primary.dark
    }
  }));
});

/**
 * @ignore - internal component.
 */
export var PickersYear = /*#__PURE__*/React.memo(function PickersYear(inProps) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiPickersYear'
  });
  var autoFocus = props.autoFocus,
    className = props.className,
    children = props.children,
    disabled = props.disabled,
    selected = props.selected,
    value = props.value,
    tabIndex = props.tabIndex,
    _onClick = props.onClick,
    _onKeyDown = props.onKeyDown,
    _onFocus = props.onFocus,
    _onBlur = props.onBlur,
    ariaCurrent = props['aria-current'],
    yearsPerRow = props.yearsPerRow,
    other = _objectWithoutProperties(props, _excluded);
  var ref = React.useRef(null);
  var classes = useUtilityClasses(props);

  // We can't forward the `autoFocus` to the button because it is a native button, not a MUI Button
  React.useEffect(function () {
    if (autoFocus) {
      // `ref.current` being `null` would be a bug in MUI.
      ref.current.focus();
    }
  }, [autoFocus]);
  return /*#__PURE__*/_jsx(PickersYearRoot, _extends({
    className: clsx(classes.root, className),
    ownerState: props
  }, other, {
    children: /*#__PURE__*/_jsx(PickersYearButton, {
      ref: ref,
      disabled: disabled,
      type: "button",
      role: "radio",
      tabIndex: disabled ? -1 : tabIndex,
      "aria-current": ariaCurrent,
      "aria-checked": selected,
      onClick: function onClick(event) {
        return _onClick(event, value);
      },
      onKeyDown: function onKeyDown(event) {
        return _onKeyDown(event, value);
      },
      onFocus: function onFocus(event) {
        return _onFocus(event, value);
      },
      onBlur: function onBlur(event) {
        return _onBlur(event, value);
      },
      className: classes.yearButton,
      ownerState: props,
      children: children
    })
  }));
});