import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
import _extends from "@babel/runtime/helpers/esm/extends";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
var _excluded = ["autoFocus", "children", "disabled", "selected", "value", "tabIndex", "onClick", "onKeyDown", "onFocus", "onBlur", "aria-current", "aria-label", "monthsPerRow"];
import * as React from 'react';
import { styled, alpha, useThemeProps } from '@mui/material/styles';
import { unstable_composeClasses as composeClasses, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';
import { getPickersMonthUtilityClass, pickersMonthClasses } from './pickersMonthClasses';
import { jsx as _jsx } from "react/jsx-runtime";
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var disabled = ownerState.disabled,
    selected = ownerState.selected,
    classes = ownerState.classes;
  var slots = {
    root: ['root'],
    monthButton: ['monthButton', disabled && 'disabled', selected && 'selected']
  };
  return composeClasses(slots, getPickersMonthUtilityClass, classes);
};
var PickersMonthRoot = styled('div', {
  name: 'MuiPickersMonth',
  slot: 'Root',
  overridesResolver: function overridesResolver(_, styles) {
    return [styles.root];
  }
})(function (_ref) {
  var ownerState = _ref.ownerState;
  return {
    flexBasis: ownerState.monthsPerRow === 3 ? '33.3%' : '25%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  };
});
var PickersMonthButton = styled('button', {
  name: 'MuiPickersMonth',
  slot: 'MonthButton',
  overridesResolver: function overridesResolver(_, styles) {
    return [styles.monthButton, _defineProperty({}, "&.".concat(pickersMonthClasses.disabled), styles.disabled), _defineProperty({}, "&.".concat(pickersMonthClasses.selected), styles.selected)];
  }
})(function (_ref4) {
  var theme = _ref4.theme;
  return _extends({
    color: 'unset',
    backgroundColor: 'transparent',
    border: 0,
    outline: 0
  }, theme.typography.subtitle1, _defineProperty(_defineProperty({
    margin: '8px 0',
    height: 36,
    width: 72,
    borderRadius: 18,
    cursor: 'pointer',
    '&:focus': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.action.activeChannel, " / ").concat(theme.vars.palette.action.hoverOpacity, ")") : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
    },
    '&:hover': {
      backgroundColor: theme.vars ? "rgba(".concat(theme.vars.palette.action.activeChannel, " / ").concat(theme.vars.palette.action.hoverOpacity, ")") : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
    },
    '&:disabled': {
      cursor: 'auto',
      pointerEvents: 'none'
    }
  }, "&.".concat(pickersMonthClasses.disabled), {
    color: (theme.vars || theme).palette.text.secondary
  }), "&.".concat(pickersMonthClasses.selected), {
    color: (theme.vars || theme).palette.primary.contrastText,
    backgroundColor: (theme.vars || theme).palette.primary.main,
    '&:focus, &:hover': {
      backgroundColor: (theme.vars || theme).palette.primary.dark
    }
  }));
});

/**
 * @ignore - do not document.
 */
export var PickersMonth = /*#__PURE__*/React.memo(function PickersMonth(inProps) {
  var props = useThemeProps({
    props: inProps,
    name: 'MuiPickersMonth'
  });
  var autoFocus = props.autoFocus,
    children = props.children,
    disabled = props.disabled,
    selected = props.selected,
    value = props.value,
    tabIndex = props.tabIndex,
    _onClick = props.onClick,
    _onKeyDown = props.onKeyDown,
    _onFocus = props.onFocus,
    _onBlur = props.onBlur,
    ariaCurrent = props['aria-current'],
    ariaLabel = props['aria-label'],
    monthsPerRow = props.monthsPerRow,
    other = _objectWithoutProperties(props, _excluded);
  var ref = React.useRef(null);
  var classes = useUtilityClasses(props);
  useEnhancedEffect(function () {
    if (autoFocus) {
      var _ref$current;
      (_ref$current = ref.current) == null || _ref$current.focus();
    }
  }, [autoFocus]);
  return /*#__PURE__*/_jsx(PickersMonthRoot, _extends({
    className: classes.root,
    ownerState: props
  }, other, {
    children: /*#__PURE__*/_jsx(PickersMonthButton, {
      ref: ref,
      disabled: disabled,
      type: "button",
      role: "radio",
      tabIndex: disabled ? -1 : tabIndex,
      "aria-current": ariaCurrent,
      "aria-checked": selected,
      "aria-label": ariaLabel,
      onClick: function onClick(event) {
        return _onClick(event, value);
      },
      onKeyDown: function onKeyDown(event) {
        return _onKeyDown(event, value);
      },
      onFocus: function onFocus(event) {
        return _onFocus(event, value);
      },
      onBlur: function onBlur(event) {
        return _onBlur(event, value);
      },
      className: classes.monthButton,
      ownerState: props,
      children: children
    })
  }));
});