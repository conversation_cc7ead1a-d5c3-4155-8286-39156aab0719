import _extends from "@babel/runtime/helpers/esm/extends";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
/* eslint-disable class-methods-use-this */
import { DateTime, Info } from 'luxon';
var formatTokenMap = {
  // Year
  y: {
    sectionType: 'year',
    contentType: 'digit',
    maxLength: 4
  },
  yy: 'year',
  yyyy: {
    sectionType: 'year',
    contentType: 'digit',
    maxLength: 4
  },
  // Month
  L: {
    sectionType: 'month',
    contentType: 'digit',
    maxLength: 2
  },
  LL: 'month',
  LLL: {
    sectionType: 'month',
    contentType: 'letter'
  },
  LLLL: {
    sectionType: 'month',
    contentType: 'letter'
  },
  M: {
    sectionType: 'month',
    contentType: 'digit',
    maxLength: 2
  },
  MM: 'month',
  MMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  MMMM: {
    sectionType: 'month',
    contentType: 'letter'
  },
  // Day of the month
  d: {
    sectionType: 'day',
    contentType: 'digit',
    maxLength: 2
  },
  dd: 'day',
  // Day of the week
  c: {
    sectionType: 'weekDay',
    contentType: 'digit',
    maxLength: 1
  },
  ccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  cccc: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  E: {
    sectionType: 'weekDay',
    contentType: 'digit',
    maxLength: 2
  },
  EEE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  EEEE: {
    sectionType: 'weekDay',
    contentType: 'letter'
  },
  // Meridiem
  a: 'meridiem',
  // Hours
  H: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  HH: 'hours',
  h: {
    sectionType: 'hours',
    contentType: 'digit',
    maxLength: 2
  },
  hh: 'hours',
  // Minutes
  m: {
    sectionType: 'minutes',
    contentType: 'digit',
    maxLength: 2
  },
  mm: 'minutes',
  // Seconds
  s: {
    sectionType: 'seconds',
    contentType: 'digit',
    maxLength: 2
  },
  ss: 'seconds'
};
var defaultFormats = {
  year: 'yyyy',
  month: 'LLLL',
  monthShort: 'MMM',
  dayOfMonth: 'd',
  weekday: 'cccc',
  weekdayShort: 'ccccc',
  hours24h: 'HH',
  hours12h: 'hh',
  meridiem: 'a',
  minutes: 'mm',
  seconds: 'ss',
  fullDate: 'DD',
  fullDateWithWeekday: 'DDDD',
  keyboardDate: 'D',
  shortDate: 'MMM d',
  normalDate: 'd MMMM',
  normalDateWithWeekday: 'EEE, MMM d',
  monthAndYear: 'LLLL yyyy',
  monthAndDate: 'MMMM d',
  fullTime: 't',
  fullTime12h: 'hh:mm a',
  fullTime24h: 'HH:mm',
  fullDateTime: 'ff',
  fullDateTime12h: 'DD, hh:mm a',
  fullDateTime24h: 'DD, T',
  keyboardDateTime: 'D t',
  keyboardDateTime12h: 'D hh:mm a',
  keyboardDateTime24h: 'D T'
};

/**
 * Based on `@date-io/luxon`
 *
 * MIT License
 *
 * Copyright (c) 2017 Dmitriy Kovalenko
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
export var AdapterLuxon = /*#__PURE__*/_createClass(function AdapterLuxon() {
  var _this = this;
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    locale = _ref.locale,
    formats = _ref.formats;
  _classCallCheck(this, AdapterLuxon);
  this.isMUIAdapter = true;
  this.isTimezoneCompatible = true;
  this.lib = 'luxon';
  this.locale = void 0;
  this.formats = void 0;
  this.escapedCharacters = {
    start: "'",
    end: "'"
  };
  this.formatTokenMap = formatTokenMap;
  this.setLocaleToValue = function (value) {
    var expectedLocale = _this.getCurrentLocaleCode();
    if (expectedLocale === value.locale) {
      return value;
    }
    return value.setLocale(expectedLocale);
  };
  this.date = function (value) {
    if (typeof value === 'undefined') {
      return DateTime.local();
    }
    if (value === null) {
      return null;
    }
    if (typeof value === 'string') {
      // @ts-ignore
      return DateTime.fromJSDate(new Date(value), {
        locale: _this.locale
      });
    }
    if (DateTime.isDateTime(value)) {
      return value;
    }

    // @ts-ignore
    return DateTime.fromJSDate(value, {
      locale: _this.locale
    });
  };
  this.dateWithTimezone = function (value, timezone) {
    if (value === null) {
      return null;
    }
    if (typeof value === 'undefined') {
      // @ts-ignore
      return DateTime.fromJSDate(new Date(), {
        locale: _this.locale,
        zone: timezone
      });
    }

    // @ts-ignore
    return DateTime.fromISO(value, {
      locale: _this.locale,
      zone: timezone
    });
  };
  this.getTimezone = function (value) {
    // When using the system zone, we want to return "system", not something like "Europe/Paris"
    if (value.zone.type === 'system') {
      return 'system';
    }
    return value.zoneName;
  };
  this.setTimezone = function (value, timezone) {
    if (!value.zone.equals(Info.normalizeZone(timezone))) {
      return value.setZone(timezone);
    }
    return value;
  };
  this.toJsDate = function (value) {
    return value.toJSDate();
  };
  this.parseISO = function (isoString) {
    return DateTime.fromISO(isoString);
  };
  this.toISO = function (value) {
    return value.toUTC().toISO({
      format: 'extended'
    });
  };
  this.parse = function (value, formatString) {
    if (value === '') {
      return null;
    }
    return DateTime.fromFormat(value, formatString, {
      locale: _this.locale
    });
  };
  this.getCurrentLocaleCode = function () {
    return _this.locale;
  };
  /* istanbul ignore next */
  this.is12HourCycleInCurrentLocale = function () {
    var _Intl$DateTimeFormat;
    if (typeof Intl === 'undefined' || typeof Intl.DateTimeFormat === 'undefined') {
      return true; // Luxon defaults to en-US if Intl not found
    }
    return Boolean((_Intl$DateTimeFormat = new Intl.DateTimeFormat(_this.locale, {
      hour: 'numeric'
    })) == null || (_Intl$DateTimeFormat = _Intl$DateTimeFormat.resolvedOptions()) == null ? void 0 : _Intl$DateTimeFormat.hour12);
  };
  this.expandFormat = function (format) {
    // Extract escaped section to avoid extending them
    var longFormatRegexp = /''|'(''|[^'])+('|$)|[^']*/g;
    return format.match(longFormatRegexp).map(function (token) {
      var firstCharacter = token[0];
      if (firstCharacter === "'") {
        return token;
      }
      return DateTime.expandFormat(token, {
        locale: _this.locale
      });
    }).join('')
    // The returned format can contain `yyyyy` which means year between 4 and 6 digits.
    // This value is supported by luxon parser but not luxon formatter.
    // To avoid conflicts, we replace it by 4 digits which is enough for most use-cases.
    .replace('yyyyy', 'yyyy');
  };
  this.getFormatHelperText = function (format) {
    return _this.expandFormat(format).replace(/(a)/g, '(a|p)m').toLocaleLowerCase();
  };
  this.isNull = function (value) {
    return value === null;
  };
  this.isValid = function (value) {
    if (DateTime.isDateTime(value)) {
      return value.isValid;
    }
    if (value === null) {
      return false;
    }
    return _this.isValid(_this.date(value));
  };
  this.format = function (value, formatKey) {
    return _this.formatByString(value, _this.formats[formatKey]);
  };
  this.formatByString = function (value, format) {
    return value.setLocale(_this.locale).toFormat(format);
  };
  this.formatNumber = function (numberToFormat) {
    return numberToFormat;
  };
  this.getDiff = function (value, comparing, unit) {
    if (typeof comparing === 'string') {
      comparing = DateTime.fromJSDate(new Date(comparing));
    }
    if (unit) {
      return Math.floor(value.diff(comparing).as(unit));
    }
    return value.diff(comparing).as('millisecond');
  };
  this.isEqual = function (value, comparing) {
    if (value === null && comparing === null) {
      return true;
    }

    // Make sure that null will not be passed to this.date
    if (value === null || comparing === null) {
      return false;
    }
    return +_this.date(value) === +_this.date(comparing);
  };
  this.isSameYear = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    return value.hasSame(comparingInValueTimezone, 'year');
  };
  this.isSameMonth = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    return value.hasSame(comparingInValueTimezone, 'month');
  };
  this.isSameDay = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    return value.hasSame(comparingInValueTimezone, 'day');
  };
  this.isSameHour = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    return value.hasSame(comparingInValueTimezone, 'hour');
  };
  this.isAfter = function (value, comparing) {
    return value > comparing;
  };
  this.isAfterYear = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    var diff = value.diff(comparingInValueTimezone.endOf('year'), 'years').toObject();
    return diff.years > 0;
  };
  this.isAfterDay = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    var diff = value.diff(comparingInValueTimezone.endOf('day'), 'days').toObject();
    return diff.days > 0;
  };
  this.isBefore = function (value, comparing) {
    return value < comparing;
  };
  this.isBeforeYear = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    var diff = value.diff(comparingInValueTimezone.startOf('year'), 'years').toObject();
    return diff.years < 0;
  };
  this.isBeforeDay = function (value, comparing) {
    var comparingInValueTimezone = _this.setTimezone(comparing, _this.getTimezone(value));
    var diff = value.diff(comparingInValueTimezone.startOf('day'), 'days').toObject();
    return diff.days < 0;
  };
  this.isWithinRange = function (value, _ref2) {
    var _ref3 = _slicedToArray(_ref2, 2),
      start = _ref3[0],
      end = _ref3[1];
    return _this.isEqual(value, start) || _this.isEqual(value, end) || _this.isAfter(value, start) && _this.isBefore(value, end);
  };
  this.startOfYear = function (value) {
    return value.startOf('year');
  };
  this.startOfMonth = function (value) {
    return value.startOf('month');
  };
  this.startOfWeek = function (value) {
    return value.startOf('week');
  };
  this.startOfDay = function (value) {
    return value.startOf('day');
  };
  this.endOfYear = function (value) {
    return value.endOf('year');
  };
  this.endOfMonth = function (value) {
    return value.endOf('month');
  };
  this.endOfWeek = function (value) {
    return value.endOf('week');
  };
  this.endOfDay = function (value) {
    return value.endOf('day');
  };
  this.addYears = function (value, amount) {
    return value.plus({
      years: amount
    });
  };
  this.addMonths = function (value, amount) {
    return value.plus({
      months: amount
    });
  };
  this.addWeeks = function (value, amount) {
    return value.plus({
      weeks: amount
    });
  };
  this.addDays = function (value, amount) {
    return value.plus({
      days: amount
    });
  };
  this.addHours = function (value, amount) {
    return value.plus({
      hours: amount
    });
  };
  this.addMinutes = function (value, amount) {
    return value.plus({
      minutes: amount
    });
  };
  this.addSeconds = function (value, amount) {
    return value.plus({
      seconds: amount
    });
  };
  this.getYear = function (value) {
    return value.get('year');
  };
  this.getMonth = function (value) {
    // See https://github.com/moment/luxon/blob/master/docs/moment.md#major-functional-differences
    return value.get('month') - 1;
  };
  this.getDate = function (value) {
    return value.get('day');
  };
  this.getHours = function (value) {
    return value.get('hour');
  };
  this.getMinutes = function (value) {
    return value.get('minute');
  };
  this.getSeconds = function (value) {
    return value.get('second');
  };
  this.getMilliseconds = function (value) {
    return value.get('millisecond');
  };
  this.setYear = function (value, year) {
    return value.set({
      year: year
    });
  };
  this.setMonth = function (value, month) {
    return value.set({
      month: month + 1
    });
  };
  this.setDate = function (value, date) {
    return value.set({
      day: date
    });
  };
  this.setHours = function (value, hours) {
    return value.set({
      hour: hours
    });
  };
  this.setMinutes = function (value, minutes) {
    return value.set({
      minute: minutes
    });
  };
  this.setSeconds = function (value, seconds) {
    return value.set({
      second: seconds
    });
  };
  this.setMilliseconds = function (value, milliseconds) {
    return value.set({
      millisecond: milliseconds
    });
  };
  this.getDaysInMonth = function (value) {
    return value.daysInMonth;
  };
  this.getNextMonth = function (value) {
    return value.plus({
      months: 1
    });
  };
  this.getPreviousMonth = function (value) {
    return value.minus({
      months: 1
    });
  };
  this.getMonthArray = function (value) {
    var firstMonth = value.startOf('year');
    var monthArray = [firstMonth];
    while (monthArray.length < 12) {
      var prevMonth = monthArray[monthArray.length - 1];
      monthArray.push(_this.addMonths(prevMonth, 1));
    }
    return monthArray;
  };
  this.mergeDateAndTime = function (dateParam, timeParam) {
    return dateParam.set({
      second: timeParam.second,
      hour: timeParam.hour,
      minute: timeParam.minute
    });
  };
  this.getWeekdays = function () {
    return Info.weekdaysFormat('narrow', {
      locale: _this.locale
    });
  };
  this.getWeekArray = function (value) {
    var cleanValue = _this.setLocaleToValue(value);
    var _cleanValue$endOf$end = cleanValue.endOf('month').endOf('week').diff(cleanValue.startOf('month').startOf('week'), 'days').toObject(),
      days = _cleanValue$endOf$end.days;
    var weeks = [];
    new Array(Math.round(days)).fill(0).map(function (_, i) {
      return i;
    }).map(function (day) {
      return cleanValue.startOf('month').startOf('week').plus({
        days: day
      });
    }).forEach(function (v, i) {
      if (i === 0 || i % 7 === 0 && i > 6) {
        weeks.push([v]);
        return;
      }
      weeks[weeks.length - 1].push(v);
    });
    return weeks;
  };
  this.getWeekNumber = function (value) {
    return value.weekNumber;
  };
  this.getYearRange = function (start, end) {
    var startDate = start.startOf('year');
    var endDate = end.endOf('year');
    var current = startDate;
    var years = [];
    while (current < endDate) {
      years.push(current);
      current = current.plus({
        year: 1
      });
    }
    return years;
  };
  this.getMeridiemText = function (ampm) {
    return Info.meridiems({
      locale: _this.locale
    }).find(function (v) {
      return v.toLowerCase() === ampm.toLowerCase();
    });
  };
  this.locale = locale || 'en-US';
  this.formats = _extends({}, defaultFormats, formats);
});