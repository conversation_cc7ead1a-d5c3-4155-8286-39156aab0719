import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
export var getHourSectionOptions = function getHourSectionOptions(_ref) {
  var now = _ref.now,
    value = _ref.value,
    utils = _ref.utils,
    ampm = _ref.ampm,
    isDisabled = _ref.isDisabled,
    resolveAriaLabel = _ref.resolveAriaLabel,
    timeStep = _ref.timeStep;
  var currentHours = value ? utils.getHours(value) : null;
  var result = [];
  var isSelected = function isSelected(hour) {
    if (currentHours === null) {
      return false;
    }
    if (ampm) {
      if (hour === 12) {
        return currentHours === 12 || currentHours === 0;
      }
      return currentHours === hour || currentHours - 12 === hour;
    }
    return currentHours === hour;
  };
  var endHour = ampm ? 11 : 23;
  for (var hour = 0; hour <= endHour; hour += timeStep) {
    var label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');
    var ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());
    label = utils.formatNumber(label);
    result.push({
      value: hour,
      label: label,
      isSelected: isSelected,
      isDisabled: isDisabled,
      ariaLabel: ariaLabel
    });
  }
  return result;
};
export var getTimeSectionOptions = function getTimeSectionOptions(_ref2) {
  var value = _ref2.value,
    utils = _ref2.utils,
    isDisabled = _ref2.isDisabled,
    timeStep = _ref2.timeStep,
    resolveLabel = _ref2.resolveLabel,
    resolveAriaLabel = _ref2.resolveAriaLabel,
    _ref2$hasValue = _ref2.hasValue,
    hasValue = _ref2$hasValue === void 0 ? true : _ref2$hasValue;
  var isSelected = function isSelected(timeValue) {
    if (value === null) {
      return false;
    }
    return hasValue && value === timeValue;
  };
  return _toConsumableArray(Array.from({
    length: Math.ceil(60 / timeStep)
  }, function (_, index) {
    var timeValue = timeStep * index;
    return {
      value: timeValue,
      label: utils.formatNumber(resolveLabel(timeValue)),
      isDisabled: isDisabled,
      isSelected: isSelected,
      ariaLabel: resolveAriaLabel(timeValue.toString())
    };
  }));
};