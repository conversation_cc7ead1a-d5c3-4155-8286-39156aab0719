"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _toPropertyKey2 = _interopRequireDefault(require("@babel/runtime/helpers/toPropertyKey"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _deepmerge = _interopRequireDefault(require("@mui/utils/deepmerge"));
var _cssVarsParser = _interopRequireDefault(require("./cssVarsParser"));
const _excluded = ["colorSchemes", "components", "defaultColorScheme"];
function prepareCssVars(theme, parserConfig) {
  // @ts-ignore - ignore components do not exist
  const {
      colorSchemes = {},
      defaultColorScheme = 'light'
    } = theme,
    otherTheme = (0, _objectWithoutPropertiesLoose2.default)(theme, _excluded);
  const {
    vars: rootVars,
    css: rootCss,
    varsWithDefaults: rootVarsWithDefaults
  } = (0, _cssVarsParser.default)(otherTheme, parserConfig);
  let themeVars = rootVarsWithDefaults;
  const colorSchemesMap = {};
  const {
      [defaultColorScheme]: light
    } = colorSchemes,
    otherColorSchemes = (0, _objectWithoutPropertiesLoose2.default)(colorSchemes, [defaultColorScheme].map(_toPropertyKey2.default));
  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {
    const {
      vars,
      css,
      varsWithDefaults
    } = (0, _cssVarsParser.default)(scheme, parserConfig);
    themeVars = (0, _deepmerge.default)(themeVars, varsWithDefaults);
    colorSchemesMap[key] = {
      css,
      vars
    };
  });
  if (light) {
    // default color scheme vars should be merged last to set as default
    const {
      css,
      vars,
      varsWithDefaults
    } = (0, _cssVarsParser.default)(light, parserConfig);
    themeVars = (0, _deepmerge.default)(themeVars, varsWithDefaults);
    colorSchemesMap[defaultColorScheme] = {
      css,
      vars
    };
  }
  const generateCssVars = colorScheme => {
    var _parserConfig$getSele2;
    if (!colorScheme) {
      var _parserConfig$getSele;
      const css = (0, _extends2.default)({}, rootCss);
      return {
        css,
        vars: rootVars,
        selector: (parserConfig == null || (_parserConfig$getSele = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele.call(parserConfig, colorScheme, css)) || ':root'
      };
    }
    const css = (0, _extends2.default)({}, colorSchemesMap[colorScheme].css);
    return {
      css,
      vars: colorSchemesMap[colorScheme].vars,
      selector: (parserConfig == null || (_parserConfig$getSele2 = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele2.call(parserConfig, colorScheme, css)) || ':root'
    };
  };
  return {
    vars: themeVars,
    generateCssVars
  };
}
var _default = exports.default = prepareCssVars;