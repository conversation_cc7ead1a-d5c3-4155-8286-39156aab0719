import * as React from 'react';
import { OverrideProps, IfEquals } from '@mui/types';
import { SxProps } from '../styleFunctionSx';
import { Theme, Breakpoint, BreakpointOverrides } from '../createTheme';
import { SystemProps } from '../Box';
type ResponsiveStyleValue<T> = T | Array<T | null> | {
    [key in Breakpoint]?: T | null;
};
export type GridDirection = 'row' | 'row-reverse' | 'column' | 'column-reverse';
export type GridSpacing = number | string;
export type GridWrap = 'nowrap' | 'wrap' | 'wrap-reverse';
export type GridSize = 'auto' | number;
export interface GridDefaultBreakpoints {
    /**
     * If a number, it sets the number of columns the grid item uses.
     * It can't be greater than the total number of columns of the container (12 by default).
     * If 'auto', the grid item's width matches its content.
     * If false, the prop is ignored.
     * If true, the grid item's width grows to use the space available in the grid container.
     * The value is applied for the `lg` breakpoint and wider screens if not overridden.
     * @default false
     */
    lg?: boolean | GridSize;
    /**
     * If a number, it sets the margin-left equals to the number of columns the grid item uses.
     * If 'auto', the grid item push itself to the right-end of the container.
     * The value is applied for the `lg` breakpoint and wider screens if not overridden.
     */
    lgOffset?: GridSize;
    /**
     * If a number, it sets the number of columns the grid item uses.
     * It can't be greater than the total number of columns of the container (12 by default).
     * If 'auto', the grid item's width matches its content.
     * If false, the prop is ignored.
     * If true, the grid item's width grows to use the space available in the grid container.
     * The value is applied for the `md` breakpoint and wider screens if not overridden.
     * @default false
     */
    md?: boolean | GridSize;
    /**
     * If a number, it sets the margin-left equals to the number of columns the grid item uses.
     * If 'auto', the grid item push itself to the right-end of the container.
     * The value is applied for the `md` breakpoint and wider screens if not overridden.
     */
    mdOffset?: GridSize;
    /**
     * If a number, it sets the number of columns the grid item uses.
     * It can't be greater than the total number of columns of the container (12 by default).
     * If 'auto', the grid item's width matches its content.
     * If false, the prop is ignored.
     * If true, the grid item's width grows to use the space available in the grid container.
     * The value is applied for the `sm` breakpoint and wider screens if not overridden.
     * @default false
     */
    sm?: boolean | GridSize;
    /**
     * If a number, it sets the margin-left equals to the number of columns the grid item uses.
     * If 'auto', the grid item push itself to the right-end of the container.
     * The value is applied for the `sm` breakpoint and wider screens if not overridden.
     */
    smOffset?: GridSize;
    /**
     * If a number, it sets the number of columns the grid item uses.
     * It can't be greater than the total number of columns of the container (12 by default).
     * If 'auto', the grid item's width matches its content.
     * If false, the prop is ignored.
     * If true, the grid item's width grows to use the space available in the grid container.
     * The value is applied for the `xl` breakpoint and wider screens if not overridden.
     * @default false
     */
    xl?: boolean | GridSize;
    /**
     * If a number, it sets the margin-left equals to the number of columns the grid item uses.
     * If 'auto', the grid item push itself to the right-end of the container.
     * The value is applied for the `xl` breakpoint and wider screens if not overridden.
     */
    xlOffset?: GridSize;
    /**
     * If a number, it sets the number of columns the grid item uses.
     * It can't be greater than the total number of columns of the container (12 by default).
     * If 'auto', the grid item's width matches its content.
     * If false, the prop is ignored.
     * If true, the grid item's width grows to use the space available in the grid container.
     * The value is applied for all the screen sizes with the lowest priority.
     * @default false
     */
    xs?: boolean | GridSize;
    /**
     * If a number, it sets the margin-left equals to the number of columns the grid item uses.
     * If 'auto', the grid item push itself to the right-end of the container.
     * The value is applied for the `xs` breakpoint and wider screens if not overridden.
     */
    xsOffset?: GridSize;
}
type CustomBreakpoints = Partial<Record<Breakpoint, boolean | GridSize> & Record<`${Breakpoint}Offset`, GridSize>>;
interface BreakpointOverridesEmpty {
}
type Breakpoints = IfEquals<BreakpointOverrides, BreakpointOverridesEmpty, GridDefaultBreakpoints, CustomBreakpoints>;
export interface GridBaseProps extends Breakpoints {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * The number of columns.
     * @default 12
     */
    columns?: ResponsiveStyleValue<number>;
    /**
     * Defines the horizontal space between the type `item` components.
     * It overrides the value of the `spacing` prop.
     */
    columnSpacing?: ResponsiveStyleValue<GridSpacing>;
    /**
     * If `true`, the component will have the flex *container* behavior.
     * You should be wrapping *items* with a *container*.
     * @default false
     */
    container?: boolean;
    /**
     * Defines the `flex-direction` style property.
     * It is applied for all screen sizes.
     * @default 'row'
     */
    direction?: ResponsiveStyleValue<GridDirection>;
    /**
     * If `true`, the negative margin and padding are apply only to the top and left sides of the grid.
     */
    disableEqualOverflow?: boolean;
    /**
     * @internal
     * The level of the grid starts from `0`
     * and increases when the grid nests inside another grid regardless of container or item.
     *
     * ```js
     * <Grid> // level 0
     *   <Grid> // level 1
     *     <Grid> // level 2
     *   <Grid> // level 1
     * ```
     *
     * Only consecutive grid is considered nesting.
     * A grid container will start at `0` if there are non-Grid element above it.
     *
     * ```js
     * <Grid> // level 0
     *   <div>
     *     <Grid> // level 0
     *       <Grid> // level 1
     * ```
     */
    unstable_level?: number;
    /**
     * Defines the vertical space between the type `item` components.
     * It overrides the value of the `spacing` prop.
     */
    rowSpacing?: ResponsiveStyleValue<GridSpacing>;
    /**
     * Defines the space between the type `item` components.
     * It can only be used on a type `container` component.
     * @default 0
     */
    spacing?: ResponsiveStyleValue<GridSpacing> | undefined;
    /**
     * Defines the `flex-wrap` style property.
     * It's applied for all screen sizes.
     * @default 'wrap'
     */
    wrap?: GridWrap;
}
export interface GridOwnerState extends GridBaseProps {
    unstable_level: number;
    gridSize: Partial<Record<Breakpoint, GridSize | boolean>>;
    gridOffset: Partial<Record<Breakpoint, GridSize>>;
}
export interface GridTypeMap<AdditionalProps = {}, DefaultComponent extends React.ElementType = 'div'> {
    props: AdditionalProps & GridBaseProps & {
        sx?: SxProps<Theme>;
    } & SystemProps<Theme>;
    defaultComponent: DefaultComponent;
}
export type GridProps<RootComponent extends React.ElementType = GridTypeMap['defaultComponent'], AdditionalProps = {
    component?: React.ElementType;
}> = OverrideProps<GridTypeMap<AdditionalProps, RootComponent>, RootComponent>;
export {};
