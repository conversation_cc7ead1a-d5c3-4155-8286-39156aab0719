import * as React from 'react';
import { FocusTrapProps } from './FocusTrap.types';
/**
 * Utility component that locks focus inside the component.
 *
 * Demos:
 *
 * - [Focus Trap](https://mui.com/base-ui/react-focus-trap/)
 *
 * API:
 *
 * - [FocusTrap API](https://mui.com/base-ui/react-focus-trap/components-api/#focus-trap)
 */
declare function FocusTrap(props: FocusTrapProps): React.JSX.Element;
declare namespace FocusTrap {
    var propTypes: any;
}
export { FocusTrap };
