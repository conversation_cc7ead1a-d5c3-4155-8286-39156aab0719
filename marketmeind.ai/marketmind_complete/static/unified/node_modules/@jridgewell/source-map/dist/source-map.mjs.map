{"version": 3, "file": "source-map.mjs", "sources": ["../../src/source-map.ts"], "sourcesContent": ["import {\n  AnyMap,\n  originalPositionFor,\n  generatedPositionFor,\n  allGeneratedPositionsFor,\n  eachMapping,\n  encodedMappings,\n  sourceContentFor,\n} from '@jridgewell/trace-mapping';\nimport {\n  GenMapping,\n  maybeAddMapping,\n  toDecodedMap,\n  toEncodedMap,\n  setSourceContent,\n  fromMap,\n} from '@jridgewell/gen-mapping';\n\nimport type {\n  TraceMap,\n  SourceMapInput,\n  SectionedSourceMapInput,\n  DecodedSourceMap,\n} from '@jridgewell/trace-mapping';\nexport type { TraceMap, SourceMapInput, SectionedSourceMapInput, DecodedSourceMap };\n\nimport type { Mapping, EncodedSourceMap } from '@jridgewell/gen-mapping';\nexport type { Mapping, EncodedSourceMap };\n\nexport class SourceMapConsumer {\n  private declare _map: TraceMap;\n  declare file: TraceMap['file'];\n  declare names: TraceMap['names'];\n  declare sourceRoot: TraceMap['sourceRoot'];\n  declare sources: TraceMap['sources'];\n  declare sourcesContent: TraceMap['sourcesContent'];\n  declare version: TraceMap['version'];\n\n  constructor(map: ConstructorParameters<typeof AnyMap>[0], mapUrl: Parameters<typeof AnyMap>[1]) {\n    const trace = (this._map = new AnyMap(map, mapUrl));\n\n    this.file = trace.file;\n    this.names = trace.names;\n    this.sourceRoot = trace.sourceRoot;\n    this.sources = trace.resolvedSources;\n    this.sourcesContent = trace.sourcesContent;\n    this.version = trace.version;\n  }\n\n  static fromSourceMap(map: SourceMapGenerator, mapUrl: Parameters<typeof AnyMap>[1]) {\n    // This is more performant if we receive\n    // a @jridgewell/source-map SourceMapGenerator\n    if (map.toDecodedMap) {\n      return new SourceMapConsumer(map.toDecodedMap() as SectionedSourceMapInput, mapUrl);\n    }\n\n    // This is a fallback for `source-map` and `source-map-js`\n    return new SourceMapConsumer(map.toJSON() as SectionedSourceMapInput, mapUrl);\n  }\n\n  get mappings(): string {\n    return encodedMappings(this._map);\n  }\n\n  originalPositionFor(\n    needle: Parameters<typeof originalPositionFor>[1],\n  ): ReturnType<typeof originalPositionFor> {\n    return originalPositionFor(this._map, needle);\n  }\n\n  generatedPositionFor(\n    originalPosition: Parameters<typeof generatedPositionFor>[1],\n  ): ReturnType<typeof generatedPositionFor> {\n    return generatedPositionFor(this._map, originalPosition);\n  }\n\n  allGeneratedPositionsFor(\n    originalPosition: Parameters<typeof generatedPositionFor>[1],\n  ): ReturnType<typeof generatedPositionFor>[] {\n    return allGeneratedPositionsFor(this._map, originalPosition);\n  }\n\n  hasContentsOfAllSources(): boolean {\n    if (!this.sourcesContent || this.sourcesContent.length !== this.sources.length) {\n      return false;\n    }\n\n    for (const content of this.sourcesContent) {\n      if (content == null) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  sourceContentFor(source: string, nullOnMissing?: boolean): string | null {\n    const sourceContent = sourceContentFor(this._map, source);\n    if (sourceContent != null) {\n      return sourceContent;\n    }\n\n    if (nullOnMissing) {\n      return null;\n    }\n    throw new Error(`\"${source}\" is not in the SourceMap.`);\n  }\n\n  eachMapping(\n    callback: Parameters<typeof eachMapping>[1],\n    context?: any /*, order?: number*/,\n  ): void {\n    // order is ignored as @jridgewell/trace-map doesn't implement it\n    eachMapping(this._map, context ? callback.bind(context) : callback);\n  }\n\n  destroy() {\n    // noop.\n  }\n}\n\nexport class SourceMapGenerator {\n  private declare _map: GenMapping;\n\n  constructor(opts: ConstructorParameters<typeof GenMapping>[0] | GenMapping) {\n    // TODO :: should this be duck-typed ?\n    this._map = opts instanceof GenMapping ? opts : new GenMapping(opts);\n  }\n\n  static fromSourceMap(consumer: SourceMapConsumer) {\n    return new SourceMapGenerator(fromMap(consumer));\n  }\n\n  addMapping(mapping: Parameters<typeof maybeAddMapping>[1]): ReturnType<typeof maybeAddMapping> {\n    maybeAddMapping(this._map, mapping);\n  }\n\n  setSourceContent(\n    source: Parameters<typeof setSourceContent>[1],\n    content: Parameters<typeof setSourceContent>[2],\n  ): ReturnType<typeof setSourceContent> {\n    setSourceContent(this._map, source, content);\n  }\n\n  toJSON(): ReturnType<typeof toEncodedMap> {\n    return toEncodedMap(this._map);\n  }\n\n  toString(): string {\n    return JSON.stringify(this.toJSON());\n  }\n\n  toDecodedMap(): ReturnType<typeof toDecodedMap> {\n    return toDecodedMap(this._map);\n  }\n}\n"], "names": [], "mappings": ";;;MA6Ba,iBAAiB;IAS5B,YAAY,GAA4C,EAAE,MAAoC;QAC5F,MAAM,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAEpD,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,eAAe,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;KAC9B;IAED,OAAO,aAAa,CAAC,GAAuB,EAAE,MAAoC;;;QAGhF,IAAI,GAAG,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAA6B,EAAE,MAAM,CAAC,CAAC;SACrF;;QAGD,OAAO,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAA6B,EAAE,MAAM,CAAC,CAAC;KAC/E;IAED,IAAI,QAAQ;QACV,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnC;IAED,mBAAmB,CACjB,MAAiD;QAEjD,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC/C;IAED,oBAAoB,CAClB,gBAA4D;QAE5D,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;KAC1D;IAED,wBAAwB,CACtB,gBAA4D;QAE5D,OAAO,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;KAC9D;IAED,uBAAuB;QACrB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC9E,OAAO,KAAK,CAAC;SACd;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;YACzC,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;KACb;IAED,gBAAgB,CAAC,MAAc,EAAE,aAAuB;QACtD,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,aAAa,IAAI,IAAI,EAAE;YACzB,OAAO,aAAa,CAAC;SACtB;QAED,IAAI,aAAa,EAAE;YACjB,OAAO,IAAI,CAAC;SACb;QACD,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM,4BAA4B,CAAC,CAAC;KACzD;IAED,WAAW,CACT,QAA2C,EAC3C,OAAa;;QAGb,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAC;KACrE;IAED,OAAO;;KAEN;CACF;MAEY,kBAAkB;IAG7B,YAAY,IAA8D;;QAExE,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,UAAU,GAAG,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;KACtE;IAED,OAAO,aAAa,CAAC,QAA2B;QAC9C,OAAO,IAAI,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;KAClD;IAED,UAAU,CAAC,OAA8C;QACvD,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACrC;IAED,gBAAgB,CACd,MAA8C,EAC9C,OAA+C;QAE/C,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KAC9C;IAED,MAAM;QACJ,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;KACtC;IAED,YAAY;QACV,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAChC;;;;;"}