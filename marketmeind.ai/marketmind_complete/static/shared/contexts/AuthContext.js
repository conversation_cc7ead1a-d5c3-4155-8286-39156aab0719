import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// أنواع الحسابات والصلاحيات
export const ACCOUNT_TYPES = {
  PERSONAL: 'personal',
  BUSINESS_USER: 'business_user',
  BUSINESS_OWNER: 'business_owner',
  ADMIN: 'admin',
  OWNER: 'owner'
};

export const PERMISSIONS = {
  // صلاحيات المستخدمين الشخصيين
  VIEW_OWN_DATA: 'view_own_data',
  EDIT_PROFILE: 'edit_profile',
  USE_AI_FEATURES: 'use_ai_features',
  
  // صلاحيات مستخدمي الشركات
  VIEW_COMPANY_DATA: 'view_company_data',
  CREATE_CAMPAIGNS: 'create_campaigns',
  MANAGE_CONTENT: 'manage_content',
  VIEW_ANALYTICS: 'view_analytics',
  
  // صلاحيات مالكي الشركات
  MANAGE_COMPANY: 'manage_company',
  MANAGE_TEAM: 'manage_team',
  VIEW_BILLING: 'view_billing',
  MANAGE_SUBSCRIPTION: 'manage_subscription',
  
  // صلاحيات المدراء
  MANAGE_USERS: 'manage_users',
  MANAGE_COMPANIES: 'manage_companies',
  VIEW_SYSTEM_ANALYTICS: 'view_system_analytics',
  MANAGE_SYSTEM: 'manage_system',
  
  // صلاحيات المالك
  FULL_ACCESS: 'full_access'
};

// تحديد الصلاحيات لكل نوع حساب
const getPermissionsByAccountType = (accountType) => {
  switch (accountType) {
    case ACCOUNT_TYPES.PERSONAL:
      return [
        PERMISSIONS.VIEW_OWN_DATA,
        PERMISSIONS.EDIT_PROFILE,
        PERMISSIONS.USE_AI_FEATURES
      ];
    
    case ACCOUNT_TYPES.BUSINESS_USER:
      return [
        PERMISSIONS.VIEW_OWN_DATA,
        PERMISSIONS.EDIT_PROFILE,
        PERMISSIONS.VIEW_COMPANY_DATA,
        PERMISSIONS.CREATE_CAMPAIGNS,
        PERMISSIONS.MANAGE_CONTENT,
        PERMISSIONS.VIEW_ANALYTICS
      ];
    
    case ACCOUNT_TYPES.BUSINESS_OWNER:
      return [
        PERMISSIONS.VIEW_OWN_DATA,
        PERMISSIONS.EDIT_PROFILE,
        PERMISSIONS.VIEW_COMPANY_DATA,
        PERMISSIONS.CREATE_CAMPAIGNS,
        PERMISSIONS.MANAGE_CONTENT,
        PERMISSIONS.VIEW_ANALYTICS,
        PERMISSIONS.MANAGE_COMPANY,
        PERMISSIONS.MANAGE_TEAM,
        PERMISSIONS.VIEW_BILLING,
        PERMISSIONS.MANAGE_SUBSCRIPTION
      ];
    
    case ACCOUNT_TYPES.ADMIN:
      return [
        PERMISSIONS.VIEW_OWN_DATA,
        PERMISSIONS.EDIT_PROFILE,
        PERMISSIONS.MANAGE_USERS,
        PERMISSIONS.MANAGE_COMPANIES,
        PERMISSIONS.VIEW_SYSTEM_ANALYTICS,
        PERMISSIONS.MANAGE_SYSTEM
      ];
    
    case ACCOUNT_TYPES.OWNER:
      return [PERMISSIONS.FULL_ACCESS];
    
    default:
      return [];
  }
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [loading, setLoading] = useState(true);

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    // التحقق من وجود token محفوظ
    const savedToken = localStorage.getItem('access_token');
    const savedUser = localStorage.getItem('user_data');
    
    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser);
        setAccessToken(savedToken);
        setCurrentUser({
          ...user,
          permissions: getPermissionsByAccountType(user.account_type)
        });
        
        // إعداد axios header
        axios.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`;
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_data');
      }
    }
    
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    try {
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email,
        password
      });

      const { access_token, user } = response.data;
      
      // إضافة الصلاحيات للمستخدم
      const userWithPermissions = {
        ...user,
        permissions: getPermissionsByAccountType(user.account_type)
      };

      // حفظ البيانات
      setAccessToken(access_token);
      setCurrentUser(userWithPermissions);
      
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('user_data', JSON.stringify(userWithPermissions));
      
      // إعداد axios header
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      return { success: true, user: userWithPermissions };
    } catch (error) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.message || 'فشل في تسجيل الدخول');
    }
  };

  const register = async (userData) => {
    try {
      const response = await axios.post(`${API_BASE}/auth/register`, userData);
      const { access_token, user } = response.data;
      
      const userWithPermissions = {
        ...user,
        permissions: getPermissionsByAccountType(user.account_type)
      };

      setAccessToken(access_token);
      setCurrentUser(userWithPermissions);
      
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('user_data', JSON.stringify(userWithPermissions));
      
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      return { success: true, user: userWithPermissions };
    } catch (error) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.message || 'فشل في إنشاء الحساب');
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setAccessToken(null);
    
    localStorage.removeItem('access_token');
    localStorage.removeItem('user_data');
    
    delete axios.defaults.headers.common['Authorization'];
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await axios.put(`${API_BASE}/auth/profile`, profileData);
      const updatedUser = {
        ...response.data.user,
        permissions: getPermissionsByAccountType(response.data.user.account_type)
      };
      
      setCurrentUser(updatedUser);
      localStorage.setItem('user_data', JSON.stringify(updatedUser));
      
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Profile update error:', error);
      throw new Error(error.response?.data?.message || 'فشل في تحديث الملف الشخصي');
    }
  };

  const changePassword = async (currentPassword, newPassword) => {
    try {
      await axios.post(`${API_BASE}/auth/change-password`, {
        current_password: currentPassword,
        new_password: newPassword
      });
      
      return { success: true };
    } catch (error) {
      console.error('Password change error:', error);
      throw new Error(error.response?.data?.message || 'فشل في تغيير كلمة المرور');
    }
  };

  const resetPassword = async (email) => {
    try {
      await axios.post(`${API_BASE}/auth/reset-password`, { email });
      return { success: true };
    } catch (error) {
      console.error('Password reset error:', error);
      throw new Error(error.response?.data?.message || 'فشل في إرسال رابط استعادة كلمة المرور');
    }
  };

  const refreshToken = async () => {
    try {
      const response = await axios.post(`${API_BASE}/auth/refresh`);
      const { access_token } = response.data;
      
      setAccessToken(access_token);
      localStorage.setItem('access_token', access_token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
      
      return access_token;
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
      throw error;
    }
  };

  // التحقق من الصلاحيات
  const hasPermission = (permission) => {
    if (!currentUser) return false;
    if (currentUser.permissions.includes(PERMISSIONS.FULL_ACCESS)) return true;
    return currentUser.permissions.includes(permission);
  };

  // التحقق من إمكانية الوصول لقسم معين
  const canAccess = (section) => {
    if (!currentUser) return false;
    
    switch (section) {
      case 'personal':
        return currentUser.account_type === ACCOUNT_TYPES.PERSONAL;
      case 'business':
        return [ACCOUNT_TYPES.BUSINESS_USER, ACCOUNT_TYPES.BUSINESS_OWNER].includes(currentUser.account_type);
      case 'admin':
        return currentUser.account_type === ACCOUNT_TYPES.ADMIN;
      case 'owner':
        return currentUser.account_type === ACCOUNT_TYPES.OWNER;
      default:
        return false;
    }
  };

  // إعداد axios interceptor للتعامل مع انتهاء صلاحية التوكن
  useEffect(() => {
    const interceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && accessToken) {
          try {
            await refreshToken();
            // إعادة المحاولة مع التوكن الجديد
            return axios.request(error.config);
          } catch (refreshError) {
            logout();
            window.location.href = '/auth/login';
          }
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, [accessToken]);

  const value = {
    currentUser,
    accessToken,
    loading,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    resetPassword,
    refreshToken,
    hasPermission,
    canAccess,
    ACCOUNT_TYPES,
    PERMISSIONS
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
