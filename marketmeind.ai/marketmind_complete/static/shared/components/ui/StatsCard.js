import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';

const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  trendValue,
  progress,
  progressColor,
  chip,
  onClick,
  loading = false,
  ...props
}) => {
  const formatValue = (val) => {
    if (typeof val === 'number') {
      return val.toLocaleString();
    }
    return val;
  };

  const getTrendColor = (trendVal) => {
    if (trendVal > 0) return 'success.main';
    if (trendVal < 0) return 'error.main';
    return 'text.secondary';
  };

  const getTrendIcon = (trendVal) => {
    if (trendVal > 0) return <TrendingUpIcon fontSize="small" />;
    if (trendVal < 0) return <TrendingDownIcon fontSize="small" />;
    return null;
  };

  return (
    <Card 
      sx={{ 
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: 3
        } : {}
      }}
      onClick={onClick}
      {...props}
    >
      <CardContent>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box flex={1}>
            <Typography 
              color="text.secondary" 
              gutterBottom 
              variant="body2"
              sx={{ fontSize: '0.875rem' }}
            >
              {title}
            </Typography>
            
            <Typography 
              variant="h4" 
              component="div"
              sx={{ 
                fontWeight: 700,
                mb: 1,
                color: loading ? 'text.disabled' : 'text.primary'
              }}
            >
              {loading ? '...' : formatValue(value)}
            </Typography>
            
            {subtitle && (
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ mb: 1 }}
              >
                {subtitle}
              </Typography>
            )}

            {/* عرض الاتجاه */}
            {trend !== undefined && trendValue !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                <Box 
                  display="flex" 
                  alignItems="center" 
                  sx={{ color: getTrendColor(trendValue) }}
                >
                  {getTrendIcon(trendValue)}
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      ml: 0.5,
                      fontWeight: 600,
                      color: getTrendColor(trendValue)
                    }}
                  >
                    {Math.abs(trendValue)}%
                  </Typography>
                </Box>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  sx={{ ml: 1 }}
                >
                  {trend}
                </Typography>
              </Box>
            )}

            {/* شريط التقدم */}
            {progress !== undefined && (
              <Box mt={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                  <Typography variant="caption" color="text.secondary">
                    التقدم
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {progress}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={progress} 
                  color={progressColor || color}
                  sx={{ 
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'grey.200'
                  }}
                />
              </Box>
            )}

            {/* رقاقة إضافية */}
            {chip && (
              <Box mt={1}>
                <Chip 
                  label={chip.label}
                  color={chip.color || 'default'}
                  size="small"
                  variant={chip.variant || 'filled'}
                />
              </Box>
            )}
          </Box>

          {/* الأيقونة */}
          {icon && (
            <Avatar 
              sx={{ 
                bgcolor: `${color}.main`,
                width: 48,
                height: 48,
                ml: 2
              }}
            >
              {icon}
            </Avatar>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default StatsCard;
