import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  TextField,
  Box,
  Typography,
  IconButton,
  Chip,
  Avatar,
  Checkbox,
  Toolbar,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';

const DataTable = ({
  data = [],
  columns = [],
  title,
  searchable = true,
  filterable = true,
  selectable = false,
  exportable = false,
  actions = [],
  onRowClick,
  onSelectionChange,
  onExport,
  loading = false,
  emptyMessage = 'لا توجد بيانات للعرض',
  ...props
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterColumn, setFilterColumn] = useState('');
  const [filterValue, setFilterValue] = useState('');
  const [selected, setSelected] = useState([]);

  // فلترة البيانات
  const filteredData = data.filter(row => {
    const searchMatch = !searchTerm || 
      Object.values(row).some(value => 
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const filterMatch = !filterColumn || !filterValue ||
      String(row[filterColumn]).toLowerCase().includes(filterValue.toLowerCase());
    
    return searchMatch && filterMatch;
  });

  // البيانات المعروضة في الصفحة الحالية
  const paginatedData = filteredData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      const newSelected = paginatedData.map(row => row.id);
      setSelected(newSelected);
      onSelectionChange?.(newSelected);
    } else {
      setSelected([]);
      onSelectionChange?.([]);
    }
  };

  const handleSelectRow = (id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1),
      );
    }

    setSelected(newSelected);
    onSelectionChange?.(newSelected);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const renderCellContent = (value, column) => {
    if (column.render) {
      return column.render(value);
    }

    if (column.type === 'avatar') {
      return (
        <Avatar sx={{ width: 32, height: 32 }}>
          {typeof value === 'string' ? value.charAt(0) : value}
        </Avatar>
      );
    }

    if (column.type === 'chip') {
      return (
        <Chip 
          label={value}
          color={column.chipColor?.(value) || 'default'}
          size="small"
        />
      );
    }

    if (column.type === 'currency') {
      return `${Number(value).toLocaleString()} ر.س`;
    }

    if (column.type === 'number') {
      return Number(value).toLocaleString();
    }

    if (column.type === 'date') {
      return new Date(value).toLocaleDateString('ar-SA');
    }

    return value;
  };

  const getFilterableColumns = () => {
    return columns.filter(col => col.filterable !== false);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {/* شريط الأدوات */}
      <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 } }}>
        <Box sx={{ flex: '1 1 100%' }}>
          {title && (
            <Typography variant="h6" component="div">
              {title}
            </Typography>
          )}
        </Box>

        {/* أدوات البحث والفلترة */}
        <Box display="flex" gap={2} alignItems="center">
          {searchable && (
            <TextField
              size="small"
              placeholder="البحث..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 200 }}
            />
          )}

          {filterable && getFilterableColumns().length > 0 && (
            <>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>العمود</InputLabel>
                <Select
                  value={filterColumn}
                  label="العمود"
                  onChange={(e) => setFilterColumn(e.target.value)}
                >
                  <MenuItem value="">
                    <em>الكل</em>
                  </MenuItem>
                  {getFilterableColumns().map((column) => (
                    <MenuItem key={column.field} value={column.field}>
                      {column.headerName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {filterColumn && (
                <TextField
                  size="small"
                  placeholder="القيمة..."
                  value={filterValue}
                  onChange={(e) => setFilterValue(e.target.value)}
                  sx={{ minWidth: 120 }}
                />
              )}
            </>
          )}

          {exportable && (
            <Tooltip title="تصدير">
              <IconButton onClick={onExport}>
                <ExportIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Toolbar>

      {/* الجدول */}
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < paginatedData.length}
                    checked={paginatedData.length > 0 && selected.length === paginatedData.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.headerName}
                </TableCell>
              ))}
              {actions.length > 0 && (
                <TableCell align="center">الإجراءات</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)}>
                  <Box display="flex" justifyContent="center" p={3}>
                    <Typography>جاري التحميل...</Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)}>
                  <Box display="flex" justifyContent="center" p={3}>
                    <Typography color="text.secondary">{emptyMessage}</Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => {
                const isItemSelected = isSelected(row.id);
                return (
                  <TableRow
                    hover
                    onClick={() => onRowClick?.(row)}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row.id || index}
                    selected={isItemSelected}
                    sx={{ cursor: onRowClick ? 'pointer' : 'default' }}
                  >
                    {selectable && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={isItemSelected}
                          onChange={() => handleSelectRow(row.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </TableCell>
                    )}
                    {columns.map((column) => (
                      <TableCell key={column.field} align={column.align || 'left'}>
                        {renderCellContent(row[column.field], column)}
                      </TableCell>
                    ))}
                    {actions.length > 0 && (
                      <TableCell align="center">
                        <Box display="flex" justifyContent="center" gap={0.5}>
                          {actions.map((action, actionIndex) => (
                            <Tooltip key={actionIndex} title={action.tooltip || action.label}>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  action.onClick(row);
                                }}
                                color={action.color || 'default'}
                              >
                                {action.icon}
                              </IconButton>
                            </Tooltip>
                          ))}
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* ترقيم الصفحات */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={filteredData.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="عدد الصفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => 
          `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
        }
      />
    </Paper>
  );
};

export default DataTable;
