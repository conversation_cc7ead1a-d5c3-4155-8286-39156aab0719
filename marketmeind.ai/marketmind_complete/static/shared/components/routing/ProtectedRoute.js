import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';

const ProtectedRoute = ({ 
  children, 
  requiredAccountTypes = [], 
  requiredPermissions = [],
  fallbackPath = '/auth/login',
  loadingComponent = null
}) => {
  const { currentUser, loading } = useAuth();
  const location = useLocation();

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (loading) {
    return loadingComponent || (
      <Box 
        display="flex" 
        flexDirection="column"
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          جاري التحقق من الصلاحيات...
        </Typography>
      </Box>
    );
  }

  // إذا لم يكن المستخدم مسجل دخول
  if (!currentUser) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // التحقق من نوع الحساب المطلوب
  if (requiredAccountTypes.length > 0) {
    if (!requiredAccountTypes.includes(currentUser.account_type)) {
      // توجيه المستخدم إلى الواجهة المناسبة لنوع حسابه
      const redirectPath = getRedirectPathByAccountType(currentUser.account_type);
      return <Navigate to={redirectPath} replace />;
    }
  }

  // التحقق من الصلاحيات المطلوبة
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      currentUser.permissions?.includes(permission) || 
      currentUser.permissions?.includes('full_access')
    );

    if (!hasAllPermissions) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return children;
};

// تحديد المسار المناسب حسب نوع الحساب
const getRedirectPathByAccountType = (accountType) => {
  switch (accountType) {
    case 'personal':
      return '/dashboard/personal';
    case 'business_user':
    case 'business_owner':
      return '/dashboard/business';
    case 'admin':
      return '/dashboard/admin';
    case 'owner':
      return '/dashboard/owner';
    default:
      return '/auth/login';
  }
};

export default ProtectedRoute;
