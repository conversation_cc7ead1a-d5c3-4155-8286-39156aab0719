import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [accessToken, setAccessToken] = useState(null);

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    // التحقق من وجود token محفوظ
    const savedToken = localStorage.getItem('adminAccessToken');
    const savedUser = localStorage.getItem('adminCurrentUser');
    
    if (savedToken && savedUser) {
      try {
        const user = JSON.parse(savedUser);
        if (user.account_type === 'admin') {
          setAccessToken(savedToken);
          setCurrentUser(user);
        } else {
          // إذا لم يكن المستخدم مدير، قم بتسجيل الخروج
          logout();
        }
      } catch (error) {
        console.error('Error parsing saved user:', error);
        logout();
      }
    }
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (data.success && data.user.account_type === 'admin') {
        setCurrentUser(data.user);
        setAccessToken(data.access_token);
        localStorage.setItem('adminAccessToken', data.access_token);
        localStorage.setItem('adminCurrentUser', JSON.stringify(data.user));
        return { success: true };
      } else if (data.success && data.user.account_type !== 'admin') {
        return { 
          success: false, 
          message: 'ليس لديك صلاحيات الوصول لوحة تحكم المدير' 
        };
      } else {
        return { 
          success: false, 
          message: data.message || 'خطأ في تسجيل الدخول' 
        };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { 
        success: false, 
        message: 'خطأ في الاتصال بالخادم' 
      };
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setAccessToken(null);
    localStorage.removeItem('adminAccessToken');
    localStorage.removeItem('adminCurrentUser');
  };

  const checkAuth = async () => {
    if (!accessToken) return false;

    try {
      const response = await fetch(`${API_BASE}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user.account_type === 'admin') {
          setCurrentUser(data.user);
          return true;
        }
      }
      
      logout();
      return false;
    } catch (error) {
      console.error('Auth check error:', error);
      logout();
      return false;
    }
  };

  const value = {
    currentUser,
    accessToken,
    loading,
    login,
    logout,
    checkAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
