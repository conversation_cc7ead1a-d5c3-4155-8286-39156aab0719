import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Avatar,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  LinearProgress
} from '@mui/material';
import {
  CreditCard as CreditCardIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const SubscriptionManagement = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState([]);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlan, setFilterPlan] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  useEffect(() => {
    filterSubscriptions();
  }, [subscriptions, searchTerm, filterPlan, filterStatus]);

  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات الاشتراكات
      const mockSubscriptions = [
        {
          id: 1,
          customerName: 'أحمد محمد',
          customerEmail: '<EMAIL>',
          company: 'شركة التقنية المتقدمة',
          plan: 'الباقة المؤسسية',
          price: 500,
          status: 'active',
          startDate: '2024-01-15',
          endDate: '2025-01-15',
          nextBilling: '2024-07-15',
          paymentMethod: 'بطاقة ائتمان',
          totalPaid: 3000,
          invoicesCount: 6,
          autoRenewal: true
        },
        {
          id: 2,
          customerName: 'فاطمة أحمد',
          customerEmail: '<EMAIL>',
          company: null,
          plan: 'الباقة الشخصية',
          price: 30,
          status: 'active',
          startDate: '2024-03-20',
          endDate: '2025-03-20',
          nextBilling: '2024-07-20',
          paymentMethod: 'تحويل بنكي',
          totalPaid: 120,
          invoicesCount: 4,
          autoRenewal: true
        },
        {
          id: 3,
          customerName: 'محمد علي',
          customerEmail: '<EMAIL>',
          company: 'مؤسسة الابتكار',
          plan: 'باقة الأعمال',
          price: 100,
          status: 'expired',
          startDate: '2023-05-10',
          endDate: '2024-05-10',
          nextBilling: null,
          paymentMethod: 'بطاقة ائتمان',
          totalPaid: 1200,
          invoicesCount: 12,
          autoRenewal: false
        },
        {
          id: 4,
          customerName: 'سارة خالد',
          customerEmail: '<EMAIL>',
          company: null,
          plan: 'الباقة الشخصية',
          price: 30,
          status: 'pending',
          startDate: '2024-06-05',
          endDate: '2025-06-05',
          nextBilling: '2024-07-05',
          paymentMethod: 'محفظة رقمية',
          totalPaid: 30,
          invoicesCount: 1,
          autoRenewal: true
        },
        {
          id: 5,
          customerName: 'عبدالله حسن',
          customerEmail: '<EMAIL>',
          company: 'مجموعة الريادة',
          plan: 'الباقة المؤسسية',
          price: 500,
          status: 'cancelled',
          startDate: '2023-11-12',
          endDate: '2024-11-12',
          nextBilling: null,
          paymentMethod: 'تحويل بنكي',
          totalPaid: 4000,
          invoicesCount: 8,
          autoRenewal: false
        }
      ];

      setSubscriptions(mockSubscriptions);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterSubscriptions = () => {
    let filtered = subscriptions;

    if (searchTerm) {
      filtered = filtered.filter(sub =>
        sub.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (sub.company && sub.company.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (filterPlan !== 'all') {
      filtered = filtered.filter(sub => sub.plan === filterPlan);
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(sub => sub.status === filterStatus);
    }

    setFilteredSubscriptions(filtered);
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const openDialog = (type, subscription = null) => {
    setDialogType(type);
    setSelectedSubscription(subscription);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedSubscription(null);
    setDialogType('');
  };

  const getPlanColor = (plan) => {
    switch (plan) {
      case 'الباقة المجانية': return 'default';
      case 'الباقة الشخصية': return 'primary';
      case 'باقة الأعمال': return 'secondary';
      case 'الباقة المؤسسية': return 'error';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'expired': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'pending': return 'في الانتظار';
      case 'expired': return 'منتهي الصلاحية';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <CheckCircleIcon color="success" />;
      case 'pending': return <WarningIcon color="warning" />;
      case 'expired': return <CancelIcon color="error" />;
      case 'cancelled': return <CancelIcon color="disabled" />;
      default: return <CheckCircleIcon />;
    }
  };

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString()} ر.س`;
  };

  const calculateRevenue = () => {
    return subscriptions
      .filter(sub => sub.status === 'active')
      .reduce((sum, sub) => sum + sub.price, 0);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والإحصائيات */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          إدارة الاشتراكات
        </Typography>
        
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <CreditCardIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      إجمالي الاشتراكات
                    </Typography>
                    <Typography variant="h5">
                      {subscriptions.length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <CheckCircleIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      الاشتراكات النشطة
                    </Typography>
                    <Typography variant="h5">
                      {subscriptions.filter(s => s.status === 'active').length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <MoneyIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      الإيرادات الشهرية
                    </Typography>
                    <Typography variant="h5">
                      {formatCurrency(calculateRevenue())}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      معدل التجديد
                    </Typography>
                    <Typography variant="h5">
                      {((subscriptions.filter(s => s.autoRenewal).length / subscriptions.length) * 100).toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* أدوات البحث والفلترة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="البحث بالاسم أو البريد الإلكتروني أو الشركة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الباقة</InputLabel>
                <Select
                  value={filterPlan}
                  label="الباقة"
                  onChange={(e) => setFilterPlan(e.target.value)}
                >
                  <MenuItem value="all">جميع الباقات</MenuItem>
                  <MenuItem value="الباقة المجانية">الباقة المجانية</MenuItem>
                  <MenuItem value="الباقة الشخصية">الباقة الشخصية</MenuItem>
                  <MenuItem value="باقة الأعمال">باقة الأعمال</MenuItem>
                  <MenuItem value="الباقة المؤسسية">الباقة المؤسسية</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={filterStatus}
                  label="الحالة"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">جميع الحالات</MenuItem>
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="pending">في الانتظار</MenuItem>
                  <MenuItem value="expired">منتهي الصلاحية</MenuItem>
                  <MenuItem value="cancelled">ملغي</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<PaymentIcon />}
              >
                تقرير المدفوعات
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* جدول الاشتراكات */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>العميل</TableCell>
                  <TableCell>الباقة</TableCell>
                  <TableCell>السعر الشهري</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>تاريخ البداية</TableCell>
                  <TableCell>تاريخ النهاية</TableCell>
                  <TableCell>إجمالي المدفوع</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredSubscriptions
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {subscription.customerName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {subscription.customerEmail}
                          </Typography>
                          {subscription.company && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              {subscription.company}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={subscription.plan}
                          color={getPlanColor(subscription.plan)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(subscription.price)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          {getStatusIcon(subscription.status)}
                          <Chip
                            label={getStatusText(subscription.status)}
                            color={getStatusColor(subscription.status)}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>{subscription.startDate}</TableCell>
                      <TableCell>{subscription.endDate}</TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(subscription.totalPaid)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {subscription.invoicesCount} فاتورة
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => openDialog('view', subscription)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => openDialog('edit', subscription)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => openDialog('invoices', subscription)}
                        >
                          <ReceiptIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredSubscriptions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
          />
        </CardContent>
      </Card>

      {/* الحوارات */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'view' && 'تفاصيل الاشتراك'}
          {dialogType === 'edit' && 'تعديل الاشتراك'}
          {dialogType === 'invoices' && 'الفواتير'}
        </DialogTitle>

        <DialogContent>
          {dialogType === 'view' && selectedSubscription && (
            <Box>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label="معلومات الاشتراك" />
                <Tab label="تفاصيل الدفع" />
                <Tab label="الإحصائيات" />
              </Tabs>

              {activeTab === 0 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">اسم العميل</Typography>
                      <Typography variant="body2">{selectedSubscription.customerName}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">البريد الإلكتروني</Typography>
                      <Typography variant="body2">{selectedSubscription.customerEmail}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الشركة</Typography>
                      <Typography variant="body2">{selectedSubscription.company || '-'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الباقة</Typography>
                      <Chip
                        label={selectedSubscription.plan}
                        color={getPlanColor(selectedSubscription.plan)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">السعر الشهري</Typography>
                      <Typography variant="body2">{formatCurrency(selectedSubscription.price)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الحالة</Typography>
                      <Chip
                        label={getStatusText(selectedSubscription.status)}
                        color={getStatusColor(selectedSubscription.status)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">تاريخ البداية</Typography>
                      <Typography variant="body2">{selectedSubscription.startDate}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">تاريخ النهاية</Typography>
                      <Typography variant="body2">{selectedSubscription.endDate}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">التجديد التلقائي</Typography>
                      <Chip
                        label={selectedSubscription.autoRenewal ? 'مفعل' : 'معطل'}
                        color={selectedSubscription.autoRenewal ? 'success' : 'default'}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الفوترة التالية</Typography>
                      <Typography variant="body2">{selectedSubscription.nextBilling || '-'}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">طريقة الدفع</Typography>
                      <Typography variant="body2">{selectedSubscription.paymentMethod}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">إجمالي المدفوع</Typography>
                      <Typography variant="body2" color="success.main" fontWeight="bold">
                        {formatCurrency(selectedSubscription.totalPaid)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">عدد الفواتير</Typography>
                      <Typography variant="body2">{selectedSubscription.invoicesCount}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">متوسط الدفع الشهري</Typography>
                      <Typography variant="body2">
                        {formatCurrency(Math.round(selectedSubscription.totalPaid / selectedSubscription.invoicesCount))}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 2 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>إحصائيات الاشتراك</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2">مدة الاشتراك</Typography>
                      <Typography variant="h4" color="primary.main">
                        {Math.round((new Date(selectedSubscription.endDate) - new Date(selectedSubscription.startDate)) / (1000 * 60 * 60 * 24 * 30))} شهر
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2">القيمة الإجمالية</Typography>
                      <Typography variant="h4" color="success.main">
                        {formatCurrency(selectedSubscription.totalPaid)}
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2">معدل الدفع</Typography>
                      <Typography variant="h4" color="info.main">
                        100%
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </Box>
          )}

          {dialogType === 'edit' && selectedSubscription && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الباقة</InputLabel>
                    <Select defaultValue={selectedSubscription.plan}>
                      <MenuItem value="الباقة المجانية">الباقة المجانية</MenuItem>
                      <MenuItem value="الباقة الشخصية">الباقة الشخصية</MenuItem>
                      <MenuItem value="باقة الأعمال">باقة الأعمال</MenuItem>
                      <MenuItem value="الباقة المؤسسية">الباقة المؤسسية</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الحالة</InputLabel>
                    <Select defaultValue={selectedSubscription.status}>
                      <MenuItem value="active">نشط</MenuItem>
                      <MenuItem value="pending">في الانتظار</MenuItem>
                      <MenuItem value="expired">منتهي الصلاحية</MenuItem>
                      <MenuItem value="cancelled">ملغي</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="تاريخ النهاية"
                    type="date"
                    defaultValue={selectedSubscription.endDate}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>التجديد التلقائي</InputLabel>
                    <Select defaultValue={selectedSubscription.autoRenewal}>
                      <MenuItem value={true}>مفعل</MenuItem>
                      <MenuItem value={false}>معطل</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}

          {dialogType === 'invoices' && selectedSubscription && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                فواتير {selectedSubscription.customerName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                سيتم عرض قائمة الفواتير هنا...
              </Typography>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={closeDialog}>إغلاق</Button>
          {dialogType === 'view' && (
            <Button onClick={() => openDialog('edit', selectedSubscription)} variant="contained">
              تعديل
            </Button>
          )}
          {dialogType === 'edit' && (
            <Button variant="contained">
              حفظ التغييرات
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubscriptionManagement;
