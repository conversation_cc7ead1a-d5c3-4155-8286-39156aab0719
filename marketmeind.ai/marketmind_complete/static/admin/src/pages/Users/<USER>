import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Avatar,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Tabs,
  Tab,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as VisibilityIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const UserManagement = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, filterType, filterStatus]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات المستخدمين
      const mockUsers = [
        {
          id: 1,
          firstName: 'أحمد',
          lastName: 'محمد',
          email: '<EMAIL>',
          phone: '+************',
          accountType: 'business_owner',
          status: 'active',
          joinDate: '2023-01-15',
          lastLogin: '2024-06-23 14:30',
          subscriptionPlan: 'باقة الأعمال',
          totalUsage: 89.5,
          company: 'شركة التقنية المتقدمة'
        },
        {
          id: 2,
          firstName: 'فاطمة',
          lastName: 'أحمد',
          email: '<EMAIL>',
          phone: '+************',
          accountType: 'personal',
          status: 'active',
          joinDate: '2023-03-20',
          lastLogin: '2024-06-23 13:45',
          subscriptionPlan: 'الباقة الشخصية',
          totalUsage: 76.2,
          company: null
        },
        {
          id: 3,
          firstName: 'محمد',
          lastName: 'علي',
          email: '<EMAIL>',
          phone: '+************',
          accountType: 'business_user',
          status: 'inactive',
          joinDate: '2024-05-10',
          lastLogin: '2024-06-20 09:15',
          subscriptionPlan: 'باقة الأعمال',
          totalUsage: 45.8,
          company: 'مؤسسة الابتكار'
        },
        {
          id: 4,
          firstName: 'سارة',
          lastName: 'خالد',
          email: '<EMAIL>',
          phone: '+************',
          accountType: 'personal',
          status: 'active',
          joinDate: '2023-08-05',
          lastLogin: '2024-06-22 16:20',
          subscriptionPlan: 'الباقة الشخصية',
          totalUsage: 54.3,
          company: null
        },
        {
          id: 5,
          firstName: 'عبدالله',
          lastName: 'حسن',
          email: '<EMAIL>',
          phone: '+************',
          accountType: 'admin',
          status: 'active',
          joinDate: '2022-11-12',
          lastLogin: '2024-06-23 15:00',
          subscriptionPlan: 'مدير النظام',
          totalUsage: 125.7,
          company: 'MarketMind'
        }
      ];

      setUsers(mockUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.company && user.company.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(user => user.accountType === filterType);
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(user => user.status === filterStatus);
    }

    setFilteredUsers(filtered);
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const openDialog = (type, user = null) => {
    setDialogType(type);
    setSelectedUser(user);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedUser(null);
    setDialogType('');
  };

  const handleUserAction = async (action, userId) => {
    try {
      // محاكاة API call
      console.log(`${action} user ${userId}`);
      
      // تحديث حالة المستخدم محلياً
      setUsers(users.map(user => 
        user.id === userId 
          ? { ...user, status: action === 'activate' ? 'active' : 'inactive' }
          : user
      ));
      
      closeDialog();
    } catch (error) {
      console.error(`Error ${action} user:`, error);
    }
  };

  const getAccountTypeIcon = (type) => {
    switch (type) {
      case 'admin': return <AdminIcon />;
      case 'business_owner': return <BusinessIcon />;
      case 'business_user': return <BusinessIcon />;
      case 'personal': return <PersonIcon />;
      default: return <PersonIcon />;
    }
  };

  const getAccountTypeText = (type) => {
    switch (type) {
      case 'admin': return 'مدير';
      case 'business_owner': return 'مالك شركة';
      case 'business_user': return 'مستخدم شركة';
      case 'personal': return 'مستخدم شخصي';
      default: return type;
    }
  };

  const getAccountTypeColor = (type) => {
    switch (type) {
      case 'admin': return 'error';
      case 'business_owner': return 'primary';
      case 'business_user': return 'secondary';
      case 'personal': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'error';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والإحصائيات */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          إدارة المستخدمين
        </Typography>
        
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <PeopleIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      إجمالي المستخدمين
                    </Typography>
                    <Typography variant="h5">
                      {users.length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <CheckCircleIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      المستخدمون النشطون
                    </Typography>
                    <Typography variant="h5">
                      {users.filter(u => u.status === 'active').length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                    <BusinessIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      مستخدمو الأعمال
                    </Typography>
                    <Typography variant="h5">
                      {users.filter(u => u.accountType.includes('business')).length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      المستخدمون الشخصيون
                    </Typography>
                    <Typography variant="h5">
                      {users.filter(u => u.accountType === 'personal').length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* أدوات البحث والفلترة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="البحث بالاسم أو البريد الإلكتروني أو الشركة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>نوع الحساب</InputLabel>
                <Select
                  value={filterType}
                  label="نوع الحساب"
                  onChange={(e) => setFilterType(e.target.value)}
                >
                  <MenuItem value="all">جميع الأنواع</MenuItem>
                  <MenuItem value="admin">مدير</MenuItem>
                  <MenuItem value="business_owner">مالك شركة</MenuItem>
                  <MenuItem value="business_user">مستخدم شركة</MenuItem>
                  <MenuItem value="personal">مستخدم شخصي</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={filterStatus}
                  label="الحالة"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">جميع الحالات</MenuItem>
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="inactive">غير نشط</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<PersonAddIcon />}
                onClick={() => openDialog('add')}
              >
                إضافة مستخدم
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* جدول المستخدمين */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>المستخدم</TableCell>
                  <TableCell>نوع الحساب</TableCell>
                  <TableCell>الشركة</TableCell>
                  <TableCell>الباقة</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>آخر تسجيل دخول</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ mr: 2, bgcolor: getAccountTypeColor(user.accountType) + '.main' }}>
                            {getAccountTypeIcon(user.accountType)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {user.firstName} {user.lastName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={getAccountTypeText(user.accountType)}
                          color={getAccountTypeColor(user.accountType)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {user.company || '-'}
                      </TableCell>
                      <TableCell>
                        {user.subscriptionPlan}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={user.status === 'active' ? 'نشط' : 'غير نشط'}
                          color={getStatusColor(user.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {user.lastLogin}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => openDialog('view', user)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => openDialog('edit', user)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => openDialog(user.status === 'active' ? 'deactivate' : 'activate', user)}
                        >
                          {user.status === 'active' ? <BlockIcon /> : <CheckCircleIcon />}
                        </IconButton>
                        {user.accountType !== 'admin' && (
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => openDialog('delete', user)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredUsers.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
          />
        </CardContent>
      </Card>

      {/* الحوارات */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'view' && 'تفاصيل المستخدم'}
          {dialogType === 'edit' && 'تعديل المستخدم'}
          {dialogType === 'add' && 'إضافة مستخدم جديد'}
          {dialogType === 'activate' && 'تفعيل المستخدم'}
          {dialogType === 'deactivate' && 'إلغاء تفعيل المستخدم'}
          {dialogType === 'delete' && 'حذف المستخدم'}
        </DialogTitle>

        <DialogContent>
          {dialogType === 'view' && selectedUser && (
            <Box>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label="المعلومات الأساسية" />
                <Tab label="الإحصائيات" />
                <Tab label="النشاط" />
              </Tabs>

              {activeTab === 0 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الاسم الكامل</Typography>
                      <Typography variant="body2">{selectedUser.firstName} {selectedUser.lastName}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">البريد الإلكتروني</Typography>
                      <Typography variant="body2">{selectedUser.email}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">رقم الهاتف</Typography>
                      <Typography variant="body2">{selectedUser.phone}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">نوع الحساب</Typography>
                      <Chip
                        label={getAccountTypeText(selectedUser.accountType)}
                        color={getAccountTypeColor(selectedUser.accountType)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الشركة</Typography>
                      <Typography variant="body2">{selectedUser.company || '-'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الباقة</Typography>
                      <Typography variant="body2">{selectedUser.subscriptionPlan}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">تاريخ الانضمام</Typography>
                      <Typography variant="body2">{selectedUser.joinDate}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">الحالة</Typography>
                      <Chip
                        label={selectedUser.status === 'active' ? 'نشط' : 'غير نشط'}
                        color={getStatusColor(selectedUser.status)}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>إحصائيات الاستخدام</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">إجمالي الاستخدام</Typography>
                      <Typography variant="body2">{selectedUser.totalUsage} ساعة</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">آخر تسجيل دخول</Typography>
                      <Typography variant="body2">{selectedUser.lastLogin}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 2 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>سجل النشاط</Typography>
                  <Typography variant="body2" color="text.secondary">
                    سيتم عرض سجل النشاط هنا...
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {dialogType === 'edit' && selectedUser && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="الاسم الأول"
                    defaultValue={selectedUser.firstName}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="الاسم الأخير"
                    defaultValue={selectedUser.lastName}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="البريد الإلكتروني"
                    defaultValue={selectedUser.email}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="رقم الهاتف"
                    defaultValue={selectedUser.phone}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>نوع الحساب</InputLabel>
                    <Select defaultValue={selectedUser.accountType}>
                      <MenuItem value="personal">مستخدم شخصي</MenuItem>
                      <MenuItem value="business_user">مستخدم شركة</MenuItem>
                      <MenuItem value="business_owner">مالك شركة</MenuItem>
                      <MenuItem value="admin">مدير</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="الشركة"
                    defaultValue={selectedUser.company || ''}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked={selectedUser.status === 'active'} />}
                    label="حساب نشط"
                  />
                </Grid>
              </Grid>
            </Box>
          )}

          {dialogType === 'add' && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField fullWidth label="الاسم الأول" />
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="الاسم الأخير" />
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="البريد الإلكتروني" type="email" />
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="رقم الهاتف" />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>نوع الحساب</InputLabel>
                    <Select defaultValue="personal">
                      <MenuItem value="personal">مستخدم شخصي</MenuItem>
                      <MenuItem value="business_user">مستخدم شركة</MenuItem>
                      <MenuItem value="business_owner">مالك شركة</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="الشركة (اختياري)" />
                </Grid>
                <Grid item xs={12}>
                  <TextField fullWidth label="كلمة المرور" type="password" />
                </Grid>
              </Grid>
            </Box>
          )}

          {(dialogType === 'activate' || dialogType === 'deactivate') && selectedUser && (
            <DialogContentText>
              هل أنت متأكد من {dialogType === 'activate' ? 'تفعيل' : 'إلغاء تفعيل'} حساب {selectedUser.firstName} {selectedUser.lastName}؟
            </DialogContentText>
          )}

          {dialogType === 'delete' && selectedUser && (
            <DialogContentText>
              هل أنت متأكد من حذف حساب {selectedUser.firstName} {selectedUser.lastName}؟
              هذا الإجراء لا يمكن التراجع عنه.
            </DialogContentText>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={closeDialog}>إلغاء</Button>
          {dialogType === 'view' && (
            <Button onClick={() => openDialog('edit', selectedUser)} variant="contained">
              تعديل
            </Button>
          )}
          {(dialogType === 'edit' || dialogType === 'add') && (
            <Button variant="contained">
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
          {dialogType === 'activate' && (
            <Button
              variant="contained"
              color="success"
              onClick={() => handleUserAction('activate', selectedUser.id)}
            >
              تفعيل
            </Button>
          )}
          {dialogType === 'deactivate' && (
            <Button
              variant="contained"
              color="warning"
              onClick={() => handleUserAction('deactivate', selectedUser.id)}
            >
              إلغاء التفعيل
            </Button>
          )}
          {dialogType === 'delete' && (
            <Button
              variant="contained"
              color="error"
              onClick={() => handleUserAction('delete', selectedUser.id)}
            >
              حذف
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
