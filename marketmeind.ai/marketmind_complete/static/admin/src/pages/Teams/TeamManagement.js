import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  AvatarGroup,
  LinearProgress
} from '@mui/material';
import {
  Groups as GroupsIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as SupervisorIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const TeamManagement = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [teams, setTeams] = useState([]);
  const [filteredTeams, setFilteredTeams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchTeams();
  }, []);

  useEffect(() => {
    filterTeams();
  }, [teams, searchTerm]);

  const fetchTeams = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات الفرق
      const mockTeams = [
        {
          id: 1,
          name: 'فريق التقنية المتقدمة',
          company: 'شركة التقنية المتقدمة',
          description: 'فريق تطوير المنتجات التقنية',
          memberCount: 12,
          owner: {
            name: 'أحمد محمد',
            email: '<EMAIL>',
            avatar: 'A'
          },
          members: [
            { id: 1, name: 'سارة أحمد', role: 'مطور', email: '<EMAIL>', status: 'active' },
            { id: 2, name: 'محمد علي', role: 'مصمم', email: '<EMAIL>', status: 'active' },
            { id: 3, name: 'فاطمة خالد', role: 'محلل', email: '<EMAIL>', status: 'active' }
          ],
          createdDate: '2023-01-15',
          subscriptionPlan: 'الباقة المؤسسية',
          status: 'active',
          performance: {
            projectsCompleted: 15,
            avgProductivity: 85,
            satisfaction: 92
          }
        },
        {
          id: 2,
          name: 'فريق الابتكار',
          company: 'مؤسسة الابتكار',
          description: 'فريق البحث والتطوير',
          memberCount: 8,
          owner: {
            name: 'عبدالله حسن',
            email: '<EMAIL>',
            avatar: 'ع'
          },
          members: [
            { id: 4, name: 'نورا سالم', role: 'باحث', email: '<EMAIL>', status: 'active' },
            { id: 5, name: 'خالد أحمد', role: 'مطور', email: '<EMAIL>', status: 'active' }
          ],
          createdDate: '2023-03-20',
          subscriptionPlan: 'باقة الأعمال',
          status: 'active',
          performance: {
            projectsCompleted: 8,
            avgProductivity: 78,
            satisfaction: 88
          }
        },
        {
          id: 3,
          name: 'فريق التسويق الرقمي',
          company: 'شركة النجاح',
          description: 'فريق التسويق والإعلان الرقمي',
          memberCount: 6,
          owner: {
            name: 'مريم عبدالله',
            email: '<EMAIL>',
            avatar: 'م'
          },
          members: [
            { id: 6, name: 'أمل محمد', role: 'مسوق', email: '<EMAIL>', status: 'active' },
            { id: 7, name: 'يوسف علي', role: 'محلل', email: '<EMAIL>', status: 'inactive' }
          ],
          createdDate: '2023-05-10',
          subscriptionPlan: 'باقة الأعمال',
          status: 'active',
          performance: {
            projectsCompleted: 12,
            avgProductivity: 82,
            satisfaction: 90
          }
        }
      ];

      setTeams(mockTeams);
    } catch (error) {
      console.error('Error fetching teams:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterTeams = () => {
    let filtered = teams;

    if (searchTerm) {
      filtered = filtered.filter(team =>
        team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        team.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
        team.owner.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredTeams(filtered);
  };

  const openDialog = (type, team = null) => {
    setDialogType(type);
    setSelectedTeam(team);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedTeam(null);
    setDialogType('');
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'مطور': return 'primary';
      case 'مصمم': return 'secondary';
      case 'محلل': return 'info';
      case 'مسوق': return 'success';
      case 'باحث': return 'warning';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'error';
  };

  const getPerformanceColor = (value) => {
    if (value >= 80) return 'success';
    if (value >= 60) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والإحصائيات */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          إدارة الفرق
        </Typography>
        
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <GroupsIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      إجمالي الفرق
                    </Typography>
                    <Typography variant="h5">
                      {teams.length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      إجمالي الأعضاء
                    </Typography>
                    <Typography variant="h5">
                      {teams.reduce((sum, team) => sum + team.memberCount, 0)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <BusinessIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      الشركات النشطة
                    </Typography>
                    <Typography variant="h5">
                      {new Set(teams.map(team => team.company)).size}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <WorkIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      المشاريع المكتملة
                    </Typography>
                    <Typography variant="h5">
                      {teams.reduce((sum, team) => sum + team.performance.projectsCompleted, 0)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* أدوات البحث */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                placeholder="البحث بالاسم أو الشركة أو المالك..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => openDialog('add')}
              >
                إضافة فريق جديد
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* قائمة الفرق */}
      <Grid container spacing={3}>
        {filteredTeams.map((team) => (
          <Grid item xs={12} md={6} lg={4} key={team.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      {team.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {team.company}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      {team.description}
                    </Typography>
                  </Box>
                  <Box>
                    <IconButton size="small" onClick={() => openDialog('view', team)}>
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton size="small" onClick={() => openDialog('edit', team)}>
                      <EditIcon />
                    </IconButton>
                  </Box>
                </Box>

                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">مالك الفريق:</Typography>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.75rem' }}>
                        {team.owner.avatar}
                      </Avatar>
                      <Typography variant="body2">{team.owner.name}</Typography>
                    </Box>
                  </Box>
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">عدد الأعضاء:</Typography>
                    <Typography variant="body2" fontWeight="bold">{team.memberCount}</Typography>
                  </Box>
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">الباقة:</Typography>
                    <Chip label={team.subscriptionPlan} size="small" color="primary" />
                  </Box>
                </Box>

                <Box mb={2}>
                  <Typography variant="body2" gutterBottom>الأداء:</Typography>
                  <Box mb={1}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="caption">الإنتاجية</Typography>
                      <Typography variant="caption">{team.performance.avgProductivity}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={team.performance.avgProductivity} 
                      color={getPerformanceColor(team.performance.avgProductivity)}
                    />
                  </Box>
                  <Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="caption">الرضا</Typography>
                      <Typography variant="caption">{team.performance.satisfaction}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={team.performance.satisfaction} 
                      color={getPerformanceColor(team.performance.satisfaction)}
                    />
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 24, height: 24, fontSize: '0.75rem' } }}>
                    {team.members.slice(0, 4).map((member) => (
                      <Avatar key={member.id}>{member.name.charAt(0)}</Avatar>
                    ))}
                  </AvatarGroup>
                  <Chip 
                    label={team.status === 'active' ? 'نشط' : 'غير نشط'}
                    color={getStatusColor(team.status)}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* الحوارات */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'view' && 'تفاصيل الفريق'}
          {dialogType === 'edit' && 'تعديل الفريق'}
          {dialogType === 'add' && 'إضافة فريق جديد'}
        </DialogTitle>

        <DialogContent>
          {dialogType === 'view' && selectedTeam && (
            <Box>
              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">اسم الفريق</Typography>
                  <Typography variant="body2">{selectedTeam.name}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">الشركة</Typography>
                  <Typography variant="body2">{selectedTeam.company}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2">الوصف</Typography>
                  <Typography variant="body2">{selectedTeam.description}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">مالك الفريق</Typography>
                  <Box display="flex" alignItems="center">
                    <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                      {selectedTeam.owner.avatar}
                    </Avatar>
                    <Box>
                      <Typography variant="body2">{selectedTeam.owner.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {selectedTeam.owner.email}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">تاريخ الإنشاء</Typography>
                  <Typography variant="body2">{selectedTeam.createdDate}</Typography>
                </Grid>
              </Grid>

              <Typography variant="h6" gutterBottom>أعضاء الفريق</Typography>
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>الاسم</TableCell>
                      <TableCell>الدور</TableCell>
                      <TableCell>البريد الإلكتروني</TableCell>
                      <TableCell>الحالة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedTeam.members.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: '0.75rem' }}>
                              {member.name.charAt(0)}
                            </Avatar>
                            {member.name}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={member.role}
                            color={getRoleColor(member.role)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{member.email}</TableCell>
                        <TableCell>
                          <Chip
                            label={member.status === 'active' ? 'نشط' : 'غير نشط'}
                            color={getStatusColor(member.status)}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box mt={3}>
                <Typography variant="h6" gutterBottom>إحصائيات الأداء</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Typography variant="subtitle2">المشاريع المكتملة</Typography>
                    <Typography variant="h4" color="primary.main">
                      {selectedTeam.performance.projectsCompleted}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="subtitle2">متوسط الإنتاجية</Typography>
                    <Typography variant="h4" color={getPerformanceColor(selectedTeam.performance.avgProductivity) + '.main'}>
                      {selectedTeam.performance.avgProductivity}%
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="subtitle2">مستوى الرضا</Typography>
                    <Typography variant="h4" color={getPerformanceColor(selectedTeam.performance.satisfaction) + '.main'}>
                      {selectedTeam.performance.satisfaction}%
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          )}

          {dialogType === 'edit' && selectedTeam && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="اسم الفريق"
                    defaultValue={selectedTeam.name}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="الشركة"
                    defaultValue={selectedTeam.company}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="الوصف"
                    multiline
                    rows={3}
                    defaultValue={selectedTeam.description}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الباقة</InputLabel>
                    <Select defaultValue={selectedTeam.subscriptionPlan}>
                      <MenuItem value="باقة الأعمال">باقة الأعمال</MenuItem>
                      <MenuItem value="الباقة المؤسسية">الباقة المؤسسية</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الحالة</InputLabel>
                    <Select defaultValue={selectedTeam.status}>
                      <MenuItem value="active">نشط</MenuItem>
                      <MenuItem value="inactive">غير نشط</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}

          {dialogType === 'add' && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField fullWidth label="اسم الفريق" />
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="الشركة" />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="الوصف"
                    multiline
                    rows={3}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField fullWidth label="بريد مالك الفريق" type="email" />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الباقة</InputLabel>
                    <Select defaultValue="باقة الأعمال">
                      <MenuItem value="باقة الأعمال">باقة الأعمال</MenuItem>
                      <MenuItem value="الباقة المؤسسية">الباقة المؤسسية</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={closeDialog}>إلغاء</Button>
          {dialogType === 'view' && (
            <Button onClick={() => openDialog('edit', selectedTeam)} variant="contained">
              تعديل
            </Button>
          )}
          {(dialogType === 'edit' || dialogType === 'add') && (
            <Button variant="contained">
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamManagement;
