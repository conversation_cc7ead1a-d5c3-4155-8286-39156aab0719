import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  LinearProgress,
  Avatar
} from '@mui/material';
import {
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Receipt as ReceiptIcon,
  Analytics as AnalyticsIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const FinancialAnalytics = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [activeTab, setActiveTab] = useState(0);
  const [financialData, setFinancialData] = useState({
    overview: {
      totalRevenue: 0,
      monthlyRevenue: 0,
      totalProfit: 0,
      monthlyProfit: 0,
      totalExpenses: 0,
      monthlyExpenses: 0,
      profitMargin: 0,
      revenueGrowth: 0
    },
    revenueByMonth: [],
    subscriptionPlans: [],
    paymentMethods: [],
    topCustomers: [],
    projections: {}
  });

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchFinancialData();
  }, [selectedPeriod]);

  const fetchFinancialData = async () => {
    setLoading(true);
    try {
      // محاكاة البيانات المالية
      const mockData = {
        overview: {
          totalRevenue: 485000,
          monthlyRevenue: 18500,
          totalProfit: 145500,
          monthlyProfit: 5550,
          totalExpenses: 339500,
          monthlyExpenses: 12950,
          profitMargin: 30.0,
          revenueGrowth: 12.5
        },
        revenueByMonth: [
          { month: 'يناير', revenue: 15200, profit: 4560, expenses: 10640 },
          { month: 'فبراير', revenue: 16800, profit: 5040, expenses: 11760 },
          { month: 'مارس', revenue: 18200, profit: 5460, expenses: 12740 },
          { month: 'أبريل', revenue: 17500, profit: 5250, expenses: 12250 },
          { month: 'مايو', revenue: 19200, profit: 5760, expenses: 13440 },
          { month: 'يونيو', revenue: 18500, profit: 5550, expenses: 12950 }
        ],
        subscriptionPlans: [
          {
            name: 'الباقة المجانية',
            subscribers: 687,
            revenue: 0,
            percentage: 55.1,
            avgRevenue: 0,
            churnRate: 15.2
          },
          {
            name: 'الباقة الشخصية',
            subscribers: 312,
            revenue: 9360,
            percentage: 25.0,
            avgRevenue: 30,
            churnRate: 8.5
          },
          {
            name: 'باقة الأعمال',
            subscribers: 198,
            revenue: 19800,
            percentage: 15.9,
            avgRevenue: 100,
            churnRate: 5.2
          },
          {
            name: 'الباقة المؤسسية',
            subscribers: 50,
            revenue: 25000,
            percentage: 4.0,
            avgRevenue: 500,
            churnRate: 2.1
          }
        ],
        paymentMethods: [
          { method: 'بطاقة ائتمان', transactions: 456, amount: 34200, percentage: 62.5 },
          { method: 'تحويل بنكي', transactions: 189, amount: 18900, percentage: 34.5 },
          { method: 'محفظة رقمية', transactions: 67, amount: 1340, percentage: 2.4 },
          { method: 'أخرى', transactions: 23, amount: 320, percentage: 0.6 }
        ],
        topCustomers: [
          {
            id: 1,
            name: 'شركة التقنية المتقدمة',
            email: '<EMAIL>',
            plan: 'الباقة المؤسسية',
            totalRevenue: 6000,
            lastPayment: '2024-06-20',
            status: 'active'
          },
          {
            id: 2,
            name: 'مؤسسة الابتكار',
            email: '<EMAIL>',
            plan: 'باقة الأعمال',
            totalRevenue: 2400,
            lastPayment: '2024-06-18',
            status: 'active'
          },
          {
            id: 3,
            name: 'شركة النجاح',
            email: '<EMAIL>',
            plan: 'باقة الأعمال',
            totalRevenue: 1800,
            lastPayment: '2024-06-15',
            status: 'active'
          },
          {
            id: 4,
            name: 'مجموعة الريادة',
            email: '<EMAIL>',
            plan: 'الباقة المؤسسية',
            totalRevenue: 4500,
            lastPayment: '2024-06-22',
            status: 'active'
          }
        ],
        projections: {
          nextMonthRevenue: 19800,
          nextQuarterRevenue: 58500,
          yearEndRevenue: 225000,
          growthRate: 15.2
        }
      };

      setFinancialData(mockData);
    } catch (error) {
      console.error('Error fetching financial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getPlanColor = (plan) => {
    switch (plan) {
      case 'الباقة المجانية': return 'default';
      case 'الباقة الشخصية': return 'primary';
      case 'باقة الأعمال': return 'secondary';
      case 'الباقة المؤسسية': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString()} ر.س`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والفلاتر */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          التحليلات المالية
        </Typography>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>الفترة الزمنية</InputLabel>
          <Select
            value={selectedPeriod}
            label="الفترة الزمنية"
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <MenuItem value="week">أسبوع</MenuItem>
            <MenuItem value="month">شهر</MenuItem>
            <MenuItem value="quarter">ربع سنة</MenuItem>
            <MenuItem value="year">سنة</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* الإحصائيات الرئيسية */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الإيرادات الشهرية
                  </Typography>
                  <Typography variant="h6">
                    {formatCurrency(financialData.overview.monthlyRevenue)}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +{financialData.overview.revenueGrowth}% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الأرباح الشهرية
                  </Typography>
                  <Typography variant="h6">
                    {formatCurrency(financialData.overview.monthlyProfit)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    هامش ربح {financialData.overview.profitMargin}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <ReceiptIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    المصروفات الشهرية
                  </Typography>
                  <Typography variant="h6">
                    {formatCurrency(financialData.overview.monthlyExpenses)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {((financialData.overview.monthlyExpenses / financialData.overview.monthlyRevenue) * 100).toFixed(1)}% من الإيرادات
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <BankIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    إجمالي الإيرادات
                  </Typography>
                  <Typography variant="h6">
                    {formatCurrency(financialData.overview.totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    نمو مستمر
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="الإيرادات الشهرية" />
            <Tab label="أداء الباقات" />
            <Tab label="طرق الدفع" />
            <Tab label="أفضل العملاء" />
            <Tab label="التوقعات المالية" />
          </Tabs>
        </Box>

        <CardContent>
          {/* تبويب الإيرادات الشهرية */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                تطور الإيرادات والأرباح خلال الأشهر الستة الماضية
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الشهر</TableCell>
                      <TableCell align="right">الإيرادات</TableCell>
                      <TableCell align="right">الأرباح</TableCell>
                      <TableCell align="right">المصروفات</TableCell>
                      <TableCell align="right">هامش الربح</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {financialData.revenueByMonth.map((row) => {
                      const profitMargin = ((row.profit / row.revenue) * 100).toFixed(1);
                      return (
                        <TableRow key={row.month}>
                          <TableCell component="th" scope="row">
                            {row.month}
                          </TableCell>
                          <TableCell align="right">{formatCurrency(row.revenue)}</TableCell>
                          <TableCell align="right">{formatCurrency(row.profit)}</TableCell>
                          <TableCell align="right">{formatCurrency(row.expenses)}</TableCell>
                          <TableCell align="right">
                            <Chip 
                              label={`${profitMargin}%`}
                              color={profitMargin > 25 ? 'success' : profitMargin > 15 ? 'warning' : 'error'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* تبويب أداء الباقات */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                أداء باقات الاشتراك
              </Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                {financialData.subscriptionPlans.map((plan) => (
                  <Grid item xs={12} sm={6} md={3} key={plan.name}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" color={getPlanColor(plan.name) + '.main'}>
                          {plan.subscribers.toLocaleString()}
                        </Typography>
                        <Typography color="text.secondary" gutterBottom>
                          {plan.name}
                        </Typography>
                        <Typography variant="h6" color="success.main">
                          {formatCurrency(plan.revenue)}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          متوسط الإيراد: {formatCurrency(plan.avgRevenue)}
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={plan.percentage} 
                          color={getPlanColor(plan.name)}
                          sx={{ mt: 1 }}
                        />
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          معدل التوقف: {plan.churnRate}%
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* تبويب طرق الدفع */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                توزيع المدفوعات حسب طريقة الدفع
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>طريقة الدفع</TableCell>
                      <TableCell align="right">عدد المعاملات</TableCell>
                      <TableCell align="right">المبلغ الإجمالي</TableCell>
                      <TableCell align="right">النسبة</TableCell>
                      <TableCell align="right">متوسط المعاملة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {financialData.paymentMethods.map((method) => (
                      <TableRow key={method.method}>
                        <TableCell component="th" scope="row">
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                              <CardIcon />
                            </Avatar>
                            {method.method}
                          </Box>
                        </TableCell>
                        <TableCell align="right">{method.transactions}</TableCell>
                        <TableCell align="right">{formatCurrency(method.amount)}</TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={`${method.percentage}%`}
                            color="primary"
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(Math.round(method.amount / method.transactions))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* تبويب أفضل العملاء */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                أفضل العملاء من ناحية الإيرادات
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>العميل</TableCell>
                      <TableCell>الباقة</TableCell>
                      <TableCell align="right">إجمالي الإيرادات</TableCell>
                      <TableCell align="right">آخر دفعة</TableCell>
                      <TableCell align="right">الحالة</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {financialData.topCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {customer.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {customer.email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={customer.plan}
                            color={getPlanColor(customer.plan)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="bold" color="success.main">
                            {formatCurrency(customer.totalRevenue)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{customer.lastPayment}</TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={customer.status === 'active' ? 'نشط' : 'غير نشط'}
                            color={customer.status === 'active' ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* تبويب التوقعات المالية */}
          {activeTab === 4 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                التوقعات المالية
              </Typography>
              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h4" color="primary.main">
                        {formatCurrency(financialData.projections.nextMonthRevenue)}
                      </Typography>
                      <Typography color="text.secondary">
                        إيرادات الشهر القادم
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h4" color="secondary.main">
                        {formatCurrency(financialData.projections.nextQuarterRevenue)}
                      </Typography>
                      <Typography color="text.secondary">
                        إيرادات الربع القادم
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h4" color="success.main">
                        {formatCurrency(financialData.projections.yearEndRevenue)}
                      </Typography>
                      <Typography color="text.secondary">
                        إيرادات نهاية العام
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h4" color="warning.main">
                        +{financialData.projections.growthRate}%
                      </Typography>
                      <Typography color="text.secondary">
                        معدل النمو المتوقع
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default FinancialAnalytics;
