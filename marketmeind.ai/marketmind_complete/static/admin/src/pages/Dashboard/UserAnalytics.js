import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  LinearProgress
} from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  PersonAdd as PersonAddIcon,
  ExitToApp as ExitToAppIcon,
  Timeline as TimelineIcon,
  Analytics as AnalyticsIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const UserAnalytics = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [activeTab, setActiveTab] = useState(0);
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalUsers: 0,
      newUsers: 0,
      activeUsers: 0,
      churnRate: 0,
      retentionRate: 0,
      avgSessionDuration: 0
    },
    userGrowth: [],
    usersByType: [],
    topUsers: [],
    usagePatterns: [],
    conversionRates: {}
  });

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات تحليلات المستخدمين
      const mockData = {
        overview: {
          totalUsers: 1247,
          newUsers: 89,
          activeUsers: 892,
          churnRate: 5.2,
          retentionRate: 78.5,
          avgSessionDuration: 24.5
        },
        userGrowth: [
          { month: 'يناير', users: 850, newUsers: 120 },
          { month: 'فبراير', users: 920, newUsers: 95 },
          { month: 'مارس', users: 1050, newUsers: 150 },
          { month: 'أبريل', users: 1180, newUsers: 130 },
          { month: 'مايو', users: 1220, newUsers: 85 },
          { month: 'يونيو', users: 1247, newUsers: 89 }
        ],
        usersByType: [
          { type: 'مستخدم شخصي', count: 687, percentage: 55.1 },
          { type: 'مالك شركة', count: 312, percentage: 25.0 },
          { type: 'مستخدم شركة', count: 198, percentage: 15.9 },
          { type: 'مدير', count: 50, percentage: 4.0 }
        ],
        topUsers: [
          {
            id: 1,
            name: 'أحمد محمد',
            email: '<EMAIL>',
            type: 'مالك شركة',
            lastActive: '2024-06-23',
            sessionsCount: 145,
            totalUsage: 89.5
          },
          {
            id: 2,
            name: 'فاطمة أحمد',
            email: '<EMAIL>',
            type: 'مستخدم شخصي',
            lastActive: '2024-06-23',
            sessionsCount: 132,
            totalUsage: 76.2
          },
          {
            id: 3,
            name: 'محمد علي',
            email: '<EMAIL>',
            type: 'مالك شركة',
            lastActive: '2024-06-22',
            sessionsCount: 98,
            totalUsage: 65.8
          },
          {
            id: 4,
            name: 'سارة خالد',
            email: '<EMAIL>',
            type: 'مستخدم شخصي',
            lastActive: '2024-06-22',
            sessionsCount: 87,
            totalUsage: 54.3
          }
        ],
        usagePatterns: [
          { hour: '00:00', users: 45 },
          { hour: '06:00', users: 78 },
          { hour: '09:00', users: 234 },
          { hour: '12:00', users: 456 },
          { hour: '15:00', users: 389 },
          { hour: '18:00', users: 567 },
          { hour: '21:00', users: 234 }
        ],
        conversionRates: {
          trialToSubscription: 23.5,
          freeToPersonal: 18.2,
          personalToBusiness: 12.8,
          businessToEnterprise: 8.5
        }
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getUserTypeColor = (type) => {
    switch (type) {
      case 'مدير': return 'error';
      case 'مالك شركة': return 'primary';
      case 'مستخدم شركة': return 'secondary';
      case 'مستخدم شخصي': return 'success';
      default: return 'default';
    }
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return hours > 0 ? `${hours}س ${mins}د` : `${mins}د`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والفلاتر */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          تحليلات المستخدمين
        </Typography>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>الفترة الزمنية</InputLabel>
          <Select
            value={selectedPeriod}
            label="الفترة الزمنية"
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <MenuItem value="week">أسبوع</MenuItem>
            <MenuItem value="month">شهر</MenuItem>
            <MenuItem value="quarter">ربع سنة</MenuItem>
            <MenuItem value="year">سنة</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* الإحصائيات الرئيسية */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <PeopleIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    إجمالي المستخدمين
                  </Typography>
                  <Typography variant="h6">
                    {analyticsData.overview.totalUsers.toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <PersonAddIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    مستخدمون جدد
                  </Typography>
                  <Typography variant="h6">
                    {analyticsData.overview.newUsers}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +12% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    مستخدمون نشطون
                  </Typography>
                  <Typography variant="h6">
                    {analyticsData.overview.activeUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {((analyticsData.overview.activeUsers / analyticsData.overview.totalUsers) * 100).toFixed(1)}%
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <ExitToAppIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    معدل التوقف
                  </Typography>
                  <Typography variant="h6">
                    {analyticsData.overview.churnRate}%
                  </Typography>
                  <Typography variant="body2" color="error.main">
                    -2% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <TimelineIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    معدل الاحتفاظ
                  </Typography>
                  <Typography variant="h6">
                    {analyticsData.overview.retentionRate}%
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +5% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                  <AnalyticsIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    متوسط الجلسة
                  </Typography>
                  <Typography variant="h6">
                    {formatDuration(analyticsData.overview.avgSessionDuration)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    +8% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="نمو المستخدمين" />
            <Tab label="أنواع المستخدمين" />
            <Tab label="أفضل المستخدمين" />
            <Tab label="معدلات التحويل" />
          </Tabs>
        </Box>

        <CardContent>
          {/* تبويب نمو المستخدمين */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                نمو المستخدمين خلال الأشهر الستة الماضية
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الشهر</TableCell>
                      <TableCell align="right">إجمالي المستخدمين</TableCell>
                      <TableCell align="right">مستخدمون جدد</TableCell>
                      <TableCell align="right">معدل النمو</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.userGrowth.map((row, index) => {
                      const growthRate = index > 0 ? 
                        (((row.users - analyticsData.userGrowth[index - 1].users) / analyticsData.userGrowth[index - 1].users) * 100).toFixed(1) : 
                        0;
                      return (
                        <TableRow key={row.month}>
                          <TableCell component="th" scope="row">
                            {row.month}
                          </TableCell>
                          <TableCell align="right">{row.users.toLocaleString()}</TableCell>
                          <TableCell align="right">{row.newUsers}</TableCell>
                          <TableCell align="right">
                            <Chip 
                              label={`${growthRate}%`}
                              color={growthRate > 0 ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* تبويب أنواع المستخدمين */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                توزيع المستخدمين حسب النوع
              </Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                {analyticsData.usersByType.map((userType) => (
                  <Grid item xs={12} sm={6} md={3} key={userType.type}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" color={getUserTypeColor(userType.type) + '.main'}>
                          {userType.count.toLocaleString()}
                        </Typography>
                        <Typography color="text.secondary" gutterBottom>
                          {userType.type}
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={userType.percentage} 
                          color={getUserTypeColor(userType.type)}
                          sx={{ mt: 1 }}
                        />
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          {userType.percentage}% من إجمالي المستخدمين
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* تبويب أفضل المستخدمين */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                أفضل المستخدمين نشاطاً
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>المستخدم</TableCell>
                      <TableCell>النوع</TableCell>
                      <TableCell align="right">عدد الجلسات</TableCell>
                      <TableCell align="right">إجمالي الاستخدام (ساعة)</TableCell>
                      <TableCell align="right">آخر نشاط</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analyticsData.topUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ mr: 2, bgcolor: getUserTypeColor(user.type) + '.main' }}>
                              {user.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {user.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {user.email}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={user.type}
                            color={getUserTypeColor(user.type)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">{user.sessionsCount}</TableCell>
                        <TableCell align="right">{user.totalUsage}</TableCell>
                        <TableCell align="right">{user.lastActive}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* تبويب معدلات التحويل */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                معدلات التحويل بين الباقات
              </Typography>
              <Grid container spacing={3} sx={{ mt: 1 }}>
                {Object.entries(analyticsData.conversionRates).map(([conversion, rate]) => (
                  <Grid item xs={12} sm={6} md={3} key={conversion}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h4" color="primary.main">
                          {rate}%
                        </Typography>
                        <Typography color="text.secondary">
                          {conversion === 'trialToSubscription' ? 'من التجربة للاشتراك' :
                           conversion === 'freeToPersonal' ? 'من المجاني للشخصي' :
                           conversion === 'personalToBusiness' ? 'من الشخصي للأعمال' :
                           conversion === 'businessToEnterprise' ? 'من الأعمال للمؤسسات' : conversion}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default UserAnalytics;
