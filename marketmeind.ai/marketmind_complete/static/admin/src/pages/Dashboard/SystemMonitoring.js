import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  LinearProgress,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Divider
} from '@mui/material';
import {
  Computer as ComputerIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  CloudQueue as CloudIcon,
  Security as SecurityIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Api as ApiIcon,
  Database as DatabaseIcon,
  Backup as BackupIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const SystemMonitoring = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [systemData, setSystemData] = useState({
    serverStatus: {},
    resourceUsage: {},
    apiEndpoints: [],
    databaseStatus: {},
    errorLogs: [],
    alerts: [],
    backupStatus: {},
    networkStatus: {}
  });

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchSystemData();
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemData = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات مراقبة النظام
      const mockData = {
        serverStatus: {
          webServer: { status: 'healthy', uptime: '15 يوم 8 ساعات', load: 45 },
          apiServer: { status: 'healthy', uptime: '15 يوم 8 ساعات', load: 62 },
          database: { status: 'healthy', uptime: '30 يوم 12 ساعة', load: 38 },
          redis: { status: 'warning', uptime: '2 يوم 4 ساعات', load: 85 }
        },
        resourceUsage: {
          cpu: { usage: 65, cores: 8, temperature: 58 },
          memory: { used: 12.5, total: 32, percentage: 39 },
          storage: { used: 450, total: 1000, percentage: 45 },
          network: { inbound: 125, outbound: 89, latency: 12 }
        },
        apiEndpoints: [
          { endpoint: '/api/auth/login', status: 'healthy', responseTime: 120, requests: 1250 },
          { endpoint: '/api/ai-marketing/customer-segmentation', status: 'healthy', responseTime: 850, requests: 89 },
          { endpoint: '/api/ai-marketing/campaign-optimization', status: 'healthy', responseTime: 920, requests: 67 },
          { endpoint: '/api/ai-marketing/personalized-content', status: 'warning', responseTime: 1200, requests: 45 },
          { endpoint: '/api/auth/profile', status: 'healthy', responseTime: 95, requests: 890 }
        ],
        databaseStatus: {
          connections: { active: 25, max: 100, percentage: 25 },
          queries: { slow: 3, total: 15420, avgTime: 45 },
          storage: { used: 2.8, total: 10, percentage: 28 },
          backup: { lastBackup: '2024-06-23 02:00', status: 'success', size: '1.2 GB' }
        },
        errorLogs: [
          {
            id: 1,
            level: 'error',
            message: 'Database connection timeout',
            timestamp: '2024-06-23 14:30:15',
            service: 'API Server'
          },
          {
            id: 2,
            level: 'warning',
            message: 'High memory usage detected',
            timestamp: '2024-06-23 14:25:30',
            service: 'Web Server'
          },
          {
            id: 3,
            level: 'info',
            message: 'Backup completed successfully',
            timestamp: '2024-06-23 02:00:00',
            service: 'Backup Service'
          },
          {
            id: 4,
            level: 'warning',
            message: 'API response time above threshold',
            timestamp: '2024-06-23 13:45:22',
            service: 'API Monitor'
          }
        ],
        alerts: [
          {
            id: 1,
            type: 'warning',
            title: 'استخدام عالي للذاكرة',
            message: 'استخدام الذاكرة وصل إلى 85% في خادم Redis',
            timestamp: '2024-06-23 14:30'
          },
          {
            id: 2,
            type: 'info',
            title: 'تحديث النظام',
            message: 'تم تطبيق تحديث أمني جديد بنجاح',
            timestamp: '2024-06-23 10:00'
          }
        ],
        backupStatus: {
          lastBackup: '2024-06-23 02:00:00',
          nextBackup: '2024-06-24 02:00:00',
          backupSize: '1.2 GB',
          status: 'success',
          retention: '30 يوم'
        },
        networkStatus: {
          bandwidth: { used: 45, total: 100, percentage: 45 },
          latency: 12,
          packetLoss: 0.1,
          connections: 1250
        }
      };

      setSystemData(mockData);
    } catch (error) {
      console.error('Error fetching system data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircleIcon color="success" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'error': return <ErrorIcon color="error" />;
      default: return <CheckCircleIcon />;
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'info': return 'info';
      default: return 'default';
    }
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 80) return 'error';
    if (percentage >= 60) return 'warning';
    return 'success';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان */}
      <Typography variant="h4" component="h1" gutterBottom>
        مراقبة النظام
      </Typography>

      {/* التنبيهات */}
      {systemData.alerts.length > 0 && (
        <Box mb={3}>
          {systemData.alerts.map((alert) => (
            <Alert key={alert.id} severity={alert.type} sx={{ mb: 1 }}>
              <AlertTitle>{alert.title}</AlertTitle>
              {alert.message} - {alert.timestamp}
            </Alert>
          ))}
        </Box>
      )}

      {/* حالة الخوادم */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة الخوادم
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(systemData.serverStatus).map(([server, data]) => (
                  <Grid item xs={12} sm={6} md={3} key={server}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box display="flex" alignItems="center" mb={1}>
                          {getStatusIcon(data.status)}
                          <Typography variant="h6" sx={{ ml: 1 }}>
                            {server === 'webServer' ? 'خادم الويب' :
                             server === 'apiServer' ? 'خادم API' :
                             server === 'database' ? 'قاعدة البيانات' :
                             server === 'redis' ? 'Redis' : server}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          وقت التشغيل: {data.uptime}
                        </Typography>
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2">
                            الحمولة: {data.load}%
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={data.load} 
                            color={getUsageColor(data.load)}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* استخدام الموارد */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <SpeedIcon />
                </Avatar>
                <Typography variant="h6">المعالج</Typography>
              </Box>
              <Typography variant="h4" color={getUsageColor(systemData.resourceUsage.cpu.usage) + '.main'}>
                {systemData.resourceUsage.cpu.usage}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {systemData.resourceUsage.cpu.cores} أنوية - {systemData.resourceUsage.cpu.temperature}°C
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={systemData.resourceUsage.cpu.usage} 
                color={getUsageColor(systemData.resourceUsage.cpu.usage)}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <MemoryIcon />
                </Avatar>
                <Typography variant="h6">الذاكرة</Typography>
              </Box>
              <Typography variant="h4" color={getUsageColor(systemData.resourceUsage.memory.percentage) + '.main'}>
                {systemData.resourceUsage.memory.percentage}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {systemData.resourceUsage.memory.used} GB / {systemData.resourceUsage.memory.total} GB
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={systemData.resourceUsage.memory.percentage} 
                color={getUsageColor(systemData.resourceUsage.memory.percentage)}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <StorageIcon />
                </Avatar>
                <Typography variant="h6">التخزين</Typography>
              </Box>
              <Typography variant="h4" color={getUsageColor(systemData.resourceUsage.storage.percentage) + '.main'}>
                {systemData.resourceUsage.storage.percentage}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {systemData.resourceUsage.storage.used} GB / {systemData.resourceUsage.storage.total} GB
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={systemData.resourceUsage.storage.percentage} 
                color={getUsageColor(systemData.resourceUsage.storage.percentage)}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <CloudIcon />
                </Avatar>
                <Typography variant="h6">الشبكة</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                {systemData.resourceUsage.network.latency}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                دخل: {systemData.resourceUsage.network.inbound} MB/s
              </Typography>
              <Typography variant="body2" color="text.secondary">
                خرج: {systemData.resourceUsage.network.outbound} MB/s
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* حالة API */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة API Endpoints
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Endpoint</TableCell>
                      <TableCell align="right">الحالة</TableCell>
                      <TableCell align="right">وقت الاستجابة</TableCell>
                      <TableCell align="right">الطلبات</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {systemData.apiEndpoints.map((endpoint) => (
                      <TableRow key={endpoint.endpoint}>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                            {endpoint.endpoint}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={endpoint.status === 'healthy' ? 'سليم' : 'تحذير'}
                            color={getStatusColor(endpoint.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">{endpoint.responseTime}ms</TableCell>
                        <TableCell align="right">{endpoint.requests}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* سجلات الأخطاء */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                سجلات النظام الأخيرة
              </Typography>
              <List>
                {systemData.errorLogs.map((log, index) => (
                  <React.Fragment key={log.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Chip 
                          label={log.level.toUpperCase()}
                          color={getLogLevelColor(log.level)}
                          size="small"
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={log.message}
                        secondary={`${log.service} - ${log.timestamp}`}
                      />
                    </ListItem>
                    {index < systemData.errorLogs.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemMonitoring;
