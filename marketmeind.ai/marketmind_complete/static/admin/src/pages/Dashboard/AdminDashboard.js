import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  LinearProgress
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Notifications as NotificationsIcon,
  Business as BusinessIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const AdminDashboard = () => {
  const { currentUser, accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalUsers: 0,
      activeUsers: 0,
      totalRevenue: 0,
      monthlyRevenue: 0,
      totalSubscriptions: 0,
      activeSubscriptions: 0,
      systemHealth: 'good'
    },
    recentActivities: [],
    alerts: [],
    systemStatus: {}
  });

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات لوحة التحكم
      const mockData = {
        stats: {
          totalUsers: 1247,
          activeUsers: 892,
          totalRevenue: 125000,
          monthlyRevenue: 18500,
          totalSubscriptions: 456,
          activeSubscriptions: 398,
          systemHealth: 'good'
        },
        recentActivities: [
          {
            id: 1,
            type: 'user_registration',
            message: 'مستخدم جديد سجل في النظام',
            user: 'أحمد محمد',
            timestamp: '2024-06-23 14:30',
            icon: 'person_add'
          },
          {
            id: 2,
            type: 'subscription',
            message: 'اشتراك جديد في الباقة المتميزة',
            user: 'فاطمة أحمد',
            timestamp: '2024-06-23 13:45',
            icon: 'payment'
          },
          {
            id: 3,
            type: 'system',
            message: 'تم تحديث النظام بنجاح',
            user: 'النظام',
            timestamp: '2024-06-23 12:00',
            icon: 'system_update'
          },
          {
            id: 4,
            type: 'analytics',
            message: 'تم إنشاء تقرير شهري جديد',
            user: 'النظام',
            timestamp: '2024-06-23 10:15',
            icon: 'analytics'
          }
        ],
        alerts: [
          {
            id: 1,
            type: 'warning',
            title: 'استخدام عالي للخادم',
            message: 'استخدام CPU وصل إلى 85%',
            timestamp: '2024-06-23 15:00'
          },
          {
            id: 2,
            type: 'info',
            title: 'تحديث متاح',
            message: 'يتوفر تحديث جديد للنظام',
            timestamp: '2024-06-23 09:30'
          }
        ],
        systemStatus: {
          database: 'healthy',
          api: 'healthy',
          storage: 'warning',
          backup: 'healthy'
        }
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircleIcon color="success" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'error': return <ErrorIcon color="error" />;
      default: return <CheckCircleIcon />;
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'user_registration': return <PeopleIcon />;
      case 'subscription': return <MoneyIcon />;
      case 'system': return <DashboardIcon />;
      case 'analytics': return <AnalyticsIcon />;
      default: return <NotificationsIcon />;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* ترحيب */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          مرحباً، {currentUser?.first_name || 'المدير'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          لوحة تحكم المدير - منصة MarketMind
        </Typography>
      </Box>

      {/* الإحصائيات الرئيسية */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <PeopleIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    إجمالي المستخدمين
                  </Typography>
                  <Typography variant="h5">
                    {dashboardData.stats.totalUsers.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {dashboardData.stats.activeUsers} نشط
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الإيرادات الشهرية
                  </Typography>
                  <Typography variant="h5">
                    {dashboardData.stats.monthlyRevenue.toLocaleString()} ر.س
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +12% من الشهر الماضي
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <BusinessIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الاشتراكات النشطة
                  </Typography>
                  <Typography variant="h5">
                    {dashboardData.stats.activeSubscriptions}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    من أصل {dashboardData.stats.totalSubscriptions}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    معدل النمو
                  </Typography>
                  <Typography variant="h5">
                    +24%
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    نمو ممتاز
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* النشاطات الأخيرة */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                النشاطات الأخيرة
              </Typography>
              <List>
                {dashboardData.recentActivities.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.light' }}>
                          {getActivityIcon(activity.type)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={activity.message}
                        secondary={`${activity.user} - ${activity.timestamp}`}
                      />
                    </ListItem>
                    {index < dashboardData.recentActivities.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* التنبيهات وحالة النظام */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={2}>
            {/* التنبيهات */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    التنبيهات
                  </Typography>
                  {dashboardData.alerts.map((alert) => (
                    <Alert 
                      key={alert.id} 
                      severity={alert.type} 
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="subtitle2">{alert.title}</Typography>
                      <Typography variant="body2">{alert.message}</Typography>
                    </Alert>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            {/* حالة النظام */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    حالة النظام
                  </Typography>
                  <Box>
                    {Object.entries(dashboardData.systemStatus).map(([service, status]) => (
                      <Box key={service} display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">
                          {service === 'database' ? 'قاعدة البيانات' :
                           service === 'api' ? 'واجهة البرمجة' :
                           service === 'storage' ? 'التخزين' :
                           service === 'backup' ? 'النسخ الاحتياطي' : service}
                        </Typography>
                        <Box display="flex" alignItems="center">
                          {getStatusIcon(status)}
                          <Chip 
                            label={status === 'healthy' ? 'سليم' : status === 'warning' ? 'تحذير' : 'خطأ'}
                            color={getStatusColor(status)}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;
