import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  GetApp as DownloadIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
  Email as EmailIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Business as BusinessIcon,
  Analytics as AnalyticsIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const UserReports = () => {
  const { accessToken } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [selectedReport, setSelectedReport] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [reportFilters, setReportFilters] = useState({
    dateRange: 'month',
    reportType: 'all',
    format: 'pdf'
  });

  const API_BASE = 'http://localhost:8082/api';

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    setLoading(true);
    try {
      // محاكاة بيانات التقارير
      const mockReports = [
        {
          id: 1,
          name: 'تقرير المستخدمين الشهري',
          type: 'users',
          description: 'تقرير شامل عن نشاط المستخدمين خلال الشهر',
          lastGenerated: '2024-06-23 10:00',
          frequency: 'monthly',
          status: 'completed',
          size: '2.5 MB',
          downloads: 45,
          format: 'PDF',
          recipients: ['<EMAIL>', '<EMAIL>']
        },
        {
          id: 2,
          name: 'تقرير الإيرادات الأسبوعي',
          type: 'financial',
          description: 'تحليل الإيرادات والمدفوعات الأسبوعية',
          lastGenerated: '2024-06-22 08:00',
          frequency: 'weekly',
          status: 'completed',
          size: '1.8 MB',
          downloads: 23,
          format: 'Excel',
          recipients: ['<EMAIL>']
        },
        {
          id: 3,
          name: 'تقرير أداء النظام اليومي',
          type: 'system',
          description: 'مراقبة أداء النظام والخوادم',
          lastGenerated: '2024-06-23 06:00',
          frequency: 'daily',
          status: 'completed',
          size: '850 KB',
          downloads: 12,
          format: 'PDF',
          recipients: ['<EMAIL>']
        },
        {
          id: 4,
          name: 'تقرير الاشتراكات الشهري',
          type: 'subscriptions',
          description: 'تحليل الاشتراكات والباقات',
          lastGenerated: '2024-06-20 14:30',
          frequency: 'monthly',
          status: 'pending',
          size: '-',
          downloads: 0,
          format: 'PDF',
          recipients: ['<EMAIL>']
        },
        {
          id: 5,
          name: 'تقرير تحليلات الاستخدام',
          type: 'analytics',
          description: 'تحليل أنماط استخدام المنصة',
          lastGenerated: '2024-06-21 16:45',
          frequency: 'weekly',
          status: 'completed',
          size: '3.2 MB',
          downloads: 67,
          format: 'Excel',
          recipients: ['<EMAIL>', '<EMAIL>']
        }
      ];

      setReports(mockReports);
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const openDialog = (type, report = null) => {
    setDialogType(type);
    setSelectedReport(report);
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
    setSelectedReport(null);
    setDialogType('');
  };

  const getReportTypeIcon = (type) => {
    switch (type) {
      case 'users': return <PeopleIcon />;
      case 'financial': return <MoneyIcon />;
      case 'system': return <AnalyticsIcon />;
      case 'subscriptions': return <BusinessIcon />;
      case 'analytics': return <TrendingUpIcon />;
      default: return <AssessmentIcon />;
    }
  };

  const getReportTypeText = (type) => {
    switch (type) {
      case 'users': return 'المستخدمين';
      case 'financial': return 'مالي';
      case 'system': return 'النظام';
      case 'subscriptions': return 'الاشتراكات';
      case 'analytics': return 'التحليلات';
      default: return type;
    }
  };

  const getReportTypeColor = (type) => {
    switch (type) {
      case 'users': return 'primary';
      case 'financial': return 'success';
      case 'system': return 'warning';
      case 'subscriptions': return 'info';
      case 'analytics': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'مكتمل';
      case 'pending': return 'في الانتظار';
      case 'failed': return 'فشل';
      default: return status;
    }
  };

  const getFrequencyText = (frequency) => {
    switch (frequency) {
      case 'daily': return 'يومي';
      case 'weekly': return 'أسبوعي';
      case 'monthly': return 'شهري';
      case 'quarterly': return 'ربع سنوي';
      case 'yearly': return 'سنوي';
      default: return frequency;
    }
  };

  const handleGenerateReport = () => {
    console.log('Generating report with filters:', reportFilters);
    closeDialog();
  };

  const handleDownloadReport = (reportId) => {
    console.log('Downloading report:', reportId);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان والإحصائيات */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          التقارير والتحليلات
        </Typography>
        
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <AssessmentIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      إجمالي التقارير
                    </Typography>
                    <Typography variant="h5">
                      {reports.length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <DownloadIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      التحميلات الشهرية
                    </Typography>
                    <Typography variant="h5">
                      {reports.reduce((sum, report) => sum + report.downloads, 0)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <ScheduleIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      التقارير المجدولة
                    </Typography>
                    <Typography variant="h5">
                      {reports.filter(r => r.frequency !== 'manual').length}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <EmailIcon />
                  </Avatar>
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      المشتركون
                    </Typography>
                    <Typography variant="h5">
                      {new Set(reports.flatMap(r => r.recipients)).size}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* أزرار الإجراءات */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<AssessmentIcon />}
                onClick={() => openDialog('generate')}
              >
                إنشاء تقرير جديد
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<ScheduleIcon />}
                onClick={() => openDialog('schedule')}
              >
                جدولة التقارير
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
              >
                فلترة التقارير
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<DownloadIcon />}
              >
                تحميل جميع التقارير
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* قائمة التقارير */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            التقارير المتاحة
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم التقرير</TableCell>
                  <TableCell>النوع</TableCell>
                  <TableCell>التكرار</TableCell>
                  <TableCell>آخر إنشاء</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الحجم</TableCell>
                  <TableCell>التحميلات</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {reports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2, bgcolor: getReportTypeColor(report.type) + '.main' }}>
                          {getReportTypeIcon(report.type)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {report.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {report.description}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getReportTypeText(report.type)}
                        color={getReportTypeColor(report.type)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{getFrequencyText(report.frequency)}</TableCell>
                    <TableCell>{report.lastGenerated}</TableCell>
                    <TableCell>
                      <Chip 
                        label={getStatusText(report.status)}
                        color={getStatusColor(report.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{report.size}</TableCell>
                    <TableCell>{report.downloads}</TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleDownloadReport(report.id)}
                        disabled={report.status !== 'completed'}
                      >
                        تحميل
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* الحوارات */}
      <Dialog open={dialogOpen} onClose={closeDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'generate' && 'إنشاء تقرير جديد'}
          {dialogType === 'schedule' && 'جدولة التقارير'}
        </DialogTitle>

        <DialogContent>
          {dialogType === 'generate' && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    إعدادات التقرير
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>نوع التقرير</InputLabel>
                    <Select
                      value={reportFilters.reportType}
                      label="نوع التقرير"
                      onChange={(e) => setReportFilters({...reportFilters, reportType: e.target.value})}
                    >
                      <MenuItem value="all">جميع التقارير</MenuItem>
                      <MenuItem value="users">تقرير المستخدمين</MenuItem>
                      <MenuItem value="financial">التقرير المالي</MenuItem>
                      <MenuItem value="system">تقرير النظام</MenuItem>
                      <MenuItem value="subscriptions">تقرير الاشتراكات</MenuItem>
                      <MenuItem value="analytics">تقرير التحليلات</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>الفترة الزمنية</InputLabel>
                    <Select
                      value={reportFilters.dateRange}
                      label="الفترة الزمنية"
                      onChange={(e) => setReportFilters({...reportFilters, dateRange: e.target.value})}
                    >
                      <MenuItem value="week">الأسبوع الماضي</MenuItem>
                      <MenuItem value="month">الشهر الماضي</MenuItem>
                      <MenuItem value="quarter">الربع الماضي</MenuItem>
                      <MenuItem value="year">السنة الماضية</MenuItem>
                      <MenuItem value="custom">فترة مخصصة</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="من تاريخ"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="إلى تاريخ"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>تنسيق التقرير</InputLabel>
                    <Select
                      value={reportFilters.format}
                      label="تنسيق التقرير"
                      onChange={(e) => setReportFilters({...reportFilters, format: e.target.value})}
                    >
                      <MenuItem value="pdf">PDF</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="csv">CSV</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="البريد الإلكتروني للإرسال (اختياري)"
                    type="email"
                    placeholder="<EMAIL>"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    المحتويات المطلوبة:
                  </Typography>
                  <Box>
                    <Chip label="الإحصائيات العامة" sx={{ m: 0.5 }} />
                    <Chip label="الرسوم البيانية" sx={{ m: 0.5 }} />
                    <Chip label="الجداول التفصيلية" sx={{ m: 0.5 }} />
                    <Chip label="التوصيات" sx={{ m: 0.5 }} />
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}

          {dialogType === 'schedule' && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                جدولة التقارير التلقائية
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>نوع التقرير</InputLabel>
                    <Select defaultValue="users">
                      <MenuItem value="users">تقرير المستخدمين</MenuItem>
                      <MenuItem value="financial">التقرير المالي</MenuItem>
                      <MenuItem value="system">تقرير النظام</MenuItem>
                      <MenuItem value="subscriptions">تقرير الاشتراكات</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>التكرار</InputLabel>
                    <Select defaultValue="monthly">
                      <MenuItem value="daily">يومي</MenuItem>
                      <MenuItem value="weekly">أسبوعي</MenuItem>
                      <MenuItem value="monthly">شهري</MenuItem>
                      <MenuItem value="quarterly">ربع سنوي</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="وقت الإرسال"
                    type="time"
                    defaultValue="08:00"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <InputLabel>يوم الإرسال</InputLabel>
                    <Select defaultValue="1">
                      <MenuItem value="1">الأول من الشهر</MenuItem>
                      <MenuItem value="15">الخامس عشر من الشهر</MenuItem>
                      <MenuItem value="last">آخر يوم في الشهر</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="قائمة المستلمين"
                    placeholder="<EMAIL>, <EMAIL>"
                    multiline
                    rows={3}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    التقارير المجدولة الحالية:
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <ScheduleIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="تقرير المستخدمين الشهري"
                        secondary="كل أول يوم من الشهر في الساعة 8:00 صباحاً"
                      />
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <ListItemIcon>
                        <ScheduleIcon color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary="التقرير المالي الأسبوعي"
                        secondary="كل يوم اثنين في الساعة 9:00 صباحاً"
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={closeDialog}>إلغاء</Button>
          {dialogType === 'generate' && (
            <Button variant="contained" onClick={handleGenerateReport}>
              إنشاء التقرير
            </Button>
          )}
          {dialogType === 'schedule' && (
            <Button variant="contained">
              حفظ الجدولة
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserReports;
