import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// صفحات لوحة التحكم
import AdminDashboard from './pages/Dashboard/AdminDashboard';
import UserAnalytics from './pages/Dashboard/UserAnalytics';
import FinancialAnalytics from './pages/Dashboard/FinancialAnalytics';
import SystemMonitoring from './pages/Dashboard/SystemMonitoring';
import UserManagement from './pages/Users/<USER>';
import TeamManagement from './pages/Teams/TeamManagement';
import SubscriptionManagement from './pages/Subscriptions/SubscriptionManagement';
import UserReports from './pages/Reports/UserReports';
import AdminLogin from './pages/Auth/AdminLogin';

// مكون للتحقق من المصادقة
const PrivateRoute = ({ children }) => {
  const { currentUser, loading } = useAuth();
  
  if (loading) {
    return <div>جاري التحميل...</div>;
  }
  
  // التحقق من وجود مستخدم وأن نوع الحساب هو 'admin'
  if (!currentUser || currentUser.account_type !== 'admin') {
    return <Navigate to="/login" />;
  }
  
  return children;
};

// إنشاء السمة
const theme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<AdminLogin />} />
            <Route path="/" element={
              <PrivateRoute>
                <AdminDashboard />
              </PrivateRoute>
            } />
            <Route path="/dashboard" element={
              <PrivateRoute>
                <AdminDashboard />
              </PrivateRoute>
            } />
            <Route path="/dashboard/user-analytics" element={
              <PrivateRoute>
                <UserAnalytics />
              </PrivateRoute>
            } />
            <Route path="/dashboard/financial-analytics" element={
              <PrivateRoute>
                <FinancialAnalytics />
              </PrivateRoute>
            } />
            <Route path="/dashboard/system-monitoring" element={
              <PrivateRoute>
                <SystemMonitoring />
              </PrivateRoute>
            } />
            <Route path="/users" element={
              <PrivateRoute>
                <UserManagement />
              </PrivateRoute>
            } />
            <Route path="/teams" element={
              <PrivateRoute>
                <TeamManagement />
              </PrivateRoute>
            } />
            <Route path="/subscriptions" element={
              <PrivateRoute>
                <SubscriptionManagement />
              </PrivateRoute>
            } />
            <Route path="/reports" element={
              <PrivateRoute>
                <UserReports />
              </PrivateRoute>
            } />
            <Route path="*" element={<Navigate to="/" />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;